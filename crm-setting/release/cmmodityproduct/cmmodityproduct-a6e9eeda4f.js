function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",i=e.toStringTag||"@@toStringTag";function l(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{l({},"")}catch(s){l=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i,o,a,u,e=e&&e.prototype instanceof d?e:d,e=Object.create(e.prototype);return l(e,"_invoke",(i=t,o=n,a=new w(r||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:s,done:!0}}for(a.method=t,a.arg=e;;){var n=a.delegate;if(n){n=function t(e,n){var r=n.method,i=e.i[r];if(i===s)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=s,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;r=p(i,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,f;i=r.arg;return i?i.done?(n[e.r]=i.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,f):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}(n,a);if(n){if(n===f)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;n=p(i,o,a);if("normal"===n.type){if(u=a.done?4:2,n.arg===f)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(u=4,a.method="throw",a.arg=n.arg)}}),!0),e}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var f={};function d(){}function o(){}function _(){}var e={},h=(l(e,r,function(){return this}),Object.getPrototypeOf),h=h&&h(h(k([]))),m=(h&&h!==t&&c.call(h,r)&&(e=h),_.prototype=d.prototype=Object.create(e));function y(t){["next","throw","return"].forEach(function(e){l(t,e,function(t){return this._invoke(e,t)})})}function g(a,u){var e;l(this,"_invoke",function(n,r){function t(){return new u(function(t,e){!function e(t,n,r,i){var o,t=p(a[t],a,n);if("throw"!==t.type)return(n=(o=t.arg).value)&&"object"==_typeof(n)&&c.call(n,"__await")?u.resolve(n.__await).then(function(t){e("next",t,r,i)},function(t){e("throw",t,r,i)}):u.resolve(n).then(function(t){o.value=t,r(o)},function(t){return e("throw",t,r,i)});i(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function v(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function w(t){this.tryEntries=[[-1]],t.forEach(v,this),this.reset(!0)}function k(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(c.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return l(m,"constructor",o.prototype=_),l(_,"constructor",o),o.displayName=l(_,i,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,l(t,i,"GeneratorFunction")),t.prototype=Object.create(m),t},a.awrap=function(t){return{__await:t}},y(g.prototype),l(g.prototype,n,function(){return this}),a.AsyncIterator=g,a.async=function(t,e,n,r,i){void 0===i&&(i=Promise);var o=new g(u(t,e,n,r),i);return a.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},y(m),l(m,i,"Generator"),l(m,r,function(){return this}),l(m,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=k,w.prototype={constructor:w,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){o.type="throw",o.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var i=this.tryEntries[r],o=i[4],a=this.prev,u=i[1],c=i[2];if(-1===i[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=a){if(a<u)return this.method="next",this.arg=s,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}var o=(i=i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]?null:i)?i[4]:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i[2],f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),b(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,i=this.tryEntries[e];if(i[0]===t)return"throw"===(n=i[4]).type&&(r=n.arg,b(i)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:k(t),r:e,n:n},"next"===this.method&&(this.arg=s),f}},a}function asyncGeneratorStep(t,e,n,r,i,o,a){try{var u=t[o](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(e,n){var r=u.apply(t,a);function i(t){asyncGeneratorStep(r,e,n,i,o,"next",t)}function o(t){asyncGeneratorStep(r,e,n,i,o,"throw",t)}i(void 0)})}}function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var n;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(n="Object"===(n={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function _iterableToArrayLimit(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,u=[],c=!0,s=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){s=!0,i=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return u}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/cmmodityproduct/cmmodityproduct",["./data","crm-modules/common/util"],function(n,t,e){var r=n("./data"),f=n("crm-modules/common/util"),i=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.init()},init:function(t){var e=this;n.async("vcrm/sdk",function(t){t.getComponent("backstage").then(function(t){e.initView(t.default,r)})})},initView:function(t,e){new Vue({el:this.$el[0],template:'<div style=\'height:100%\'>\n                                <Backstage ref="backstage" :data="dataList" @change="change"></Backstage>\n                          </div>',components:{Backstage:t},data:function(){return{dataList:e,modelValues:{is_open_quoter:{cache_key:"openQuoter",value:!1},is_open_attribute:{cache_key:"openAttribute",value:!1},is_open_nonstandard_attribute:{cache_key:"openNsAttribute",value:!1},multiple_unit:{cache_key:"multiunitStatus",value:!1},multi_unit_price_book:{cache_key:"multi_unit_price_book",value:!1},simple_cpq:{cache_key:"fixedCollocationOpenStatus",value:!1},spu:{cache_key:"productOpenSpu",value:!1},saveConfig:{cache_key:"spuStatus",value:!1},bom_price_calculation_configuration:{cache_key:"bom_price_calculation_configuration",value:"0"},multi_spec_display_style:{cache_key:"multi_spec_display_style",value:""},is_open_incremental_pricing:{cache_key:"is_open_incremental_pricing",value:"0"},close_old_category:{cache_key:"close_old_category",value:"0"},periodic_product:{cache_key:"periodic_product",value:!1},periodic_product_plugin:{cache_key:"periodic_product_plugin",value:"[]"},cpq_ui_mode:{cache_key:"cpq_ui_mode",value:"0"},non_standard_product:{value:!1},change_product_type_refresh_price:{value:!1}},licenses:{}}},watch:{},computed:{},created:function(){},mounted:function(){this.init()},methods:{getLisenses:function(t){return new Promise(function(e){CRM.api.get_licenses({key:t,cb:function(t){e(t)}})})},getDataByFilter:function(t,e,n,r){var i=this;t&&e&&n&&t.forEach(function(t){e(t)&&(n&&n(t),r)||(t=t.moduleList||t.children)&&i.getDataByFilter(t,e,n)})},isShowFixedCollocation:function(){return new Promise(function(e){CRM.util.getConfigValue("price_policy").then(function(t){return e("1"===t&&CRM.util.isGrayScale("CRM_FIXED_COLLOCATION"))})})},init:function(){var e=this,t=this;this.getLisenses(["cpq_attribute_app","cpq_subscription_product_management_app"]).then(function(t){e.licenses=t,e.dataList.forEach(function(t){"attributeConfig"==t.moduleId&&(t.visible=e.licenses.cpq_attribute_app),"productCyclical"==t.moduleId&&(t.visible=e.licenses.cpq_subscription_product_management_app)})}),t.isShowFixedCollocation().then(function(e){t.dataList.forEach(function(t){"simple_cpq"==t.moduleList[0].key&&(t.moduleList[0].isShow=e,t.visible=e)})}),CRM.util.getproductWithSpuConfig().done(function(e){t.dataList.forEach(function(t){t.moduleList[1]&&"saveConfig"==t.moduleList[1].key&&(t.moduleList[1].isShow=e,t.moduleList[2].isShow=e)})}),t.isShowPeriodicProduct().then(function(e){t.dataList.forEach(function(t){t=t.moduleList.find(function(t){return"periodic_product"===t.key});t&&(t.children[0].options=t.children[0].options.filter(function(t){return"NewOpportunityObj"===t.key?"open"==e.config_newopportunity_open:"SaleContractObj"!==t.key||"1"==e.sale_contract}))})}),t.getConfig()},getSpuConfig:function(){return new Promise(function(n,r){f.FHHApi({url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/choose_spu",success:function(t){var e;0===t.Result.StatusCode?n(null==(e=t.Value)?void 0:e.result):r(t.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:""})})},getConfig:function(){var i=this,t=(t=Object.keys(this.modelValues)).filter(function(t){return"saveConfig"!==t});CRM.util.showLoading_tip(),Promise.all([this.getSpuConfig(),CRM.util.getConfigValues(t)]).then(function(t){var t=_slicedToArray(t,2),e=t[0],t=t[1];CRM.util.hideLoading_tip(),t.unshift({key:"saveConfig",value:e}),t&&t.forEach(function(t){var e=t.key,t=t.value,n=i.modelValues[e].cache_key,r=null;"saveConfig"===e?r="true"==t:["multi_spec_display_style","bom_price_calculation_configuration","cpq_ui_mode"].includes(e)?(r=t,"multi_spec_display_style"===e&&(r=r||"capsule")):r=["periodic_product_plugin"].includes(e)?t:"1"===t,n&&(CRM._cache[n]=r),i.$set(i.modelValues[e],"value",r),i.$set(i.modelValues[e],"originalValue",t)}),i.initDataList()}).catch(function(t){CRM.util.hideLoading_tip(),CRM.util.remind(3,t||$t("操作失败!"))})},isShowPeriodicProduct:function(){return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,CRM.util.getConfigValues(["config_newopportunity_open","sale_contract"]);case 2:return e=t.sent,t.abrupt("return",(e||[]).reduce(function(t,e){return t[e.key]=e.value,t},{}));case 4:case"end":return t.stop()}},t)}))()},initDataList:function(){var o=this;this.getDataByFilter(this.dataList,function(t){return!!t.key||!!t.moduleId},function(t){var e,n,r,i;t.moduleId?("mobileMultiUnitConfig"===t.moduleId&&(n=o.modelValues.multiple_unit.value,o.$set(t,"visible",n)),"cpqUIMode"===t.moduleId&&(n=o.modelValues.is_open_attribute.value||CRM._cache.cpqStatus,o.$set(t,"visible",n))):(n=null,(e=t.key)instanceof Array?n=e.map(function(t){return o.modelValues[t].value}):o.modelValues[e]&&(n=o.modelValues[e].value),["periodic_product_plugin"].includes(e)&&(n=JSON.parse(o.modelValues[e].value||"[]")),"close_old_category"===e?(i="2"==(r=o.modelValues[e].originalValue)?$t("sfa.crm.setting_cmmodityproduct.product_category_opening_status"):$t("crm.setting.tradeconfigure.warn_cannot_closed"),o.$set(t,"extraStatus",{statusInfo:i}),n="0"!==r):"simple_cpq"===e&&n?o.dataList.forEach(function(t){"simple_cpq"==t.moduleList[0].key&&(t.moduleList[0].children[0].isShow=!0)}):"periodic_product_plugin"===e&&o.$set(t,"isShow",o.modelValues.periodic_product.value),o.$set(t,"value",n))})},change:function(a,u){var c=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e,n,r,i,o;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=a.type,n=a.key,r=a.value,i=a.values,"switch"!==e&&"checkbox"!==e||(r=r?"1":"0"),t.next=4,c.beforeSetConfig({type:e,key:n,value:r,values:i},u);case 4:if(o=t.sent){t.next=7;break}return t.abrupt("return");case 7:o.confirmInfo?c.confirm=f.confirm(o.confirmInfo,$t("提示"),c.setConfig.bind(null,o,a)):c.setConfig(o,a);case 8:case"end":return t.stop()}},t)}))()},beforeSetConfig:function(t,a){var u=this,c=(t.type,t.key),s=t.value;return new Promise(function(e,t){if(c){var n,r={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:c,value:s}},i=a.confirmInfo?_.isFunction(a.confirmInfo)?a.confirmInfo():a.confirmInfo:null;if(r.confirmInfo=i,"saveConfig"==c&&(r={url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/save_spu_selector_config",data:{key:c,value:!CRM._cache.spuStatus,saveConfig:!CRM._cache.spuStatus},confirmInfo:CRM._cache.spuStatus?$t("crm.确定关闭选产品吗"):$t("crm.确定启用选产品吗")}),"spu"===c&&(r.confirmInfo=CRM._cache.productOpenSpu?$t("确认关闭商品设置吗"):$t("确认开启商品设置吗"),e(r)),"multiple_unit"===c&&(r=o(c,s)),"multi_unit_price_book"===c){if(!CRM._cache.multiunitStatus)return void f.alert($t("crm.multiunitStatus.open"));r=o(c,s)}"simple_cpq"===c&&(r=o(c,s)),"non_standard_product"===c&&(r=o(c,s,i)),"is_open_attribute"===c||"is_open_nonstandard_attribute"===c?u.isAttributeAllowed().done(function(t){t.value?("is_open_attribute"===c&&(r.confirmInfo=$t("开启当前开关时，请先开启[价目表开关].注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]")),"is_open_nonstandard_attribute"===c&&(r.confirmInfo=$t("开启当前开关时，请先开启[价目表开关].注意：开启非标属性后，将不能开启[商品设置][多单位设置][促销设置]")),e(r)):CRM.util.alert(t.info)}):"is_open_quoter"!==c||"1"!==s||CRM._cache.openAttribute?("periodic_product_plugin"===c&&(n=JSON.parse(u.modelValues[c].value),r.data.value=JSON.stringify(r.data.value.filter(function(t){return!n.includes(t)}))),e(r)):f.alert($t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")}))}function o(t,e,n){var r={multiple_unit:$t("确认开启多单位吗"),multi_unit_price_book:$t("确认开启多单位定价吗"),simple_cpq:$t("确认开启固定搭配")};return{url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{tenantId:CRM.enterpriseId,moduleCode:t,openStatus:e},confirmInfo:n||r[t]}}})},isAttributeAllowed:function(){return new Promise(function(u,t){CRM.util.showLoading_tip(),CRM.util.getConfigValues(["28","spu","multiple_unit","promotion_status"]).then(function(t){CRM.util.hideLoading_tip();var e=$t("crm.propertiesOpenFailedTitle"),n=$t("crm.propertiesOpenFailedInfo"),r=!1,i=!1,o=!1,a=!1,t=(t.forEach(function(t){"promotion_status"===t.key&&(a="2"==t.value),"28"===t.key&&(r="1"==t.value),"spu"===t.key&&(i="1"==t.value),"multiple_unit"===t.key&&(o="1"==t.value)}),r&&!i&&!o&&!a);r?(i&&(n+="[".concat($t("商品设置"),"]")),o&&(n+=(i?$t("和"):"")+"[".concat($t("多单位设置"),"]")),a&&(n+=(!i||o?$t("和"):"")+"[".concat($t("促销设置"),"]"))):(e=$t("crm.setting.pricebookStatus",null,"开启当前开关时，请先开启[价目表开关] "),n=$t("crm.setting.pricebookStatus_tip",null,"注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]")),u({value:t,info:e+"</br>"+n})})}).catch(function(){CRM.util.hideLoading_tip()})},setConfig:function(r,t){var s=this,l=t.type,p=t.values;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var i,e,n,o,a,u,c;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:i=s,e=r.data,n=e.key,o=e.moduleCode,a=e.value,u=e.openStatus,c=n||o,CRM.util.waiting(),s.$refs.backstage.commonSetConfig({url:r.url,data:r.data,complete:function(){var t;null!=(t=i.confirm)&&t.hide()}}).then(function(t){CRM.util.waiting(!1);var e,n,r;!["multiple_unit","multi_unit_price_book","simple_cpq"].includes(c)||t.Value.success&&0===t.Value.errCode?(e=null,n=s.modelValues[c].cache_key,r="switch"===l&&"[object Object]"===Object.prototype.toString.call(t.Value)&&_.isEmpty(t.Value),["is_open_attribute","is_open_nonstandard_attribute","spu","is_open_quoter","close_old_category","saveConfig"].includes(c)||r?(e=o?"1"===u:"1"===a,"spu"==c?i.init():"close_old_category"==c?s.getConfig():"saveConfig"===c&&i.dataList.forEach(function(t){t.moduleList[2]&&"mobilemultispec"==t.moduleList[2].type&&(t.moduleList[2].isVis=e)})):e="simple_cpq"==c?(i.dataList.forEach(function(t){"simple_cpq"==t.moduleList[0].key&&(t.moduleList[0].children[0].isShow=!0)}),"1"===t.Value.value.openStatus):"bom_price_calculation_configuration"==c?(i.dataList.forEach(function(t){"simple_cpq"==t.moduleList[0].key&&(t.moduleList[0].children[0].value=a)}),a):"is_open_incremental_pricing"===c?"1"===a:["multi_spec_display_style","cpq_ui_mode"].includes(c)?a:["periodic_product_plugin"].includes(c)?JSON.stringify(p):"1"===t.Value.value.openStatus,s.$set(s.modelValues[c],"value",e),n&&(CRM._cache[n]=e),s.initDataList(),f.remind(1,$t("操作成功"))):f.alert(t.Value.errMessage||$t("操作失败!"))}).catch(function(t){CRM.util.waiting(!1),f.alert(t||$t("操作失败!"))});case 5:case"end":return t.stop()}},t)}))()}}})},destroy:function(){}});e.exports=i});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return l};var a,l={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",i=e.asyncIterator||"@@asyncIterator",n=e.toStringTag||"@@toStringTag";function u(t,e,i,r){return Object.defineProperty(t,e,{value:i,enumerable:!r,configurable:!r,writable:!r})}try{u({},"")}catch(a){u=function(t,e,i){return t[e]=i}}function s(t,e,i,r){var n,o,l,s,e=e&&e.prototype instanceof m?e:m,e=Object.create(e.prototype);return u(e,"_invoke",(n=t,o=i,l=new v(r||[]),s=1,function(t,e){if(3===s)throw Error("Generator is already running");if(4===s){if("throw"===t)throw e;return{value:a,done:!0}}for(l.method=t,l.arg=e;;){var i=l.delegate;if(i){i=function t(e,i){var r=i.method,n=e.i[r];if(n===a)return i.delegate=null,"throw"===r&&e.i.return&&(i.method="return",i.arg=a,t(e,i),"throw"===i.method)||"return"!==r&&(i.method="throw",i.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;r=d(n,e.i,i.arg);if("throw"===r.type)return i.method="throw",i.arg=r.arg,i.delegate=null,p;n=r.arg;return n?n.done?(i[e.r]=n.value,i.next=e.n,"return"!==i.method&&(i.method="next",i.arg=a),i.delegate=null,p):n:(i.method="throw",i.arg=new TypeError("iterator result is not an object"),i.delegate=null,p)}(i,l);if(i){if(i===p)continue;return i}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(1===s)throw s=4,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);s=3;i=d(n,o,l);if("normal"===i.type){if(s=l.done?4:2,i.arg===p)continue;return{value:i.arg,done:l.done}}"throw"===i.type&&(s=4,l.method="throw",l.arg=i.arg)}}),!0),e}function d(t,e,i){try{return{type:"normal",arg:t.call(e,i)}}catch(t){return{type:"throw",arg:t}}}l.wrap=s;var p={};function m(){}function o(){}function f(){}var e={},y=(u(e,r,function(){return this}),Object.getPrototypeOf),y=y&&y(y(w([]))),_=(y&&y!==t&&c.call(y,r)&&(e=y),f.prototype=m.prototype=Object.create(e));function h(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function g(l,s){var e;u(this,"_invoke",function(i,r){function t(){return new s(function(t,e){!function e(t,i,r,n){var o,t=d(l[t],l,i);if("throw"!==t.type)return(i=(o=t.arg).value)&&"object"==_typeof(i)&&c.call(i,"__await")?s.resolve(i.__await).then(function(t){e("next",t,r,n)},function(t){e("throw",t,r,n)}):s.resolve(i).then(function(t){o.value=t,r(o)},function(t){return e("throw",t,r,n)});n(t.arg)}(i,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function $(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=a,t[4]=e}function v(t){this.tryEntries=[[-1]],t.forEach($,this),this.reset(!0)}function w(e){if(null!=e){var i,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return i=-1,(t=function t(){for(;++i<e.length;)if(c.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=a,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return u(_,"constructor",o.prototype=f),u(f,"constructor",o),o.displayName=u(f,n,"GeneratorFunction"),l.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},l.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,u(t,n,"GeneratorFunction")),t.prototype=Object.create(_),t},l.awrap=function(t){return{__await:t}},h(g.prototype),u(g.prototype,i,function(){return this}),l.AsyncIterator=g,l.async=function(t,e,i,r,n){void 0===n&&(n=Promise);var o=new g(s(t,e,i,r),n);return l.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},h(_),u(_,n,"Generator"),u(_,r,function(){return this}),u(_,"toString",function(){return"[object Generator]"}),l.keys=function(t){var e,i=Object(t),r=[];for(e in i)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in i)return t.value=e,t.done=!1,t;return t.done=!0,t}},l.values=w,v.prototype={constructor:v,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=a,this.done=!1,this.delegate=null,this.method="next",this.arg=a,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=a)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var i=this;function t(t){o.type="throw",o.arg=e,i.next=t}for(var r=i.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r],o=n[4],l=this.prev,s=n[1],c=n[2];if(-1===n[0])return t("end"),!1;if(!s&&!c)throw Error("try statement without catch or finally");if(null!=n[0]&&n[0]<=l){if(l<s)return this.method="next",this.arg=a,t(s),!0;if(l<c)return t(c),!1}}},abrupt:function(t,e){for(var i=this.tryEntries.length-1;0<=i;--i){var r=this.tryEntries[i];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var n=r;break}}var o=(n=n&&("break"===t||"continue"===t)&&n[0]<=e&&e<=n[2]?null:n)?n[4]:{};return o.type=t,o.arg=e,n?(this.method="next",this.next=n[2],p):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var i=this.tryEntries[e];if(i[2]===t)return this.complete(i[4],i[3]),b(i),p}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var i,r,n=this.tryEntries[e];if(n[0]===t)return"throw"===(i=n[4]).type&&(r=i.arg,b(n)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,i){return this.delegate={i:w(t),r:e,n:i},"next"===this.method&&(this.arg=a),p}},l}function asyncGeneratorStep(t,e,i,r,n,o,l){try{var s=t[o](l),c=s.value}catch(t){return void i(t)}s.done?e(c):Promise.resolve(c).then(r,n)}function _asyncToGenerator(s){return function(){var t=this,l=arguments;return new Promise(function(e,i){var r=s.apply(t,l);function n(t){asyncGeneratorStep(r,e,i,n,o,"next",t)}function o(t){asyncGeneratorStep(r,e,i,n,o,"throw",t)}n(void 0)})}}define("crm-setting/cmmodityproduct/data",[],function(t,e,i){var r,n=[{domId:"level0",moduleId:"multipleUnit",title:$t("多单位配置"),moduleList:[{type:"switch",title:$t("多单位开启开关"),key:"multiple_unit",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("多单位设置说明1"),list:[]},{title:$t("多单位设置说明2"),list:[]},{title:$t("多单位设置说明3"),list:[]},{title:$t("单位性质"),list:[$t("不支持自定义选项值"),$t("根据单位性质指定赠品单位")]},{title:$t("多单位设置说明4"),list:[]},{title:$t("多单位设置说明5"),list:[]},{title:$t("多单位设置说明6"),list:[]}]},{type:"switch",title:$t("多单位定价开启开关"),key:"multi_unit_price_book",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("多单位定价说明1"),list:[]},{title:$t("多单位定价说明2"),list:[]},{title:$t("多单位定价说明3"),list:[$t("多单位定价说明3-1"),$t("多单位定价说明3-2")]},{title:$t("多单位定价说明4"),list:[]}]}]},{domId:"level1",title:$t("多单位模式配置"),visible:CRM._cache.multiunitStatus,moduleId:"mobileMultiUnitConfig",isChange:!1,moduleList:[{type:"MobileMulticonfig",title:$t("crm.MobileMulticonfig.modeSelection"),value:"0",_id:"multi_unit_mode_config",key:"multi_unit_mode_config",describeList:[{title:$t("多单位模式配置1"),list:[{title:$t("多单位模式配置1.1")},{title:$t("多单位模式配置1.2")}]},{title:$t("多单位模式配置2"),list:[{title:$t("多单位模式配置2.1")}]},{title:$t("多单位模式配置3")}]},{type:"MobileMultUnitConfig",title:$t("单位显示样式"),key:"multi_unit_show_type",describeList:[{title:$t("单位显示样式1"),list:[{title:$t("单位显示样式1.1")}]},{title:$t("下拉展示")+":",list:[{title:$t("下拉展示1.1")},{title:$t("下拉展示1.2")},{title:$t("下拉展示1.3")}]},{title:$t("单位显示样式3")}]}]},{domId:"level2",moduleId:"spuConfig",title:$t("商品配置"),moduleList:[{type:"switch",title:$t("商品配置开启开关"),key:"spu",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!0,render:null,radioOptions:[],describeList:[{title:$t("开关默认关闭，商品、规格&规格值对象隐藏，直接在产品对象下维护产品数，且选择产品页面，无选择商品页面，直接选择产品，即开关“选择产品设置”无法打开"),list:[]},{title:$t("此开关可以从默认的关闭状态修改为开启状态，但开启后不可再关闭"),list:[]},{title:$t("线上企业，如不存在多规格产品，可以关闭此开关，系统会将商品上的字段“标签（原商品标签）、是否多单位、批次与序列号管理“的值自动赋值到产品对象，但关闭后，不可再开启"),list:[]},{title:$t("线上企业，如关闭此开关，系统会判断商品上是否存在自定义字段，商品对象被查找关联，字段被引用等，均需要调整现有关联到所需产品对象之后，才可关闭，系统不做自动处理"),list:[]},{title:$t("如企业有OpenAPI的对接，请做好接口的升级后，开启或关闭此开关"),list:[]}]},{type:"switch",title:$t("基于商品选择产品"),key:"saveConfig",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],describeList:[{title:$t("crm.选商品产品开关描述"),list:[]},{title:$t("开启该开关后，移动端支持选择产品样式配置"),list:[]},{title:$t("crm.该开关仅作用"),list:[]},{title:$t("此开关可根据业务需要反复开启和关闭。"),list:[]},{title:$t("请注意: 移动端使用该功能，需升级至6.5以上版本"),list:[]}]},{type:"mobilemultispec",title:$t("移动端多规格商品选择产品样式配置"),key:"multi_spec_display_style",value:"",isVis:CRM._cache.spuStatus,isDisabled:!1,isShow:!1,radioOptions:[],describeList:[]}]},{domId:"level3",moduleId:"attributeConfig",title:$t("属性配置"),visible:!1,licenseKey:"cpq_attribute_app",moduleList:[{type:"switch",title:$t("属性配置开启开关"),key:"is_open_attribute",value:!1,displayCount:2,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],describeList:[{title:$t("该开关一旦开启，不可关闭。")},{title:$t("开启当前开关时，请先开启 [ 价目表开关 ]。"),list:[function(t){return[t("div",{class:"crm-intro-warning"},[$t("注：开启属性后，将不能开启 [ 商品设置 ] [ 多单位设置 ] [ 促销设置 ]")])]}]},{title:$t("开关开启：通过属性属性值关联产品，供报价或下单时使用。属性有启用/禁用，关联产品等功能，实现产品业务差异化"),list:[$t("[ 属性 ]、[ 产品属性价目表 ] 对象显示"),function(t){return[t("div",{style:{padding:"0 10px"}},$t("注：同时订单产品、报价单明细：显示 [ 属性 ]、[ 产品属性价目表名称 ]"))]},$t("[订单 ]、[报价单 ] “从历史订单”入口屏蔽"),function(t){var e={padding:"0 10px"};return[t("li",{},"3.3 ".concat($t("[ 产品属性价目表 ] 使用"))),t("li",{style:e},"3.3.1 ".concat($t("销售订单、报价单中，产品适配的价目表带出规则不变（默认带出客户的最优可售范围中产品的最优的价目表）"))),t("li",{style:e},"3.3.2 ".concat($t("影响点：产品、订单产品、报价单明细中，产品价格以及折扣的取值"))),t("li",{style:e},"a、 ".concat($t("开启的[ 产品属性价目表 ] 适配某价目表后， [ 产品属性选择后 ] 价格取值： [ 产品属性价目表 ] 中 [ 当前属性价格 ] ，折扣带出依据  [ 产品属性价目表 ] 中[ 定价方式 ] 判断"))),t("li",{style:e},"① ".concat($t("[ 定价方式 ] 为 [ 价目表折扣 ] ，[ 折扣 ] 取值 [ 适配的价目表 ] 中：产品折扣"))),t("li",{style:e},"② ".concat($t("[ 定价方式 ] 为 [ 指定折扣 ] ，[ 折扣 ] 取值 [ 产品属性价目表 ] 中：产品属性的[ 折扣 ]"))),t("li",{style:e},"b、".concat($t("[ 产品属性价目表 ] 未适配某价目表， [ 产品属性选择后 ] 取值：依据 [ 价目表 ] 中 [ 价目表售价 ] 和 [ 价目表折扣 ]")))]}]}],children:[{title:$t("报价器启用开关",null,"报价器启用开关"),key:"is_open_quoter",type:"switch",value:!1,isShow:!0,enableClose:!1,describeList:[$t("该开关一旦开启，不可关闭。"),$t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")})]},{title:$t("sfa.CRM.setting.enable_attribute_incremental_pricing",null,"启用属性增量定价"),key:"is_open_incremental_pricing",type:"switch",value:!1,enableClose:!1,describeList:[$t("该开关一旦开启，不可关闭。"),$t("开启当前开关时，请先开启[{{title}}]。",{title:$t("属性配置开启开关")})]}]},{isShow:!0,title:$t("非标属性开启开关"),key:"is_open_nonstandard_attribute",type:"switch",value:!1,enableClose:!1,describeList:[{title:$t("开启或关闭的操作，只可操作一次，请慎重操作")}]}]},{domId:"level34",moduleId:"cpqUIMode",title:$t("cpq.cpq_ui_mode",null,"产品选配模式"),visible:!1,moduleList:[{type:"CpqUIMode",key:"cpq_ui_mode",value:"0",displayCount:3,isDisabled:!1,isShow:!0,enableClose:!0,render:null}]},{domId:"level4",moduleId:"simpleCpq",title:$t("固定搭配"),visible:(r=_asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,CRM.util.getConfigValue("price_policy");case 2:return e=t.sent,t.abrupt("return",e&&"1"===e.value&&CRM.util.isGrayScale("CRM_FIXED_COLLOCATION"));case 4:case"end":return t.stop()}},t)})),function(){return r.apply(this,arguments)}),moduleList:[{type:"switch",title:$t("固定搭配开启开关"),key:"simple_cpq",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],describeList:[$t("CRM.setting.fastsale.fixedCollocation.intro.desc1"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc2"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc3"),$t("CRM.setting.fastsale.fixedCollocation.intro.desc4")],children:[{title:$t("CRM.setting.fastsale.fixedCollocation.bom.description.title",null,"产品包价格是否包含包内产品价格"),key:"bom_price_calculation_configuration",type:"radio",value:"0",isShow:!1,radioOptions:[{label:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option1",null,"产品包价格包含包内产品价格"),warningMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option1.warn_info1",null,"选用此选项，则：产品包价格为整个固定搭配的价格，可用于为整个固定搭配定价。"),value:"0"},{label:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2",null,"产品包价格不包含所添加产品价格"),warningMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info1",null,"选用此选项，则：产品包价格+搭配内产品的金额（数量x价格）为固定搭配的价格；"),secWaringMessage:$t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info2",null,"当固定搭配的“组合销售类型”为【产品包不独立销售】时，选此开关产品包价格设置为0，则：搭配内产品的总金额（数量x价格）为固定搭配的价格。"),value:"1"}]}]}]},{domId:"level5",moduleId:"productCategory",title:$t("sfa.crm.setting_cmmodityproduct.product_category_module_title"),moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.product_category_title"),key:"close_old_category",value:!1,enableClose:!1,confirmInfo:$t("crm.setting.tradeconfigure.confirm_open_notclose",{name:$t("sfa.crm.setting_cmmodityproduct.product_category_module_title")}),describeList:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc3")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc4")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.2.3")}]},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc5.3")}]},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6"),list:[{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.1")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.2")},{title:$t("sfa.crm.setting_cmmodityproduct.product_category_desc6.3")}]}]}]},{domId:"level6",moduleId:"productCyclical",title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_module_title",null,"周期性产品"),visible:!1,licenseKey:"cpq_subscription_product_management_app",moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_title",null,"周期性产品开启开关"),key:"periodic_product",value:!1,enableClose:!1,confirmInfo:$t("确认开启{{name}}吗",{name:$t("sfa.crm.setting_cmmodityproduct.periodic_product_title")}),isShow:!0,describeList:[],children:[{title:$t("sfa.crm.setting_cmmodityproduct.periodic_product_plugin_title",null,"开启以下对象范围"),key:"periodic_product_plugin",type:"checkbox-group",value:[],isShow:!1,enableClose:!1,confirmInfo:$t("开启后不可关闭"),options:[{key:"NewOpportunityObj",label:$t("商机")},{key:"QuoteObj",label:$t("报价单")},{key:"SaleContractObj",label:$t("销售合同")},{key:"SalesOrderObj",label:$t("销售订单")}],describeList:[$t("sfa.crm.setting_cmmodityproduct.periodic_product_plugin.desc1",null,"开启开关后，产品支持周期性特征配置，如产品设置计费模式、计费周期等信息。")]}]}]},{domId:"level7",moduleId:"productNonstandard",title:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_module_title",null,"非标产品"),visible:!0,moduleList:[{type:"switch",title:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_title",null,"非标产品开启开关"),key:"non_standard_product",value:!1,enableClose:!1,confirmInfo:$t("确认开启{{name}}吗",{name:$t("sfa.crm.setting_cmmodityproduct.nonstandard_product_title")}),isShow:!0,describeList:[$t("sfa.crm.setting_cmmodityproduct.nonstandard_product.desc1",null,"开启开关后，支持[事先不明确具体产品]的交易，自动以特定的【默认非标产品】进行流转，可填写非标产品的描述，不进行各种业务强校验（如价格上下限）。"),$t("sfa.crm.setting_cmmodityproduct.nonstandard_product.desc2",null,"说明：非标产品的能力体现在新建编辑页布局上。如需启用非标产品，请先开启交易单据的新建编辑页布局。")],children:[{title:$t("sfa.crm.setting_nonstandard_product.not_reprice",null,"交易单据的非标品行切换为普通产品时，不重新取价"),key:"change_product_type_refresh_price",type:"switch",value:!1,isShow:!0,describeList:[{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg1",null,"支持单据：商机、报价单、销售合同、销售订单，及配置了非标产品插件的其他对象")},{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg2",null,"开关关闭：单据里非标品行的【产品名称】更新为普通品时，产品相关信息更新，价格信息按普通品重新取价、价格相关字段重新计算，其他与产品和价格无关的字段保持不变。开关默认关闭。")},{title:$t("sfa.crm.setting_nonstandard_product.not_reprice.msg3",null,"开关开启：单据里非标品行的【产品名称】更新为普通品时，产品相关信息更新，价格及价格相关字段保留原值，不重新取价，其他与产品和价格无关的字段保持不变")}]}]}]}];i.exports=n});