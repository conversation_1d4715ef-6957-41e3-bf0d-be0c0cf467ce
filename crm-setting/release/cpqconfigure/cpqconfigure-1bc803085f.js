function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function f(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{f({},"")}catch(s){f=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var o,i,a,u,e=e&&e.prototype instanceof p?e:p,e=Object.create(e.prototype);return f(e,"_invoke",(o=t,i=n,a=new b(r||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:s,done:!0}}for(a.method=t,a.arg=e;;){var n=a.delegate;if(n){n=function t(e,n){var r=n.method,o=e.i[r];if(o===s)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=s,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),l;r=h(o,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,l;o=r.arg;return o?o.done?(n[e.r]=o.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,l):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,l)}(n,a);if(n){if(n===l)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;n=h(o,i,a);if("normal"===n.type){if(u=a.done?4:2,n.arg===l)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(u=4,a.method="throw",a.arg=n.arg)}}),!0),e}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var l={};function p(){}function i(){}function d(){}var e={},g=(f(e,r,function(){return this}),Object.getPrototypeOf),g=g&&g(g(k([]))),y=(g&&g!==t&&c.call(g,r)&&(e=g),d.prototype=p.prototype=Object.create(e));function m(t){["next","throw","return"].forEach(function(e){f(t,e,function(t){return this._invoke(e,t)})})}function _(a,u){var e;f(this,"_invoke",function(n,r){function t(){return new u(function(t,e){!function e(t,n,r,o){var i,t=h(a[t],a,n);if("throw"!==t.type)return(n=(i=t.arg).value)&&"object"==_typeof(n)&&c.call(n,"__await")?u.resolve(n.__await).then(function(t){e("next",t,r,o)},function(t){e("throw",t,r,o)}):u.resolve(n).then(function(t){i.value=t,r(i)},function(t){return e("throw",t,r,o)});o(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function v(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function b(t){this.tryEntries=[[-1]],t.forEach(v,this),this.reset(!0)}function k(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(c.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return f(y,"constructor",i.prototype=d),f(d,"constructor",i),i.displayName=f(d,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,f(t,o,"GeneratorFunction")),t.prototype=Object.create(y),t},a.awrap=function(t){return{__await:t}},m(_.prototype),f(_.prototype,n,function(){return this}),a.AsyncIterator=_,a.async=function(t,e,n,r,o){void 0===o&&(o=Promise);var i=new _(u(t,e,n,r),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},m(y),f(y,o,"Generator"),f(y,r,function(){return this}),f(y,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=k,b.prototype={constructor:b,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){i.type="throw",i.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var o=this.tryEntries[r],i=o[4],a=this.prev,u=o[1],c=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=s,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var o=r;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),w(n),l}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,o=this.tryEntries[e];if(o[0]===t)return"throw"===(n=o[4]).type&&(r=n.arg,w(o)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:k(t),r:e,n:n},"next"===this.method&&(this.arg=s),l}},a}function asyncGeneratorStep(t,e,n,r,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void n(t)}u.done?e(c):Promise.resolve(c).then(r,o)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(e,n){var r=u.apply(t,a);function o(t){asyncGeneratorStep(r,e,n,o,i,"next",t)}function i(t){asyncGeneratorStep(r,e,n,o,i,"throw",t)}o(void 0)})}}define("crm-setting/cpqconfigure/cpqconfigure",["./data"],function(n,t,e){var r=n("./data"),o=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.getLicense()},getLicense:function(){var e=this;CRM.api.get_licenses({key:["cpq_app","cpq_base_app"],cb:function(t){t.cpq_app||t.cpq_base_app||e.setVisible(),e.init()}})},setVisible:function(){r.forEach(function(t){return t.visible=!1})},init:function(t){var e=this;n.async("vcrm/sdk",function(t){t.getComponent("backstage").then(function(t){e.initView(t.default,r)})})},initView:function(t,e){new Vue({el:this.$el[0],template:'\n\t\t\t\t\t<Backstage :data="dataList" :methodList="methodList" @change="change" @click="click"></Backstage>\n\t\t\t\t',components:{Backstage:t},data:function(){return{dataList:e,methodList:{afterGetConfig:this.afterGetConfig,beforeSetConfig:this.beforeSetConfig,afterSetConfig:this.afterSetConfig}}},watch:{},computed:{},created:function(){},mounted:function(){this.initDataList()},methods:{click:function(t,e){},afterGetConfig:function(t){var n=this;"1"===t[0].find(function(t){return"cpq"===t.key}).value&&CRM.util.forEachTreeData(this.dataList[0].moduleList,function(t){var e;t.isShow=!0,"bom_temp_node"===t.key&&(t.isShow=!0),"standard_cpq"===t.key&&(t.isShow=!0),"generate_standard_bom_based_on_order"===t.key&&(t.isShow=n.getConfigByKey("standard_cpq").value),"multiplexed_bom_mode"===t.key&&(t.isShow=!0),"bom_duplicate_check"===t.key&&(e=n.getConfigByKey("generate_standard_bom_based_on_order").value,t.isShow=["1","2"].includes(e))})},beforeSetConfig:function(t,e,n){var r;switch(t){case"bom_print_template_has_sub_node":n.confirmMessage=e?$t("确认开启{{name}}吗",{name:$t("报价单订单销售合同打印时只包含母件产品")}):$t("确认关闭只打印母件开关吗？");break;case"bom_duplicate_check":n.confirmMessage=e?CRM.util.getI18n(["确认开启","BOM","查重","校验","开关"])+"?":CRM.util.getI18n(["确认关闭","BOM","查重","校验","开关"])+"?";break;case"not_show_bom":n.confirmMessage=e?$t("确认开启{{name}}吗",{name:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页")}):$t("确认关闭{{name}}吗",{name:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页")})}var o=null;return null!=(r=n.setUrl)&&r.includes("save_module_status")&&(o={param:{moduleCode:t,openStatus:e?"1":"0",tenantId:CRM.enterpriseId}},"generate_standard_bom_based_on_order"===t)&&(o.param.openStatus=e),o},afterSetConfig:function(t,e,n){"generate_standard_bom_based_on_order"===t&&(t=this.getConfigByKey("bom_duplicate_check"))&&(t.isShow="0"!=e)},getConfigByKey:function(t){return this._allConfig[t]},initDataList:function(){var e=this;this._allConfig={},this.dataList.forEach(function(t){CRM.util.forEachTreeData(t.moduleList,function(t){e._allConfig[t.key]=t})})},change:function(t){t.type;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}},t)}))()}}})},destroy:function(){}});e.exports=o});
define("crm-setting/cpqconfigure/data",[],function(e,t,l){var s=[{domId:"level0",title:$t("CPQ配置"),moduleId:"cpqConfig",autoSetConfig:!0,moduleList:[{type:"switch",title:$t("CPQ开启开关"),key:"cpq",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("crm.确认开启CPQ吗"),getUrl:"",setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:$t("该开关一旦开启，不可关闭。"),list:[]},{title:$t("开启该开关后，报价单和订单可以配置复杂产品和价格，其它业务模块暂时不支持。"),list:[]},{title:$t("cpq开关说明3"),list:[{title:$t("cpq开关说明3_1")},{title:$t("cpq开关说明3_2")},{title:$t("cpq开关说明3_2_1")},{title:$t("cpq开关说明3_2_2")}]},{title:$t("开启该开关后，价目表暂时只能设置产品组合的折扣/价格，无法设置其子产品的折扣/价格。"),list:[{title:$t("如果在报价单和订单中选择了产品组合并配置了其子产品，最终产品组合显示价格按照价目表中的折扣折算。")},{title:$t("比如，熊猫电脑 价格5,000元，价目表中折扣设置80%。")+$t("cpq举例说明")}]}]},{isShow:!1,type:"switch",title:$t("BOM套BOM模式"),key:"multiplexed_bom_mode",value:!1,displayCount:3,isDisabled:!1,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[$t("crm.multiplexed_bom_mode.desc1",null,"功能概述：此开关允许用户通过嵌套BOM的方式创建和管理多层级的产品结构。开启后，BOM的层级和结构将支持通过嵌套子BOM来实现复杂组合产品的构建。"),$t("crm.multiplexed_bom_mode.desc2",null,"适用场景：特别适用于那些需要构建由多个子组件组成的复杂产品，而这些子组件本身也是由多个子件构成的BOM结构的情况。"),$t("crm.multiplexed_bom_mode.desc3",null,"操作效果：启用此开关后，用户可以在BOM中添加其他BOM作为子项，从而实现产品结构的多层次嵌套。这允许用户在创建新产品时，复用已有的子BOM，简化设计流程，提高效率。"),{title:$t("优势"),list:[$t("crm.multiplexed_bom_mode.desc4",null,"结构复用：通过嵌套子BOM，可以复用已有的物料结构，减少重复定义相同组件的工作。"),$t("crm.multiplexed_bom_mode.desc5",null,"简化管理：简化了物料和产品结构的管理，使得维护和更新更加高效。"),$t("crm.multiplexed_bom_mode.desc6",null,"灵活性增强：提高了设计和生产的灵活性，便于快速响应市场变化和客户需求。")]}]},{type:"switch",title:$t("报价单订单销售合同打印时只包含母件产品"),key:"bom_print_template_has_sub_node",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{type:"radio",title:$t("cpq-子产品价目表规则"),key:"bom_adaptation_price_list_rules",value:"0",displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[{label:$t("子件产品跟随母件选择的价目表"),value:"0",warningMessage:$t("主子同步注意1")},{label:$t("子件产品随价目表优先级"),value:"1",warningMessage:$t("主子同步注意2")}],describeList:[]},{type:"radio",title:$t("cpq.calculate.msg"),key:"bom_price_calculation_configuration",value:"0",displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[{label:$t("含默认子件"),value:"0",warningMessage:$t("cpq.calculate.msg1"),secWaringMessage:$t("cpq.calculate.msg2")},{label:$t("不")+$t("含默认子件"),value:"1",warningMessage:$t("cpq.calculate.msg3"),secWaringMessage:$t("cpq.calculate.msg4")}],describeList:[]},{type:"switch",title:$t("cpq.optionalSwitch"),key:"bom_instance",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("cpq.optionalSwitch.msg"),setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{isShow:!1,type:"switch",title:$t("standard_cpq.title",null,"开启标准BOM"),key:"standard_cpq",value:!1,displayCount:3,enableClose:!1,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("standard_cpq.desc.title",null,"开关开启后，产品组合可以创建BOM类型为【标准BOM】的BOM")}]}]},{type:"radio",title:$t("crm.bom.generate_standard_bom_based_on_order"),key:"generate_standard_bom_based_on_order",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[],radioOptions:[{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label1",null,"不生成标准BOM"),value:"0"},{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label2",null,"自动生成标准BOM"),value:"1"},{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label3",null,"手动生成标准BOM"),value:"2"}]},{type:"switch",title:$t("crm.setting.cpqconfig.bom_duplicate_check.title",null,"BOM查重校验"),key:"bom_duplicate_check",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{type:"switch",title:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页"),key:"not_show_bom",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,describeList:[$t("crm.setting.cpq.not_show_bom_desc",null,"用途说明：报价单/销售合同/销售订单 选择数据页面，选择“产品组合”时，不弹出选配产品页面，默认弹出。")]},{type:"switch",title:$t("开启临时子件"),key:"bom_temp_node",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("确认开启临时子件吗？开关开启后不可关闭"),setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{isShow:!1,type:"BomcoreFilterRule",title:$t("crm.bomcoreFilterRule.title",null,"产品组合数据过滤规则"),key:"biz_function",value:"",displayCount:3,isDisabled:!1,enableClose:!0,describeList:[]},{isShow:!1,type:"switch",title:$t("crm.setting.cpq.bom_node_share",null,"CPQ分摊"),key:"bom_node_share",displayCount:3,isDisabled:!1,enableClose:!1,describeList:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status"},{isShow:!1,type:"switch",title:$t("crm.setting.cpq.bom_single_leaf_node_closed",null,"自动收起末级单选子件分组"),key:"bom_single_leaf_node_closed",displayCount:3,isDisabled:!1,enableClose:!0,describeList:[$t("crm.setting.cpq.bom_single_leaf_node_closed.desc1",null,"开关开启后，在组合产品配置的场景下，组内的选配规则为单选（即一次只能选择一个子件），用户勾选某个子件后，系统会自动将包含该子件的分组收起，以简化界面和避免用户进一步操作同一分组内的其他子件。"),$t("crm.setting.cpq.bom_single_leaf_node_closed.desc2",null,"开关关闭后，用户勾选子件后，包含该子件的分组保持展开状态，用户可以继续查看或更改同一分组内的其他子件选项。")]},{isShow:!1,type:"switch",title:$t("crm.bom_detail_layout_closed"),key:"bom_detail_layout_closed",value:!1,displayCount:3,enableClose:!0,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("crm.bom_detail_layout_closed.msg")+"；"+$t("crm.bom_detail_layout_closed.msg2")}]}]},{isShow:!1,type:"switch",title:$t("sfa.crm.bom_delete_root"),key:"bom_delete_root",value:!1,displayCount:3,enableClose:!0,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("sfa.crm.bom_delete_root.msg")}]}]},{isShow:!1,type:"switch",title:$t("sfa.crm.cpq_enabled_latest_version",null,"选择产品组合默认最新版本"),key:"cpq_enabled_latest_version",value:!1,displayCount:3,enableClose:!0,describeList:[$t("sfa.crm.cpq_enabled_latest_version.msg",null,"用途说明：选数据页面，选择“产品组合”时，如果存在多版本，默认取最新版本。")]}]}];l.exports=s});