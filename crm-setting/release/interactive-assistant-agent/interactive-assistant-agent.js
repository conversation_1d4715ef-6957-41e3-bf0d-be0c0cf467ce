define("crm-setting/interactive-assistant-agent/interactive-assistant-agent",["crm-modules/common/util","./template/index-html"],function(i,e,t){var n=i("crm-modules/common/util"),r=i("./template/index-html"),a=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},config:{page1:{id:"customerInteractionAgent",wrapper:".recording-retention-rules-box",path:"./recordingRetentionRules/recordingRetentionRules"},page2:{id:"selfprompt",wrapper:".self-prompt",path:"./selfprompt/selfprompt"},page3:{id:"recordSetting",wrapper:".recording-setting",path:"./recordingSetting/recordingSetting"}},events:{"click .crm-tab a":"onHandle"},render:function(e){var t=n.getTplQueryParams(),t=_.values(t);e=e||[t[1]],this.pages={},this.$el.html(r()),this.renderPage()},onHandle:function(e){var n=$(e.target),r=this;e.preventDefault(),_.some(this.config,function(e,t){if(n.hasClass(t))return r.switchPage([t]),!0})},switchPage:function(e){this.renderPage(e)},renderPage:function(e){var t,n=this,r=n.$(".crm-tab .item"),r=(r.removeClass("cur"),(e&&e[0]?r.filter("."+e[0]):r.eq(0)).addClass("cur"),n.curId=e&&e[0]?e[0]:"page1");_.map(n.pages,function(e){e.hide()}),n.pages[r]?n.pages[r].show():(t=r,i.async(n.config[t].path,function(e){e=new e(_.extend({wrapper:n.config[t].wrapper}));n.curId===t&&e.show(),n.pages[t]=e}))},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.pages=this.curId=null}});t.exports=a});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,u=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function f(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(s){f=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o,i,a,c,e=e&&e.prototype instanceof p?e:p,e=Object.create(e.prototype);return f(e,"_invoke",(o=t,i=r,a=new b(n||[]),c=1,function(t,e){if(3===c)throw Error("Generator is already running");if(4===c){if("throw"===t)throw e;return{value:s,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===s)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=s,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),l;n=h(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,l;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=s),r.delegate=null,l):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,l)}(r,a);if(r){if(r===l)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===c)throw c=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);c=3;r=h(o,i,a);if("normal"===r.type){if(c=a.done?4:2,r.arg===l)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(c=4,a.method="throw",a.arg=r.arg)}}),!0),e}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=c;var l={};function p(){}function i(){}function d(){}var e={},y=(f(e,n,function(){return this}),Object.getPrototypeOf),y=y&&y(y(x([]))),g=(y&&y!==t&&u.call(y,n)&&(e=y),d.prototype=p.prototype=Object.create(e));function v(t){["next","throw","return"].forEach(function(e){f(t,e,function(t){return this._invoke(e,t)})})}function m(a,c){var e;f(this,"_invoke",function(r,n){function t(){return new c(function(t,e){!function e(t,r,n,o){var i,t=h(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&u.call(r,"__await")?c.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):c.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function _(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function b(t){this.tryEntries=[[-1]],t.forEach(_,this),this.reset(!0)}function x(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(u.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return f(g,"constructor",i.prototype=d),f(d,"constructor",i),i.displayName=f(d,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,f(t,o,"GeneratorFunction")),t.prototype=Object.create(g),t},a.awrap=function(t){return{__await:t}},v(m.prototype),f(m.prototype,r,function(){return this}),a.AsyncIterator=m,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new m(c(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},v(g),f(g,o,"Generator"),f(g,n,function(){return this}),f(g,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=x,b.prototype={constructor:b,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,c=o[1],u=o[2];if(-1===o[0])return t("end"),!1;if(!c&&!u)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<c)return this.method="next",this.arg=s,t(c),!0;if(a<u)return t(u),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),w(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,w(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:x(t),r:e,n:r},"next"===this.method&&(this.arg=s),l}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function _asyncToGenerator(c){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=c.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}define("crm-setting/interactive-assistant-agent/recordingRetentionRules/recordingRetentionRules",[],function(t,e,r){var n,o=Backbone.View.extend({config:{dont_save_real_time_record_audio:!1},initialize:(n=_asyncToGenerator(_regeneratorRuntime().mark(function t(e){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.setElement(e.wrapper),t.next=3,this.getConfig();case 3:this.render(e);case 4:case"end":return t.stop()}},t,this)})),function(t){return n.apply(this,arguments)}),render:function(t){this.renderCom()},renderCom:function(){var t=this;FxUI.create({wrapper:this.$el[0],template:'\n                   <div class="interactive-assistant-agent-wrapper">\n                         <fx-alert\n                            class="alert"\n                            type="info"\n                            show-icon\n                            title=\''.concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_title"),'\'\n                            :closable="false"\n                        >\n                            <p>').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip1"),"</p>\n                            <p>").concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip2"),"</p>\n                            <p>").concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip3"),'</p>\n                        </fx-alert>\n                        <div class="form-wrapper">\n                            <fx-checkbox\n                                size="mini"\n                                v-model="checked"\n                                label=\'').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_checkbox"),'\'\n                            ></fx-checkbox>\n                            <br />\n                            <fx-button size="mini" type="primary" @click="save">').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_button"),"</fx-button>\n                        </div>\n                    </div>\n                "),data:function(){return{checked:t.config.dont_save_real_time_record_audio}},methods:{save:function(){CRM.util.showLoading();CRM.util.setConfigValue({key:"dont_save_real_time_record_audio",value:this.checked?"1":"0"}).then(function(){CRM.util.remind(1,$t("设置成功"))}),CRM.util.hideLoading()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},getConfig:function(){var r=this;return new Promise(function(e,t){CRM.util.getConfigValue("dont_save_real_time_record_audio").then(function(t){r.config.dont_save_real_time_record_audio="1"===t,e()})})}});r.exports=o});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,u=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function f(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(s){f=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o,i,a,c,e=e&&e.prototype instanceof p?e:p,e=Object.create(e.prototype);return f(e,"_invoke",(o=t,i=r,a=new b(n||[]),c=1,function(t,e){if(3===c)throw Error("Generator is already running");if(4===c){if("throw"===t)throw e;return{value:s,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===s)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=s,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),l;n=h(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,l;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=s),r.delegate=null,l):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,l)}(r,a);if(r){if(r===l)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===c)throw c=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);c=3;r=h(o,i,a);if("normal"===r.type){if(c=a.done?4:2,r.arg===l)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(c=4,a.method="throw",a.arg=r.arg)}}),!0),e}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=c;var l={};function p(){}function i(){}function y(){}var e={},d=(f(e,n,function(){return this}),Object.getPrototypeOf),d=d&&d(d(x([]))),g=(d&&d!==t&&u.call(d,n)&&(e=d),y.prototype=p.prototype=Object.create(e));function m(t){["next","throw","return"].forEach(function(e){f(t,e,function(t){return this._invoke(e,t)})})}function v(a,c){var e;f(this,"_invoke",function(r,n){function t(){return new c(function(t,e){!function e(t,r,n,o){var i,t=h(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&u.call(r,"__await")?c.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):c.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function _(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function b(t){this.tryEntries=[[-1]],t.forEach(_,this),this.reset(!0)}function x(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(u.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return f(g,"constructor",i.prototype=y),f(y,"constructor",i),i.displayName=f(y,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,f(t,o,"GeneratorFunction")),t.prototype=Object.create(g),t},a.awrap=function(t){return{__await:t}},m(v.prototype),f(v.prototype,r,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new v(c(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},m(g),f(g,o,"Generator"),f(g,n,function(){return this}),f(g,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=x,b.prototype={constructor:b,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,c=o[1],u=o[2];if(-1===o[0])return t("end"),!1;if(!c&&!u)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<c)return this.method="next",this.arg=s,t(c),!0;if(a<u)return t(u),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),w(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,w(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:x(t),r:e,n:r},"next"===this.method&&(this.arg=s),l}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function _asyncToGenerator(c){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=c.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}define("crm-setting/interactive-assistant-agent/recordingSetting/recordingSetting",["crm-modules/common/util"],function(t,e,r){var n,o=t("crm-modules/common/util"),t=Backbone.View.extend({config:{sfa_activity_record_audio_layout:!1},initialize:(n=_asyncToGenerator(_regeneratorRuntime().mark(function t(e){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.setElement(e.wrapper),t.next=3,this.getConfig();case 3:this.render(e);case 4:case"end":return t.stop()}},t,this)})),function(t){return n.apply(this,arguments)}),render:function(t){this.renderComps()},renderComps:function(){var t=this;FxUI.create({wrapper:this.$el[0],template:'\n                 <div class="interactive-assistant-agent-wrapper">\n                       <fx-alert\n                          class="alert"\n                          type="info"\n                          show-icon\n                          title=\''.concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_title"),'\'\n                          :closable="false"\n                      >\n                          <p>').concat($t("sfa.activity.crmmanage.recording_setting_tip1"),"</p>\n                          <p>").concat($t("sfa.activity.crmmanage.recording_setting_tip2"),'</p>\n                      </fx-alert>\n                      <div class="form-wrapper">\n                          <fx-checkbox\n                              size="mini"\n                              v-model="checked"\n                              label=\'').concat($t("sfa.activity.crmmanage.recording_setting_checkbox"),'\'\n                          ></fx-checkbox>\n                          <br />\n                          <fx-button size="mini" type="primary" @click="save">').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_button"),"</fx-button>\n                      </div>\n                  </div>\n              "),data:function(){return{checked:t.config.sfa_activity_record_audio_layout}},methods:{save:function(){o.showLoading();o.setConfigValue({key:"sfa_activity_record_audio_layout",value:this.checked?"1":"0"}).then(function(){o.remind(1,$t("设置成功"))}),o.hideLoading()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},getConfig:function(){var r=this;return new Promise(function(e,t){o.getConfigValue("sfa_activity_record_audio_layout").then(function(t){r.config.sfa_activity_record_audio_layout="1"===t,e()})})}});r.exports=t});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var c,a={},t=Object.prototype,s=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function l(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{l({},"")}catch(c){l=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o,i,a,u,e=e&&e.prototype instanceof f?e:f,e=Object.create(e.prototype);return l(e,"_invoke",(o=t,i=r,a=new _(n||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:c,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===c)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=c,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;n=p(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,h;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=c),r.delegate=null,h):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}(r,a);if(r){if(r===h)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;r=p(o,i,a);if("normal"===r.type){if(u=a.done?4:2,r.arg===h)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(u=4,a.method="throw",a.arg=r.arg)}}),!0),e}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var h={};function f(){}function i(){}function d(){}var e={},y=(l(e,n,function(){return this}),Object.getPrototypeOf),y=y&&y(y(x([]))),m=(y&&y!==t&&s.call(y,n)&&(e=y),d.prototype=f.prototype=Object.create(e));function g(t){["next","throw","return"].forEach(function(e){l(t,e,function(t){return this._invoke(e,t)})})}function v(a,u){var e;l(this,"_invoke",function(r,n){function t(){return new u(function(t,e){!function e(t,r,n,o){var i,t=p(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&s.call(r,"__await")?u.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):u.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=c,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function x(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(s.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=c,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return l(m,"constructor",i.prototype=d),l(d,"constructor",i),i.displayName=l(d,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,l(t,o,"GeneratorFunction")),t.prototype=Object.create(m),t},a.awrap=function(t){return{__await:t}},g(v.prototype),l(v.prototype,r,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new v(u(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},g(m),l(m,o,"Generator"),l(m,n,function(){return this}),l(m,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=x,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&s.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,u=o[1],s=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=c,t(u),!0;if(a<s)return t(s),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],h):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),b(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,b(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:x(t),r:e,n:r},"next"===this.method&&(this.arg=c),h}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var u=t[i](a),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=u.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}define("crm-setting/interactive-assistant-agent/selfprompt/selfprompt",["crm-modules/page/list/list"],function(t,e,r){var n,u=t("crm-modules/page/list/list"),t=Backbone.View.extend({initialize:(n=_asyncToGenerator(_regeneratorRuntime().mark(function t(e){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:this.setElement(e.wrapper),this.render(e);case 2:case"end":return t.stop()}},t,this)})),function(t){return n.apply(this,arguments)}),render:function(t){this.renderList()},renderList:function(){var t=u.extend({getOptions:function(){var t=u.prototype.getOptions.apply(this,arguments);return _.extend(t,{autoHeight:!0,maxHeight:$(".crm-s-interactive-assistant-agent").height()-450})},parseData:function(t){var e=u.prototype.parseData.apply(this,arguments);return _.each(e.data,function(e){_.each(e.operate,function(t){"ChangeStatus"==t.action&&(t.label="disable"==e.prompt_status?$t("启用"):$t("停用")),t.render_type="not_fold"})}),e},parseColumns:function(t,i){var e=u.prototype.parseColumns.apply(this,arguments);return e.map(function(t){return"operate"==t.dataType&&(t.render=function(t,e,r){return t.replace('class="tr-operate-btn-item tr-operate- crm-ui-title"','class="tr-operate-btn-item tr-operate- crm-ui-title" style="color: #1890ff;"')}),"rule"==t.api_name&&(t.render=function(t,e,r){var n,r=(r=r.rule?null==(r=JSON.parse(r.rule))||null==(r=r.filter[0])?void 0:r.field_values:[])||[],o=(null==i||null==(n=i.interactive_scenario)?void 0:n.options)||[];return r.map(function(e){var t=o.find(function(t){return t.value===e});return t?t.label:e}).join(",")||"--"}),"subtemplate_api_name"==t.api_name&&(t.render=function(t,e,r){r=r.subtemplate_api_name?null==(r=JSON.parse(r.subtemplate_api_name))?void 0:r.promptName:"--";return'<span style="color: #1890ff;" data-action="showTemplateDetailEdit">'.concat(r,"</span>")}),t}),e},trclickHandle:function(t,e,r,n,o,i){"showTemplateDetailEdit"===r.data("action")?this[r.data("action")](t):u.prototype.trclickHandle.apply(this,arguments)},showTemplateDetailEdit:function(t){t=t.subtemplate_api_name?null==(t=JSON.parse(t.subtemplate_api_name))?void 0:t.promptApiName:"";t&&Fx.getBizAction("paasdev","openPromptFormDialog",{status:"update",apiName:t})},completeRender:function(){this.table&&this.table.hideLoading()},operateBtnClickHandle:function(t){var e,r=this,t=$(t.target),n=t.data("action"),o=t.closest(".tr").data("index"),i=r.getCurData().data,a=i&&i[o],i=t.closest(".tr").find("[data-action=ChangeStatus]").text();"ChangeStatus"==n?e=CRM.util.confirm($t("确认{{changestatus}}这个订阅规则项目？",{changestatus:i}),null,function(){e.destroy(),r.changeEnable(a._id,"disable"==a.prompt_status)},{hideFn:function(){},stopPropagation:!0}):u.prototype.operateBtnClickHandle.apply(this,arguments)},changeEnable:function(t,e){var r=this;CRM.util.waiting(),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/AgentPromptVersionObj/action/ChangeStatus",data:{objectIds:[t],isEnabled:e},success:function(t){0==t.Result.StatusCode?(CRM.util.waiting(!1),CRM.util.remind(1,$t("操作成功"))):(CRM.util.waiting(!1),CRM.util.remindFail(t.Result.FailureMessage)),r.table.refresh()},error:function(){CRM.util.waiting(!1),r.table.refresh()}},{errorAlertModel:1})}});this.list=new t({wrapper:this.$el.find(".list-view"),apiname:"AgentPromptVersionObj"}),this.list.render()},show:function(){this.$el.show()},hide:function(){this.$el.hide()}});r.exports=t});
define("crm-setting/interactive-assistant-agent/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("sfa.activity.crmmanage.customer_interaction_agent_title")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item page1" href="javascript:void(0)">' + ((__t = $t("sfa.activity.crmmanage.recording_retention_rules_tab_item")) == null ? "" : __t) + '</a> <a class="item page2" href="javascript:void(0)">' + ((__t = $t("sfa.activity.prompt.self_prompt_tab_item")) == null ? "" : __t) + '</a> <a class="item page3" href="javascript:void(0)">' + ((__t = $t("sfa.activity.crmmanage.recording_setting_tab_item")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item recording-retention-rules-box" style="display:none;"> <!-- <div class="crm-loading"></div> --> </div> <div class="item self-prompt" style="display:none;"> <!-- <div class="crm-loading"></div> --> <div class="crm-set-system crm-set-bid"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc")) == null ? "" : __t) + "</p> <h3>" + ((__t = $t("注意事项")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc_notice_first")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc_notice_second")) == null ? "" : __t) + '</p> </div> <div class="list-view"> <div class="crm-loading"></div> </div> </div> </div> <div class="item recording-setting" style="display:none;"> </div> </div> </div> </div>';
        }
        return __p;
    };
});