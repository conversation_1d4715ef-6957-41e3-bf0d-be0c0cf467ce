function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var c,a={},t=Object.prototype,u=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function f(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(c){f=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o,i,a,s,e=e&&e.prototype instanceof l?e:l,e=Object.create(e.prototype);return f(e,"_invoke",(o=t,i=r,a=new _(n||[]),s=1,function(t,e){if(3===s)throw Error("Generator is already running");if(4===s){if("throw"===t)throw e;return{value:c,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===c)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=c,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;n=h(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,p;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=c),r.delegate=null,p):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,p)}(r,a);if(r){if(r===p)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===s)throw s=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=3;r=h(o,i,a);if("normal"===r.type){if(s=a.done?4:2,r.arg===p)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=4,a.method="throw",a.arg=r.arg)}}),!0),e}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=s;var p={};function l(){}function i(){}function y(){}var e={},d=(f(e,n,function(){return this}),Object.getPrototypeOf),d=d&&d(d(E([]))),g=(d&&d!==t&&u.call(d,n)&&(e=d),y.prototype=l.prototype=Object.create(e));function m(t){["next","throw","return"].forEach(function(e){f(t,e,function(t){return this._invoke(e,t)})})}function v(a,s){var e;f(this,"_invoke",function(r,n){function t(){return new s(function(t,e){!function e(t,r,n,o){var i,t=h(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&u.call(r,"__await")?s.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):s.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=c,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function E(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(u.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=c,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return f(g,"constructor",i.prototype=y),f(y,"constructor",i),i.displayName=f(y,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,f(t,o,"GeneratorFunction")),t.prototype=Object.create(g),t},a.awrap=function(t){return{__await:t}},m(v.prototype),f(v.prototype,r,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new v(s(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},m(g),f(g,o,"Generator"),f(g,n,function(){return this}),f(g,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=E,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,s=o[1],u=o[2];if(-1===o[0])return t("end"),!1;if(!s&&!u)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<s)return this.method="next",this.arg=c,t(s),!0;if(a<u)return t(u),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),b(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,b(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:E(t),r:e,n:r},"next"===this.method&&(this.arg=c),p}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var s=t[i](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,o)}function _asyncToGenerator(s){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=s.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}define("crm-setting/assetmanagement/assetmanagement",["crm-modules/common/util"],function(n,t,e){var o=n("crm-modules/common/util");return Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper)},render:function(){this.initDeviceSettingView()},initDeviceSettingView:function(){var n=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e,r;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=n,t.next=3,e.queryDeviceConfig();case 3:1!=+(e=t.sent)?(r={isPaasMenu:!0,fromManage:!0,hasBack:!"app-workorder/tpls/devicesetting/devicesettingv2/devicesettingv2-vue",propTitle:$t("stock_asset_management"),propStatus:e,isSubModule:!0},n.requirePage("app-workorder/tpls/devicesetting/devicesettingv2/devicesettingv2-vue",r)):(r={isPaasMenu:!0,fromManage:!0,isSubModule:!0,bReady:!0,propMenuCode:"da_basic_setting",hasBack:!"app-workorder/tpls/components/components-vue",propTitle:$t("stock_asset_management")},n.requirePage("app-workorder/tpls/components/components-vue",r));case 5:case"end":return t.stop()}},t)}))()},queryDeviceConfig:function(){return new Promise(function(e,r){o.FHHApi({url:"/EM1AESERVICE/DeviceService/queryDeviceConfig",data:{},success:function(t){t.Value&&"C120060000"==t.Value.errorCode?e(t.Value.data.deviceStatus):(o.remind(3,$t("获取状态失败")),r())}},{errorAlertModel:1})})},requirePage:function(t,e){var r=this;n.async("app-workorder/app.js",function(){n.async(["app-common-assets/style/common.css","app-standalone-assets/style/_ui.css","app-standalone-assets/style/all.css","app-workorder-assets/style/all.css"],function(){n.async(t,function(t){r.instance=new t({el:r.$el[0],propsData:e,hasBack:!1,propTitle:$t("stock.crm.setting.assetmanagement.assets_management")})})})})},destroy:function(){this.instance&&this.instance.$destroy&&this.instance.$destroy()}})});