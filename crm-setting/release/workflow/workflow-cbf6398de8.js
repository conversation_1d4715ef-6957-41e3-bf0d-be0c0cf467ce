define("crm-setting/workflow/action",["paas-workprocess/flowsetting"],function(a,e,o){function t(e){this.manageScope=e.manageScope}var r=CRM.util;a("paas-workprocess/flowsetting");_.extend(t.prototype,{constructor:t,add:function(){var t=this;t._onlyonce||(t._onlyonce=!0,t._checkQuota(function(e,o){t._onlyonce=!1,e?a.async("paas-workprocess/sdk",function(e){t.flowdetail&&t.flowdetail.destroy(),t.flowdetail=new e({quota:o}),t.flowdetail.on("refresh",function(e){t.manageScope.hasOwnProperty(e.entityId)?t.trigger("refresh"):t.trigger("refreshManageScope")})}):(e="\n                        <p>".concat($t("web.paas.flow.workprocess_add_allow_able_label"),'</p>\n                        <p style="color: #91959E;">').concat($t("web.paas.flow.workprocess_add_allow_able_tip"),"</p>"),FxUI.MessageBox.confirm(e,$t("提示"),{confirmButtonText:$t("确定"),showCancelButton:!1,dangerouslyUseHTMLString:!0}),FS.MEDIATOR.trigger("paas.workprocessoperate.load"))}))},copyadd:function(r){var s=this;this._checkQuota(function(e,o){var t;e?r&&(t=s,a.async("paas-workprocess/sdk",function(e){t.flowdetail&&t.flowdetail.destroy(),t.flowdetail=new e({isCopy:!0,sourceWorkflowId:r}),t.flowdetail.on("refresh",function(){t.trigger("refresh")})}),s.trigger("detailDestroy")):(e="\n                        <p>".concat($t("web.paas.flow.workprocess_add_allow_able_label"),'</p>\n                        <p style="color: #91959E;">').concat($t("web.paas.flow.workprocess_add_allow_able_tip"),"</p>"),FxUI.MessageBox.confirm(e,$t("提示"),{confirmButtonText:$t("确定"),showCancelButton:!1,dangerouslyUseHTMLString:!0}),FS.MEDIATOR.trigger("paas.workprocessoperate.load"))})},edit:function(o){var t;o&&((t=this).trigger("detailDestroy"),a.async("paas-workprocess/sdk",function(e){t.flowdetail&&t.flowdetail.destroy(),t.flowdetail=new e({sourceWorkflowId:o}),t.flowdetail.on("refresh",function(){t.trigger("refresh")})}))},delete:function(e){var o=this,t=r.confirm($t("确认删除该流程"),$t("删除流程"),function(){r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/DeleteDefinition",data:{sourceWorkflowId:e},success:function(e){0==e.Result.StatusCode?(e.Value.result?(r.remind(1,$t("操作成功")),o.trigger("refresh","delete")):r.alert($t("删除失败"),null,{title:$t("删除流程")}),t.hide()):(t.hide(),r.alert(e.Result.FailureMessage))}},{errorAlertModel:1,submitSelector:$(".b-g-btn",t.element)})});FS.MEDIATOR.trigger("paas.workprocessoperate.load"),r.uploadLog("s-Workflow","List",{operationId:"DeleteConfirm",eventType:"cl"})},start:function(e){var o=this;r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/EnableDefinition",data:{sourceWorkflowId:e,enabled:!0},success:function(e){0==e.Result.StatusCode?(r.remind(1,$t("操作成功")),o.trigger("refresh","startusing")):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1}),r.uploadLog("s-Workflow","List",{operationId:"ActiveConfirm",eventType:"cl"})},stop:function(e){var o=this,t=r.confirm($t("确定要停用这个流程吗？"),$t("停用流程"),function(){r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/EnableDefinition",data:{sourceWorkflowId:e,enabled:!1},success:function(e){0==e.Result.StatusCode?(o.trigger("refresh","blockup"),r.remind(1,$t("操作成功")),t.hide()):(t.hide(),r.alert(e.Result.FailureMessage))}},{errorAlertModel:1,submitSelector:$(".b-g-btn",t.element)})});FS.MEDIATOR.trigger("paas.workprocessoperate.load"),r.uploadLog("s-Workflow","List",{operationId:"Deactivate",eventType:"cl"})},_getDefinition:function(e,o){r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/GetDefinitionDetail",data:{sourceWorkflowId:e},success:function(e){0==e.Result.StatusCode&&o&&o(e.Value.workflow)}})},_checkQuota:function(o){r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/GetLicenseInfo",success:function(e){0==e.Result.StatusCode?o&&o(e.Value.allowable,e.Value):o&&o(!1)}})},destroy:function(){var o=this;_.each(["selectTpl","editTpl","editflow"],function(e){o[e]&&o[e].destroy&&o[e].destroy()}),this.off()}},Backbone.Events),o.exports=t});
define("crm-setting/workflow/detail",["./template/page-html","base-modules/ui/scrollbar/scrollbar","crm-modules/common/slide/slide","crm-widget/table/table"],function(n,e,t){var r=CRM.util,l=r.moment,a=n("./template/page-html"),o=n("base-modules/ui/scrollbar/scrollbar"),i=n("crm-modules/common/slide/slide"),s=(Table=n("crm-widget/table/table"),i.extend({events:{"click [data-action]":"_actionHandle","click .b-list":"toggleBtns","mouseleave .b-list":"toggleBtns","click .nav-item":"toggleNavigation"},options:{showMask:!1,width:800,zIndex:500,className:"crm-d-businessflow crm-d-detail crm-nd-detail workprocess-crm-detail",entry:"crm"},editFlowData:function(a){var o=this;r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/GetDefinitionDetail",data:{sourceWorkflowId:a},success:function(e){var t;0===e.Result.StatusCode?(i.prototype.show.apply(o),t=e.Value.workflow,o.sourceWorkflowId=a,_.each(t.workflow.activities,function(e){e.itemList&&e.itemList.length&&_.each(e.itemList,function(e){"updates"==e.taskType&&(e.updateFieldJson=JSON.parse(e.updateFieldJson))})}),o.data=t,o.render(),o.initScroll()):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},show:function(e){this.showCopy=e.showCopy,this.showEdit=e.showEdit,this.ruleList={},1!=this.page&&(this.page=1,this.toPage)&&this.toPage.reset(),this.editFlowData(e.sourceWorkflowId)},render:function(){var t=this;r.getUserGroups(function(e){t.groups=e,r.getUserRoles(function(e){t.roles=e;e=t.data.nameTranslateInfo&&t.data.nameTranslateInfo[Fx.userLanguage]||t.data.name;t.$el.html(a({objname:e,status:t.data.enable,list:t._getListData(),showCopy:t.showCopy,showEdit:t.showEdit,showRule:5===t.data.triggerTypes[0],controlStatus:t.data.controlStatus})),n.async("paas-workprocess/flowdetail",function(e){t.cavas=new e({el:t.$el.find(".svg"),data:t.data,useToolbar:!0}),t.cavas.show()}),$(".flow-detail",t.$el).toggle(!0),$(".flow-map",t.$el).toggle(!1),$(".flow-record",t.$el).toggle(!1),5===t.data.triggerTypes[0]&&t.initTimeRule(),t.initTrigger()})})},renderHistoryTable:function(){var o=this;o.historyTable=new Table({className:"crm-table historyTable",$el:o.$el.find(".flow-record .record-box"),requestType:"FHHApi",url:"/EM1HFLOW/DefinitionConfig/GetDefinitionHistoryBySrcId",showPage:!0,showMultiple:!1,hideIconSet:!0,search:{placeHolder:$t("flow.enter.version"),type:"workflowId",pos:"T"},paramFormat:function(e){return e=Object.assign(e,{type:"workflow",sourceWorkflowId:o.data.sourceWorkflowId})},formatData:function(e){return{totalCount:e.total,data:e.data}},columns:[{data:"workflowId",title:$t("版本号"),width:200},{data:"modifyTime",title:$t("修改时间"),width:200,dataType:4},{data:"modifier",title:$t("修改人"),width:160,render:function(e,t,a){return FS.contacts.getEmployeeById(e,{includeStop:!0,includeStopByRequest:!0})?FS.contacts.getEmployeeById(e,{includeStop:!0,includeStopByRequest:!0}).name:"--"}},{data:null,lastFixed:!0,title:$t("操作"),width:180,render:function(e,t,a,o,i){var n='<a class="view-detail" href="javascript:void(0)" data-id="'+a.workflowId+'">'+$t("流程图")+"</a>&nbsp;";return n+=a.ruleId?'<a href="javascript:void(0)" class="view-rule" data-ruleid="'+a.ruleId+'">'+$t("触发规则")+"</a>":""}}]}),o.historyTable.on("trclick",function(e,t,a){a.hasClass("view-detail")?o.rendLogDetail(a):a.hasClass("view-rule")&&o.showRule(a)}),o.historyTable._search.on("search",function(){window.Fx&&window.Fx.log("paas-flow-searchversion","cl",{module:"workflow"})})},initTrigger:function(){var t=this;t.propertyView&&t.propertyView.destroy(),n.async("paas-paasui/ui",function(){window.PaasUI.getComponent("FilterAnalyze").then(function(e){t.propertyView=new e({model:new Backbone.Model({fromApp:"workprocess",fromModule:"filter",originalConditions:t.data.rule,apiName:t.data.entityId,forDetail:!0,triggerNames:t.data.triggerNames})}),t.$el.find(".workprocess-trigger-list").empty().append(t.propertyView.$el)})})},initTimeRule:function(){var t=this;t.timeRuleView&&t.timeRuleView.destroy(),n.async("paas-workprocess/sdk",function(e){e.getModule("timeTrigger").then(function(e){t.timeRuleView=e.default.render({el:t.$el.find(".workprocess-triggerTime-list")[0],fromApp:"workprocess",ruleContent:t.data.rule.quartzRule,apiName:t.data.entityId})})})},showRule:function(e){var t=this,a=e.data("ruleid");if(t.ruleList[a])return t.showUnConditionDialog(t.ruleList[a],t.data.entityId),!1;r.FHHApi({url:"/EM1AFLOW/PaaS/GetHistoryRule",data:{flowType:"workflow",ruleId:a},success:function(e){e.Value.rule&&(t.ruleList[a]=e.Value.rule,t.showUnConditionDialog(t.ruleList[a],t.data.entityId))}},{errorAlertModel:1})},showUnConditionDialog:function(a,o){var i=this.data.triggerNames;return FxUI.create({template:'<fx-dialog\n\t\t\t\t\t\t:visible.sync="dialogVisible"\n            v-if="dialogVisible"\n\t\t\t\t\t\t:append-to-body="true"\n\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\tcustom-class="rule-condition-dialog"\n\t\t\t\t\t\t:title="$t(\'流程触发规则\')"\n\t\t\t\t\t\t@closed="handleClose">\n              <div class="ruleContent">\n                <div class="timeRuleContent" v-if="timeRule">\n                  <h3>'.concat($t("定时规则"),'</h3>\n                  <div class="content"></div>\n                </div>\n                <div class="filterAnalyzeContent" v-if="filterAnalyze">\n                  <h3>').concat($t("触发条件"),'</h3>\n                  <div class="content"></div>\n                </div>\n              </div>\n\t\t\t\t\t\t</fx-dialog>'),data:function(){return{dialogVisible:!0,timeRule:!1,filterAnalyze:!1}},mounted:function(){a.conditions&&0<a.conditions.length&&(this.filterAnalyze=!0,this.renderFilterContent()),a.quartzRule&&0<Object.keys(a.quartzRule).length&&(this.timeRule=!0,this.renderTimeRuleContent())},methods:{renderFilterContent:function(){var t=this;n.async("paas-paasui/ui",function(){window.PaasUI.getComponent("FilterAnalyze").then(function(e){t.logAnalyze=new e({model:new Backbone.Model({apiName:o,fromApp:"workprocess",fromModule:"filter",originalConditions:a,forDetail:!0,triggerNames:i})}),$(".filterAnalyzeContent .content").append(t.logAnalyze.$el)})})},renderTimeRuleContent:function(){var t=this;n.async("paas-workprocess/sdk",function(e){e.getModule("timeTrigger").then(function(e){t.timeRuleView=e.default.render({fromApp:"workprocess",ruleContent:a.quartzRule,apiName:o}),$(".timeRuleContent .content").append(t.timeRuleView.$el)})})}}})},_getListData:function(){var e=this,t=this.data;function a(e){return l(e).format("YYYY-MM-DD HH:mm")||"--"}var o=t.triggerNames instanceof Array?t.triggerNames.join(",")||"--":t.triggerNames||"--",i=e.data.nameTranslateInfo&&e.data.nameTranslateInfo[Fx.userLanguage]||e.data.name,e=e.data.descTranslateInfo&&e.data.descTranslateInfo[Fx.userLanguage]||e.data.description;return[{name:$t("工作流名称"),value:i||CRM.config.TEXT_DEFAULT},{name:$t("API名称"),value:t.sourceWorkflowId||CRM.config.TEXT_DEFAULT},{name:$t("描述"),value:e||CRM.config.TEXT_DEFAULT},{name:$t("状态"),value:t.enable?$t("启用"):$t("停用")},{name:$t("关联对象"),value:t.entityName||CRM.config.TEXT_DEFAULT},{name:$t("触发动作"),value:o||CRM.config.TEXT_DEFAULT},{name:$t("创建时间"),value:a(t.createTime)||CRM.config.TEXT_DEFAULT},{name:$t("最后修改人"),value:((i=(i=t.modifier)&&r.getEmployeeById(i))?i.fullName:"--")||"--"},{name:$t("最后修改时间"),value:a(t.modifyTime)||CRM.config.TEXT_DEFAULT}]},_actionHandle:function(e){var t,a=this.data,o=$(e.currentTarget).data("action");"hide"==o?this.hide():("toggle"==o&&(o=a.enable?"stop":"start"),this.createAction(function(e){switch(o){case"edit":case"copyadd":case"delete":t=a.sourceWorkflowId;break;case"stop":case"start":t=a.sourceWorkflowId}e[o]&&e[o](t)}))},_fetchLogDetail:function(a){r.FHHApi({url:"/EM1HPROCESS/WorkflowAction/GetDefinitionByWorkflowId",data:{workflowId:this.workflowId},success:function(e){var t;0===e.Result.StatusCode?(t=e.Value.workflow,_.each(t.workflow.activities,function(e){e.itemList&&e.itemList.length&&_.each(e.itemList,function(e){"updates"==e.taskType&&(e.updateFieldJson=JSON.parse(e.updateFieldJson))})}),a&&a(t)):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},rendLogDetail:function(e){var a=this;a.workflowId=e.attr("data-id"),a._fetchLogDetail(function(t){n.async("paas-workprocess/flowdetail",function(e){a.svgfull&&a.svgfull.destroy(),a.svgfull=new e({data:t,fullScreen:!0,useToolbar:!0}),a.svgfull.show()})})},createAction:function(t){var a=this;n.async("crm-modules/action/workprocess/workprocess",function(e){a.action||(a.action=new e,a.action.on("refresh",function(e){a.hide(),a.trigger("refresh",e)}),a.action.on("detailDestroy",function(){a.trigger("detailDestroy")})),t&&t(a.action)})},toggleBtns:function(e){"click"==e.type?$(e.currentTarget).toggleClass("active"):$(e.currentTarget).removeClass("active")},toggleNavigation:function(e){e.preventDefault();var e=$(e.currentTarget),t=$(".flow-map",this.$el),a=$(".flow-detail",this.$el),o=$(".flow-record",this.$el);e.hasClass("nav-selected")||(e.addClass("nav-selected").siblings().removeClass("nav-selected"),e=e.attr("data-name"),t.toggle(e===t.attr("data-name")),a.toggle(e===a.attr("data-name")),e===o.attr("data-name")?(o.toggle(!0),this.renderHistoryTable()):o.toggle(!1))},toggleContent:function(e){var e=$(e.currentTarget),t=$("h4 span",e);t.toggleClass("icon-arrow-b icon-arrow-r"),t.hasClass("icon-arrow-r")?e.next().slideUp():e.next().slideDown()},initScroll:function(){this.scroll&&this.scroll.destroy&&this.scroll.destroy(),this.scroll=new o($(".layout-scroll",this.$el))},destroy:function(){var e=this;e.action&&e.action.destroy&&e.action.destroy(),e.scroll&&e.scroll.destroy&&e.scroll.destroy(),e.record&&e.record.destroy&&e.record.destroy(),e.historyTable&&e.historyTable.destroy(),e.record=e.action=e.scroll=null,i.prototype.destroy.call(e)}}));t.exports=s});
define("crm-setting/workflow/template/page-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-d-layout"> <div class="header d-top"> <div class="crm-d-comp-tit"> <span class="d-obj-icon"></span> <span class="obj-name">' + ((__t = $t("工作流")) == null ? "" : __t) + '</span> <div class="tit"> <div title="' + __e(objname) + '">' + __e(objname) + '</div> </div> </div> <div class="operate"> <div class="crm-d-btn-operate"> ';
            if (controlStatus !== "controlled" && showEdit) {
                __p += ' <div class="crm-btn crm-btn-primary b-item b-edit" data-action="edit">' + ((__t = $t("编辑")) == null ? "" : __t) + "</div> ";
            }
            __p += ' <div class="b-item b-list"> <span class="crm-btn show-more">...</span> <!-- <span>' + ((__t = $t("更多")) == null ? "" : __t) + '</span> --> <ul class="ops-list"> <li class="opts-list-item" data-action="toggle">' + ((__t = status ? $t("停用") : $t("启用")) == null ? "" : __t) + "</li> ";
            if (showCopy && controlStatus !== "controlled") {
                __p += ' <li class="opts-list-item" data-action="copyadd">' + ((__t = $t("复制并新建")) == null ? "" : __t) + "</li> ";
            }
            __p += " ";
            if (!status && controlStatus !== "controlled") {
                __p += ' <li class="opts-list-item" data-action="delete">' + ((__t = $t("删除")) == null ? "" : __t) + "</li> ";
            }
            __p += ' </ul> </div> </div> </div> <div class="d-g-btns"> <span data-action="hide" class="h-btn" title="' + ((__t = $t("关闭")) == null ? "" : __t) + '"></span> </div> </div> <div class="workprocess-slide-navigation"> <div class="nav-item-group"> <span data-name="flow-detail" class="nav-item nav-selected">' + ((__t = $t("详细信息")) == null ? "" : __t) + '</span> <span data-name="flow-map" class="nav-item ">' + ((__t = $t("流程配置")) == null ? "" : __t) + '</span> <span data-name="flow-record" class="nav-item">' + ((__t = $t("历史版本")) == null ? "" : __t) + '</span> </div> </div> <div class="layout-scroll"> <div class="d-content"> <div class="d-container"> <div class="crm-d-comp-board"> <div class="b-content"> <div class="flow-map" data-name="flow-map"> <article class="field-items b-g-clear svg"></article> </div> <div class="flow-detail" data-name="flow-detail"> <div class="sec-tit"> <h3><span class="icon icon-info"></span>' + ((__t = $t("详细信息")) == null ? "" : __t) + '</h3> </div> <div class="field-items b-g-clear"> <div class="crm-d-comp-infolist"> ';
            _.each(list, function(item, index) {
                __p += " ";
                if (index % 2 == 0) {
                    __p += ' <div class="i-item b-g-clear"> ';
                }
                __p += ' <div class="' + ((__t = index % 2 == 0 ? "i-l" : "i-r") == null ? "" : __t) + '"> <div class="i-wrap"> <span class="tit" title="' + ((__t = item.name) == null ? "" : __t) + '">' + ((__t = item.name) == null ? "" : __t) + '</span> <div class="con">' + __e(item.value) + "</div> </div> </div> ";
                if (index % 2 == 1 || index == list.length - 1) {
                    __p += " </div> ";
                }
                __p += " ";
            });
            __p += ' <div class="i-line"></div> </div> </div> ';
            if (showRule) {
                __p += ' <div class="sec-tit"> <h3><span class="icon icon-info"></span>' + ((__t = $t("定时规则")) == null ? "" : __t) + '</h3> </div> <div class="workprocess-triggerTime-list"></div> ';
            }
            __p += ' <div class="sec-tit"> <h3><span class="icon icon-info"></span>' + ((__t = $t("触发条件")) == null ? "" : __t) + '</h3> </div> <div class="workprocess-trigger-list"></div> </div> <div class="flow-record " data-name="flow-record"> <div class="history-tit"> <h3>' + ((__t = $t("历史版本")) == null ? "" : __t) + '</h3> </div> <div class="record-box"> </div> </div> </div> </div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/workflow/template/record-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="record-log"> <div class="record-item-list r-list"> ';
            if (!data.length) {
                __p += ' <div class="tip">' + ((__t = $t("暂无历史版本")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            _.each(data, function(obj) {
                __p += ' <div class="o-item"> <div class="o-l"> <span class="l-tit"><span class="circle"></span><span>' + ((__t = obj.date) == null ? "" : __t) + "</span></span> <span>" + ((__t = obj.time) == null ? "" : __t) + '</span> </div> <div class="o-r"> <div class="r-tit"> <div class="img-wrap" ';
                if (obj.empId > 0) {
                    __p += 'data-cardid="' + ((__t = obj.empId) == null ? "" : __t) + '"';
                }
                __p += " > ";
                if (obj.headImg) {
                    __p += ' <img src="' + ((__t = obj.headImg) == null ? "" : __t) + '"/> ';
                }
                __p += ' </div> <div class="o-name">' + __e(obj.name) + '</div> </div> <!-- <span class="r-content">' + __e(obj.recordText) + '</span> --> <div class="revise-o-btn"> ';
                if (obj.ruleId) {
                    __p += ' <a href="javascript:void(0);" class="none view-rule" data-ruleid="' + ((__t = obj.ruleId) == null ? "" : __t) + '">' + ((__t = $t("查看流程触发条件")) == null ? "" : __t) + "</a> ";
                }
                __p += ' <a class="none view-detail" href="javascript:void(0);" data-id="' + ((__t = obj.workflowId) == null ? "" : __t) + '">' + ((__t = $t("查看详情")) == null ? "" : __t) + '</a> </div> <!--<a class="view-detail j-log-detail" href="javascript:void(0);" data-id="' + ((__t = obj.workflowId) == null ? "" : __t) + '">' + ((__t = $t("查看详情")) == null ? "" : __t) + '</a>--> <!--<div class="j-detail-sec log-detail-con"></div>--> </div> </div> ';
            });
            __p += " </div> ";
            if (data.length) {
                __p += ' <div class="pagination"></div> ';
            }
            __p += ' <!-- <div class="record-log-pagination"> <a href="javascript:void(0);" class="record-btn prev disabled">上一页</a> <a href="javascript:void(0);" class="record-btn next">下一页</a> </div> --> </div>';
        }
        return __p;
    };
});
define("crm-setting/workflow/whitelistConfigureDialog/addWhiteListDialog/addWhiteListDialog",["crm-widget/dialog/dialog","crm-widget/select/select","../template/tpl-html"],function(t,e,i){var l=CRM.util,n=t("crm-widget/dialog/dialog");Select=t("crm-widget/select/select"),Tpl=t("../template/tpl-html"),i.exports=n.extend({attrs:{title:"",width:500,showBtns:!0,btnName:{save:$t("保存"),cancel:$t("取消")},content:'<div class="white-list-content"></div>'},events:{"click .b-g-btn-cancel":"_closeHandle","click .dialog-btns .b-g-btn":"onSave"},initObjectSelect:function(){var i=this,t=(this.entityIdList||[]).map(function(t){return{value:t.objApiName,name:t.displayName}});i.objectSelect=new Select({$wrap:$(this.element).find(".j-object-select"),multiple:"single",placeHolder:$t("请选择"),options:t,defaultVal:[]}),i.objectSelect.on("change",function(t,e){i.getPara(t)})},initCountSelect:function(){this.countSelect=new Select({$wrap:$(this.element).find(".j-count-select"),multiple:"multiple",onlyoneline:!0,options:[],defaultVal:""})},getPara:function(e){var i=this;l.FHHApi({url:"/EM1HPROCESS/ConfigAction/FieldWhiteFieldDescsForWhiteSelect",data:{entityId:e},success:function(t){0==t.Result.StatusCode&&(t=Object.values(t.Value).map(function(t){return{name:t.label,value:t.apiName}}),i.countSelect.resetOptions(t),t=i.optionsData.find(function(t){return t.entityId===e}),i.countSelect.setValue(t?t.fieldApiNames:[]))}})},show:function(t,e){this.entityIdList=t,this.optionsData=e||[];t=this,e=n.superclass.show.call(this);return $(this.element).find(".white-list-content").html(Tpl()),t.isLoading=!1,t.initObjectSelect(),t.initCountSelect(),e},initTable:function(t){this._initTable=t},setDefaultValue:function(t){var e=this;e.objectSelect.setValue(t.entityId),e.objectSelect.disable(),e.getPara(t.entityId)},onSave:function(t){var e=this;return e.objectSelect.val&&0==e.objectSelect.val.length?(FxUI.MessageBox.alert($t("关联对象不能为空！"),$t("提示"),{confirmButtonText:$t("确定")}),!1):0<e.objectSelect.val.length&&e.countSelect.val&&0==e.countSelect.val.length?(FxUI.MessageBox.alert($t("统计或引用字段不能为空！"),$t("提示"),{confirmButtonText:$t("确定")}),!1):void(e.isLoading||(e.isLoading=!0,$(e.element).find(".b-g-btn").prepend('<i class="el-icon-loading whitelist"></i>'),l.FHHApi({url:"/EM1HPROCESS/ConfigAction/FieldWhiteUpdate",data:{entityId:e.objectSelect.val[0],fieldApiNames:e.countSelect.val},success:function(t){0==t.Result.StatusCode?(e._initTable&&e._initTable(),e.hide()):l.alert(t.Result.FailureMessage)},complete:function(){e.isLoading=!1,$(e.element).find(".whitelist").remove()}})))},_closeHandle:function(){this.hide()},destroy:function(){var t=this;return t.objectSelect&&t.objectSelect.destroy(),t.countSelect&&t.countSelect.destroy(),i.exports.superclass.destroy.call(this)}})});
define("crm-setting/workflow/whitelistConfigureDialog/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="content"> <div class="check-obj-box"> <span class="select-label">' + ((__t = $t("关联对象")) == null ? "" : __t) + '</span> <div class="j-object-select"></div> </div> <div class="check-obj-box"> <span class="select-label">' + ((__t = $t("统计或引用字段")) == null ? "" : __t) + '</span> <div class="j-count-select"></div> </div> </div>';
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var c,a={},t=Object.prototype,u=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",i=e.toStringTag||"@@toStringTag";function l(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{l({},"")}catch(c){l=function(t,e,n){return t[e]=n}}function s(t,e,n,r){var i,o,a,s,e=e&&e.prototype instanceof f?e:f,e=Object.create(e.prototype);return l(e,"_invoke",(i=t,o=n,a=new x(r||[]),s=1,function(t,e){if(3===s)throw Error("Generator is already running");if(4===s){if("throw"===t)throw e;return{value:c,done:!0}}for(a.method=t,a.arg=e;;){var n=a.delegate;if(n){n=function t(e,n){var r=n.method,i=e.i[r];if(i===c)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=c,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;r=h(i,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,d;i=r.arg;return i?i.done?(n[e.r]=i.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=c),n.delegate=null,d):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}(n,a);if(n){if(n===d)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===s)throw s=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=3;n=h(i,o,a);if("normal"===n.type){if(s=a.done?4:2,n.arg===d)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(s=4,a.method="throw",a.arg=n.arg)}}),!0),e}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}a.wrap=s;var d={};function f(){}function o(){}function p(){}var e={},g=(l(e,r,function(){return this}),Object.getPrototypeOf),g=g&&g(g(_([]))),y=(g&&g!==t&&u.call(g,r)&&(e=g),p.prototype=f.prototype=Object.create(e));function m(t){["next","throw","return"].forEach(function(e){l(t,e,function(t){return this._invoke(e,t)})})}function w(a,s){var e;l(this,"_invoke",function(n,r){function t(){return new s(function(t,e){!function e(t,n,r,i){var o,t=h(a[t],a,n);if("throw"!==t.type)return(n=(o=t.arg).value)&&"object"==_typeof(n)&&u.call(n,"__await")?s.resolve(n.__await).then(function(t){e("next",t,r,i)},function(t){e("throw",t,r,i)}):s.resolve(n).then(function(t){o.value=t,r(o)},function(t){return e("throw",t,r,i)});i(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function v(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=c,t[4]=e}function x(t){this.tryEntries=[[-1]],t.forEach(v,this),this.reset(!0)}function _(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(u.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=c,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return l(y,"constructor",o.prototype=p),l(p,"constructor",o),o.displayName=l(p,i,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,l(t,i,"GeneratorFunction")),t.prototype=Object.create(y),t},a.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,n,function(){return this}),a.AsyncIterator=w,a.async=function(t,e,n,r,i){void 0===i&&(i=Promise);var o=new w(s(t,e,n,r),i);return a.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},m(y),l(y,i,"Generator"),l(y,r,function(){return this}),l(y,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=_,x.prototype={constructor:x,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){o.type="throw",o.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var i=this.tryEntries[r],o=i[4],a=this.prev,s=i[1],u=i[2];if(-1===i[0])return t("end"),!1;if(!s&&!u)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=a){if(a<s)return this.method="next",this.arg=c,t(s),!0;if(a<u)return t(u),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}var o=(i=i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]?null:i)?i[4]:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i[2],d):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),b(n),d}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,i=this.tryEntries[e];if(i[0]===t)return"throw"===(n=i[4]).type&&(r=n.arg,b(i)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:_(t),r:e,n:n},"next"===this.method&&(this.arg=c),d}},a}function asyncGeneratorStep(t,e,n,r,i,o,a){try{var s=t[o](a),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,i)}function _asyncToGenerator(s){return function(){var t=this,a=arguments;return new Promise(function(e,n){var r=s.apply(t,a);function i(t){asyncGeneratorStep(r,e,n,i,o,"next",t)}function o(t){asyncGeneratorStep(r,e,n,i,o,"throw",t)}i(void 0)})}}define("crm-setting/workflow/whitelistConfigureDialog/whitelistConfigureDialog",["crm-widget/table/table","crm-widget/dialog/dialog","./addWhiteListDialog/addWhiteListDialog"],function(t,e,n){var r=CRM.util,i=t("crm-widget/table/table"),o=t("crm-widget/dialog/dialog");AddWhiteListDialog=t("./addWhiteListDialog/addWhiteListDialog"),n.exports=o.extend({attrs:{title:$t("白名单配置"),width:850,height:500,showBtns:!0,showOkBtn:!1,className:"white-list-dialog",btnName:{cancel:$t("关闭")}},events:{"click .j-addnew":"onAdd","click .b-g-btn-cancel":"_closeHandle"},initialize:function(){o.prototype.initialize.call(this,{showOkBtn:!1})},_initTable:function(){var r=this;r.dt&&r.dt.destroy(),r.dt=new i({$el:$(".dialog-con",r.element),className:"crm-table white-list",title:$t("统计或引用字段白名单"),requestType:"FHHApi",url:"/EM1HPROCESS/ConfigAction/FieldWhiteList",showSize:!1,showPage:!0,pageType:"pageNumber",noDataTip:$t("暂无数据"),height:200,scrollLoadY:!0,operate:{btns:[{text:$t("添加"),className:"j-addnew",order:1}]},columns:[{data:"displayName",title:$t("对象"),width:200},{data:"countNames",title:$t("统计或引用字段"),width:300,render:function(t,e,n){if(t instanceof Array)return'<div title="'+(t=t.filter(function(t){return!!t}).join(",")||"--")+'" >'+t+"</div>"}},{data:"modifyTime",title:$t("最后修改时间"),width:150,dataType:4},{data:"operate",title:$t("操作"),width:50,render:function(t,e,n){return'<a href="javascript:;" data-operate="edit" >'+$t("编辑")+"</a>"+('<a href="javascript:;" data-operate="delete" >'+$t("删除")+"</a>")}}],formatData:function(t){var e=t.data;return r.optionsData=e,_.each(e,function(n){n.countNames=[],_.each(n.fieldApiNames,function(t,e){n.countNames[e]=n.fieldDesc[t]&&n.fieldDesc[t].label})}),{totalCount:t.totalCount,data:e}}}),$(".dt-caption .dt-tit",r.dt.$el).append("<div style='color:#91959e;top:25px;font-size:10px' class='tit-title'></div>"),$(".tit-title").html($t("仅配置的统计或引用字段，才能触发工作流")),r.dt.on("trclick",function(t,e,n){n=n.data("operate");"edit"==n?r.editHandle(t):"delete"==n&&r.deleteHandle(t)})},getAllObject:function(){return new Promise(function(e){r.FHHApi({url:"/EM1HPROCESS/MetadataAction/FindCustomObjs",data:{includeFieldsDesc:!1,packageName:"CRM"},success:function(t){0==t.Result.StatusCode&&e(t.Value.customObjects)}})})},show:function(){var n=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return result=o.superclass.show.call(n),e=n,t.next=4,e.getAllObject();case 4:return e.entityIdList=t.sent,e._initTable(),t.abrupt("return",result);case 7:case"end":return t.stop()}},t)}))()},onAdd:function(){this.addWhiteListDialog||(this.addWhiteListDialog=new AddWhiteListDialog({title:$t("添加统计或引用字段白名单")}),this.addWhiteListDialog.initTable(this._initTable.bind(this))),this.addWhiteListDialog.show(this.entityIdList,this.optionsData)},editHandle:function(t){this.editWhiteListDialog||(this.editWhiteListDialog=new AddWhiteListDialog({title:$t("编辑统计或引用字段白名单")}),this.editWhiteListDialog.initTable(this._initTable.bind(this))),this.editWhiteListDialog.show(this.entityIdList,this.optionsData),this.editWhiteListDialog.setDefaultValue(t)},deleteHandle:function(t){var e=this;FxUI.MessageBox.confirm($t("确定删除吗"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消")}).then(function(){r.FHHApi({url:"/EM1HPROCESS/ConfigAction/FieldWhiteDelete",data:{entityIds:new Array(t.entityId)},success:function(t){0==t.Result.StatusCode&&e.refreshTable()}})})},refreshTable:function(){this._initTable()},_closeHandle:function(){this.hide()},destroy:function(){var t=this;return t.dt&&t.dt.destroy(),t.addWhiteListDialog&&t.addWhiteListDialog.destroy(),t.editWhiteListDialog&&t.editWhiteListDialog.destroy(),n.exports.superclass.destroy.call(this)}})});
define("crm-setting/workflow/workflow",[],function(e,n,a){var r=Backbone.View.extend({initialize:function(e){var n=this,e=(n.setElement(e.wrapper),n.$el.addClass("crm-workProcess-table"),document.createElement("div"));e.className="workProcess-manage",n.$el[0].appendChild(e)},render:function(){var a=this;e.async(["paas-paasui/vui","paas-workprocess/sdk"],function(e,n){n.getModule("manageList").then(function(n){new Vue({el:a.$el.children(".workProcess-manage")[0],render:function(e){return e(n.default)}})})})}});a.exports=r});