define("crm-setting/contractconfigure/config",[],function(t,e,c){c.exports={CONFIG_DATA:[{domId:"level0_1",moduleId:"SaleContract",title:$t("crm.SaleContractObj",null,"销售合同"),moduleList:[{title:$t("开启销售合同开关"),type:"switch",key:"sale_contract",setConfigParam:2,enableClose:!1,describeList:[{title:$t("先开订单新建布局")}]},{title:$t("crm.salecontractobjconstraint.settings.title",null,"合同约束开关"),type:"switch",key:"contract_constraint_mode",setConfigParam:2,enableClose:!1,isShow:function(t){return t.sale_contract&&CRM.util.isGrayScale("CRM_SALECONTRACT_CONSTRAINT")},describeList:[{title:$t("crm.salecontractobjconstraint.settings.desc1"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc1_1")}]},{title:$t("crm.salecontractobjconstraint.settings.desc2"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc2_1")},{title:$t("crm.salecontractobjconstraint.settings.desc2_2")}]},{title:$t("crm.salecontractobjconstraint.settings.desc3"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc3_1")}]}],children:[{title:"",type:"checkbox",value:[!1],key:["contract_constraint_pricebook_available_range"],enableClose:!1,setConfigParam:2,isShow:function(t){return t.contract_constraint_mode},options:function(t){var e=[],c=t.available_range,t=t[28],c=[c?$t("crm.AvailableRangeObj"):"",t?$t("crm.PriceBookObj"):""].filter(function(t){return t});return c.length&&(t=c.join($t("crm.salecontractobjconstraint.settings.label_opts_and",null,"和")),e.push({key:"contract_constraint_pricebook_available_range",label:$t("crm.salecontractobjconstraint.settings.label_opts_prefix",{name:t},"合同约束")})),e}}]},{title:$t("sfa.crm.setting_tradeconfigure.contract_connector_title"),type:"SaleContractConnector",key:"contract_connector",desc:[$t("sfa.crm.setting_tradeconfigure.contract_connector_desc")],isShow:!1},{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_title"),key:"is_open_additional_contract",enableClose:!1,describeList:[{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc1"),list:[{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc2")},{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc3")}]}],isShow:function(t){return t.sale_contract}},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_title"),key:"sale_contract_record_type_mapping",type:"ContractMainSubFlag",desc:[{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc1"),list:[{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc2")},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc3")},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc4")}]}],isShow:function(t){return t.is_open_additional_contract}},{title:$t("sfa.crm.setting_tradeconfigure.contract_mapping_title"),key:"contract_mapping",type:"ContractMapping",desc:[$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc1"),$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc2"),$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc3")],isShow:function(t){return t.is_open_additional_contract}},{title:$t("sfa.crm.setting_tradeconfigure.is_open_contract_progress_title"),describeList:[$t("sfa.crm.setting_tradeconfigure.is_open_contract_progress_desc1")],key:"is_open_contract_progress",type:"switch",value:!1,enableClose:!1,isShow:function(t,e){t=t.sale_contract,e=e.contract_progress_management_app;return t&&e},children:[{title:"",key:"open_contract_progress_rule",type:"OpenContractProgressRule",isShow:function(t){return t.is_open_contract_progress}}]}]}],KEY_CONFIG:{sale_contract:{cache_key:"sale_contract"},contract_connector:{type:"string"},sale_contract_record_type_mapping:{type:"jsonstring"}}}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var f,a={},t=Object.prototype,c=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",e=r.asyncIterator||"@@asyncIterator",o=r.toStringTag||"@@toStringTag";function s(t,r,e,n){return Object.defineProperty(t,r,{value:e,enumerable:!n,configurable:!n,writable:!n})}try{s({},"")}catch(f){s=function(t,r,e){return t[r]=e}}function u(t,r,e,n){var o,i,a,u,r=r&&r.prototype instanceof l?r:l,r=Object.create(r.prototype);return s(r,"_invoke",(o=t,i=e,a=new _(n||[]),u=1,function(t,r){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw r;return{value:f,done:!0}}for(a.method=t,a.arg=r;;){var e=a.delegate;if(e){e=function t(r,e){var n=e.method,o=r.i[n];if(o===f)return e.delegate=null,"throw"===n&&r.i.return&&(e.method="return",e.arg=f,t(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;n=h(o,r.i,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,p;o=n.arg;return o?o.done?(e[r.r]=o.value,e.next=r.n,"return"!==e.method&&(e.method="next",e.arg=f),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}(e,a);if(e){if(e===p)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;e=h(o,i,a);if("normal"===e.type){if(u=a.done?4:2,e.arg===p)continue;return{value:e.arg,done:a.done}}"throw"===e.type&&(u=4,a.method="throw",a.arg=e.arg)}}),!0),r}function h(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var p={};function l(){}function i(){}function y(){}var r={},g=(s(r,n,function(){return this}),Object.getPrototypeOf),g=g&&g(g(x([]))),d=(g&&g!==t&&c.call(g,n)&&(r=g),y.prototype=l.prototype=Object.create(r));function m(t){["next","throw","return"].forEach(function(r){s(t,r,function(t){return this._invoke(r,t)})})}function v(a,u){var r;s(this,"_invoke",function(e,n){function t(){return new u(function(t,r){!function r(t,e,n,o){var i,t=h(a[t],a,e);if("throw"!==t.type)return(e=(i=t.arg).value)&&"object"==_typeof(e)&&c.call(e,"__await")?u.resolve(e.__await).then(function(t){r("next",t,n,o)},function(t){r("throw",t,n,o)}):u.resolve(e).then(function(t){i.value=t,n(i)},function(t){return r("throw",t,n,o)});o(t.arg)}(e,n,t,r)})}return r=r?r.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var r=t[4]||{};r.type="normal",r.arg=f,t[4]=r}function _(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function x(r){if(null!=r){var e,t=r[n];if(t)return t.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return e=-1,(t=function t(){for(;++e<r.length;)if(c.call(r,e))return t.value=r[e],t.done=!1,t;return t.value=f,t.done=!0,t}).next=t}throw new TypeError(_typeof(r)+" is not iterable")}return s(d,"constructor",i.prototype=y),s(y,"constructor",i),i.displayName=s(y,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,s(t,o,"GeneratorFunction")),t.prototype=Object.create(d),t},a.awrap=function(t){return{__await:t}},m(v.prototype),s(v.prototype,e,function(){return this}),a.AsyncIterator=v,a.async=function(t,r,e,n,o){void 0===o&&(o=Promise);var i=new v(u(t,r,e,n),o);return a.isGeneratorFunction(r)?i:i.next().then(function(t){return t.done?t.value:i.next()})},m(d),s(d,o,"Generator"),s(d,n,function(){return this}),s(d,"toString",function(){return"[object Generator]"}),a.keys=function(t){var r,e=Object(t),n=[];for(r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},a.values=x,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=f,this.done=!1,this.delegate=null,this.method="next",this.arg=f,this.tryEntries.forEach(b),!t)for(var r in this)"t"===r.charAt(0)&&c.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=f)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function t(t){i.type="throw",i.arg=r,e.next=t}for(var n=e.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,u=o[1],c=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=f,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=r&&r<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=r,o?(this.method="next",this.next=o[2],p):this.complete(i)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),p},finish:function(t){for(var r=this.tryEntries.length-1;0<=r;--r){var e=this.tryEntries[r];if(e[2]===t)return this.complete(e[4],e[3]),b(e),p}},catch:function(t){for(var r=this.tryEntries.length-1;0<=r;--r){var e,n,o=this.tryEntries[r];if(o[0]===t)return"throw"===(e=o[4]).type&&(n=e.arg,b(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,r,e){return this.delegate={i:x(t),r:r,n:e},"next"===this.method&&(this.arg=f),p}},a}function asyncGeneratorStep(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(r,e){var n=u.apply(t,a);function o(t){asyncGeneratorStep(n,r,e,o,i,"next",t)}function i(t){asyncGeneratorStep(n,r,e,o,i,"throw",t)}o(void 0)})}}define("crm-setting/contractconfigure/contractconfigure",["../promotionrebate/promotionrebate","./config"],function(t,r,e){var i=t("../promotionrebate/promotionrebate").Base,t=t("./config"),n=t.CONFIG_DATA,o=t.KEY_CONFIG;e.exports=i.extend({getLicenseKeys:function(){return["contract_progress_management_app"]},getConfigData:function(){return n},getConfigKeyData:function(){return o},beforeSetConfig:function(e,t){var n=arguments,o=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var r;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i.prototype.beforeSetConfig.apply(o,n);case 2:if(r=t.sent,"sale_contract"===e)return t.next=6,CRM.util.getEditLayoutStatus("SalesOrderObj");t.next=10;break;case 6:if(t.sent){t.next=9;break}throw new Error($t("请先开启{{apiName}}新建布局",{apiName:CRM.config.objDes["SalesOrderObj".toLowerCase()].name}));case 9:r.confirmInfo=$t("确定要开启销售合同吗");case 10:return t.abrupt("return",r);case 11:case"end":return t.stop()}},t)}))()},afterSetConfig:function(t){t=t.update;"is_open_additional_contract"===this.currentChangeKey&&t(["sale_contract_record_type_mapping"])}}),e.exports.config=t});