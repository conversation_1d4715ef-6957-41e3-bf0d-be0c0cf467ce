define("crm-setting/promotionrebate/common/config",[],function(e,i,t){t.exports={CONFIG_DATA:[{domId:"level0",moduleId:"advancedPricing",title:$t("高级定价",null,"价格政策"),licenseKey:"advanced_pricing_app",moduleList:[{type:"switch",title:$t("开启高级定价",null,"开启价格政策"),key:"price_policy",dependKeys:["promotion_status","28"],value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("高级定价开关",null,"价格政策开关")}],children:[{_id:"price_policy_objects",key:"price_policy_objects",title:"",type:"PricePolicyObject",dependKeys:["price_policy"],isShow:function(e){return e.price_policy}}]},{title:$t("sfa.crm.price_policy_config.multiple_object"),type:"switch",key:"multiple_object_price_policy",isShow:function(e){return e.price_policy&&CRM.util.isGrayScale("CRM_POLICY_MULTIPLE_OBJECT")},value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("sfa.crm.price_policy_config.multiple_object_desc")}]},{title:$t("允许切换政策："),type:"checkbox",key:["allow_switch_master_price_policy","allow_switch_detail_price_policy"],isShow:function(e){return e.price_policy},value:[!1,!1],options:[{key:"allow_switch_master_price_policy",label:$t("整单促:主对象上匹配的价格政策允许手工切换或取消")},{key:"allow_switch_detail_price_policy",label:$t("产品促:从对象上匹配的价格政策允许手工切换或取消")}],describeList:[{title:$t("注意:如果有限额限量控制，禁止切换可能导致无法下单")}]},{title:$t("crm.price_policy_config.percentile_gift"),key:"price_policy_support_percentile_gift",isShow:function(e){return e.price_policy},type:"switch",value:!1,enableClose:!1,describeList:[{title:$t("crm.price_policy_config.percentile_gift_no")},{title:$t("crm.price_policy_config.percentile_gift_yes")}]},{title:$t("sfa.crm.promotionrebate.price_policy_unit_title"),key:"price_policy_unit",type:"PricePolicyUnit",isShow:function(e){return e.price_policy&&e.multiple_unit}},{title:$t("赠品费用分摊依据："),key:"gift_amortize_basis",isShow:function(e){return e.price_policy},type:"radio",value:"price_book_price",radioOptions:[{label:$t("价目表价格"),value:"price_book_price"},{label:$t("产品档案价格"),value:"product_price"}]},{title:$t("赠品参与分摊："),type:"radio",key:"gift_attend_amortize",isShow:function(e){return e.price_policy},value:"0",radioOptions:[{value:"0",label:$t("否:赠品的价值全部分摊到本品行，赠品最终【销售单价】为0、【费用分摊后小计】即为赠品自己的价值")},{value:"1",label:$t("是:赠品的价值分摊到本品行和赠品行，赠品最终【销售单价】为0、【费用分摊后小计】为赠品自己的价值扣除分摊值的部分")}]},{title:$t("crm.price_policy_config.gift_range_title"),type:"radio",key:"enable_gift_range_shelves",isShow:function(e){return e.price_policy},value:"0",radioOptions:[{value:"0",label:$t("crm.price_policy_config.gift_range_no")},{value:"1",label:$t("crm.price_policy_config.gift_range_yes")}]},{title:$t("crm.price_policy_config.stacked_discount_title"),type:"switch",enableClose:!1,key:"discount_on_discount",isShow:function(e){return e.price_policy&&CRM.util.isGrayScale("CRM_POLICY_STACKED_DISCOUNT")},value:!1,setConfigParam:2,describeList:[{title:$t("crm.price_policy_config.stacked_discount_no")},{title:$t("crm.price_policy_config.stacked_discount_yes")}]},{title:$t("crm.price_policy_config.match_mode"),key:"match_mode",isShow:function(e){return e.price_policy},type:"radio",value:"immediately",radioOptions:[{label:$t("crm.price_policy_config.match_immediately"),value:"immediately"},{label:$t("crm.price_policy_config.match_once"),value:"once"}]},{type:"checkbox",title:$t("选产品页，各产品显示的内容"),key:["show_price_policy_name"],isShow:function(e){return e.price_policy},value:[!1],options:[{label:$t("移动端显示政策名称"),key:"show_price_policy_name"}],describeList:[{title:$t("移动端选产品页，各产品将展示“全部政策名称”，建议创建价格政策时，只创建一条价格规则（或者多条规则的产品范围完全相同），将政策名称置为具体的促销信息，（如：满200减20）起到在选产品时可以查看产品的具体促销信息的作用。")}]},{title:$t("crm.price_policy_config.dep"),key:"price_policy_dept_not_this",isShow:function(e){return e.price_policy},type:"radio",value:"0",radioOptions:[{label:$t("crm.price_policy_config.dep_self"),value:"0"},{label:$t("crm.price_policy_config.dep_all"),value:"1"}]}]},{domId:"level1",moduleId:"rebate",title:$t("返利单"),licenseKey:"rebate_app",moduleList:[{title:$t("开启返利单"),type:"switch",key:"rebate",value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("返利单政策开关，开启后不可关闭。")}]},{title:$t("crm.setting.promotionrebate.rebate_policy_source_title",null,"返利产生来源"),key:"rebate_policy_source",isShow:function(e){return e.rebate},type:"RebatePolicySource"},{title:$t("sfa.crm.setting.promotionrebate.rebate_product_range_shelves",null,"返利品校验可售范围"),key:"rebate_product_range_shelves",isShow:function(e){return e.rebate},type:"radio",value:"0",radioOptions:[{label:$t("sfa.crm.rebate_config.product_range_no"),value:"0"},{label:$t("sfa.crm.rebate_config.product_range_yes"),value:"1"}]}]},{domId:"level2",moduleId:"coupon",title:$t("优惠券"),licenseKey:"coupon_app",moduleList:[{title:$t("开启优惠券"),type:"switch",key:"coupon",value:!1,enableClose:!1,setConfigParam:2,describeList:[{title:$t("优惠券政策开关，开启后不可关闭。")}]},{title:$t("启用纸质券业务"),key:"paper_coupon",value:!1,enableClose:!1,setConfigParam:2,isShow:CRM.util.isGrayScale("CRM_PAPER_COUPON")}]}],KEY_CONFIG:{price_policy:{cache_key:"advancedPricing"},multiple_object_price_policy:{type:"boolean"},28:{type:"string"},promotion_status:{type:"string"},clone_history_order_product:{type:"string"},gift_amortize_basis:{cache_key:"giftAmortizeBasis",type:"string"},gift_attend_amortize:{cache_key:"giftAttendAmortize",type:"string"},enable_gift_range_shelves:{type:"string"},show_price_policy_name:{cache_key:"showPricePolicyName"},allow_switch_master_price_policy:{cache_key:"allowSwitchMasterPricePolicy"},allow_switch_detail_price_policy:{cache_key:"allowSwitchDetailPricePolicy"},price_policy_support_percentile_gift:{type:"boolean"},match_mode:{type:"string"},price_policy_dept_not_this:{type:"string"},discount_on_discount:{type:"boolean"},gift_fixed_attribute:{type:"string"},coupon:{cache_key:"openCoupon"},paper_coupon:{cache_key:"openPaperCoupon"},rebate:{cache_key:"openRebate"},rebate_policy_source:{type:"string"},price_policy_unit:{type:"string"},rebate_product_range_shelves:{type:"string"}}}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},e=Object.prototype,c=e.hasOwnProperty,t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",r=t.asyncIterator||"@@asyncIterator",o=t.toStringTag||"@@toStringTag";function f(e,t,r,n){return Object.defineProperty(e,t,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(s){f=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o,i,a,u,t=t&&t.prototype instanceof y?t:y,t=Object.create(t.prototype);return f(t,"_invoke",(o=e,i=r,a=new w(n||[]),u=1,function(e,t){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===e)throw t;return{value:s,done:!0}}for(a.method=e,a.arg=t;;){var r=a.delegate;if(r){r=function e(t,r){var n=r.method,o=t.i[n];if(o===s)return r.delegate=null,"throw"===n&&t.i.return&&(r.method="return",r.arg=s,e(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;n=l(o,t.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,h;o=n.arg;return o?o.done?(r[t.r]=o.value,r.next=t.n,"return"!==r.method&&(r.method="next",r.arg=s),r.delegate=null,h):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}(r,a);if(r){if(r===h)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;r=l(o,i,a);if("normal"===r.type){if(u=a.done?4:2,r.arg===h)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(u=4,a.method="throw",a.arg=r.arg)}}),!0),t}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}a.wrap=u;var h={};function y(){}function i(){}function p(){}var t={},g=(f(t,n,function(){return this}),Object.getPrototypeOf),g=g&&g(g(C([]))),d=(g&&g!==e&&c.call(g,n)&&(t=g),p.prototype=y.prototype=Object.create(t));function m(e){["next","throw","return"].forEach(function(t){f(e,t,function(e){return this._invoke(t,e)})})}function v(a,u){var t;f(this,"_invoke",function(r,n){function e(){return new u(function(e,t){!function t(e,r,n,o){var i,e=l(a[e],a,r);if("throw"!==e.type)return(r=(i=e.arg).value)&&"object"==_typeof(r)&&c.call(r,"__await")?u.resolve(r.__await).then(function(e){t("next",e,n,o)},function(e){t("throw",e,n,o)}):u.resolve(r).then(function(e){i.value=e,n(i)},function(e){return t("throw",e,n,o)});o(e.arg)}(r,n,e,t)})}return t=t?t.then(e,e):e()},!0)}function b(e){this.tryEntries.push(e)}function _(e){var t=e[4]||{};t.type="normal",t.arg=s,e[4]=t}function w(e){this.tryEntries=[[-1]],e.forEach(b,this),this.reset(!0)}function C(t){if(null!=t){var r,e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return r=-1,(e=function e(){for(;++r<t.length;)if(c.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=s,e.done=!0,e}).next=e}throw new TypeError(_typeof(t)+" is not iterable")}return f(d,"constructor",i.prototype=p),f(p,"constructor",i),i.displayName=f(p,o,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===i||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,p):(e.__proto__=p,f(e,o,"GeneratorFunction")),e.prototype=Object.create(d),e},a.awrap=function(e){return{__await:e}},m(v.prototype),f(v.prototype,r,function(){return this}),a.AsyncIterator=v,a.async=function(e,t,r,n,o){void 0===o&&(o=Promise);var i=new v(u(e,t,r,n),o);return a.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},m(d),f(d,o,"Generator"),f(d,n,function(){return this}),f(d,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,r=Object(e),n=[];for(t in r)n.unshift(t);return function e(){for(;n.length;)if((t=n.pop())in r)return e.value=t,e.done=!1,e;return e.done=!0,e}},a.values=C,w.prototype={constructor:w,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=s)},stop:function(){this.done=!0;var e=this.tryEntries[0][4];if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function e(e){i.type="throw",i.arg=t,r.next=e}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,u=o[1],c=o[2];if(-1===o[0])return e("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=s,e(u),!0;if(a<c)return e(c),!1}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===e||"continue"===e)&&o[0]<=t&&t<=o[2]?null:o)?o[4]:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o[2],h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r[2]===e)return this.complete(r[4],r[3]),_(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r,n,o=this.tryEntries[t];if(o[0]===e)return"throw"===(r=o[4]).type&&(n=r.arg,_(o)),n}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={i:C(e),r:t,n:r},"next"===this.method&&(this.arg=s),h}},a}function asyncGeneratorStep(e,t,r,n,o,i,a){try{var u=e[i](a),c=u.value}catch(e){return void r(e)}u.done?t(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(u){return function(){var e=this,a=arguments;return new Promise(function(t,r){var n=u.apply(e,a);function o(e){asyncGeneratorStep(n,t,r,o,i,"next",e)}function i(e){asyncGeneratorStep(n,t,r,o,i,"throw",e)}o(void 0)})}}function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/promotionrebate/components/Base",[],function(e,t,r){r.exports=Backbone.View.extend({initialize:function(e){e=e.wrapper;this.setElement(e),this.wContent=null,this.pricePolicyObjects=[]},render:function(){this.initMainContentView()},initMainContentView:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){t.wContent=FxUI.create(t.getMainContentInitOptions(e.default))})})},getMainContentInitOptions:function(e){var f=this,c=f.getConfigKeyData(),t=f.getConfigData();return{wrapper:this.$el[0],template:'\n                    <backstage\n                        ref="rBackstage"\n                        :data="cConfigData"\n                        :changeLoading="changeLoading"\n                        @change="handleChange"\n                        @update="(keys) => getConfig(keys, true)"\n                    />\n                ',components:{Backstage:e},data:function(){return{configData:t,licenses:{},keyValues:{},oriKeyValues:{},changeLoading:!1}},computed:{cConfigData:function(){var s=this;return this.configData.map(function(e){function t(){return(0<arguments.length&&void 0!==arguments[0]?arguments[0]:[]).map(function(e){return e.moduleList&&e.moduleList.length?_objectSpread(_objectSpread({},e),{},{moduleList:t(e.moduleList)}):c(e)}).filter(function(e){return e.moduleList||e.isShow})}var c=function(e){e.key;var t=e.value,r=e.isShow,n=e.radioOptions,n=void 0===n?[]:n,o=e.options,o=void 0===o?[]:o,i=e.depKeys,i=void 0===i?[]:i,a=Array.isArray(e.key)?e.key.map(function(e){return s.keyValues[e]}):s.keyValues[e.key],a=Object.keys(s.keyValues).length?a:t,t=i.reduce(function(e,t){return e[t]=s.keyValues[t],e},{}),i=void 0===r||(_.isFunction(r)?e.isShow(s.keyValues,s.licenses):r),r=n.map(function(e){var t=e.disabled,t=void 0!==t&&(_.isFunction(t)?t(s.keyValues):t);return _objectSpread(_objectSpread({},e),{},{disabled:t})}),n=_.isFunction(o)?o(s.keyValues):o,o=(e.children||[]).map(function(e){return c(e)}).filter(function(e){return e.isShow}),u=f.getExtraStatus&&f.getExtraStatus(e.key,s.oriKeyValues);return _objectSpread(_objectSpread({},e),{},{isShow:i,value:a,depValues:t,radioOptions:r,options:n,children:o,extraStatus:u})},r=!e.licenseKey||s.licenses[e.licenseKey];return _objectSpread(_objectSpread({},e),{},{visible:r,moduleList:t(e.moduleList)})})}},created:function(){this.getLicenses(),this.getConfig()},methods:{getKeyValues:function(){return this.keyValues},transValue:function(e,t,r){var n=(c[e]||{}).type,o=t;return"string"===n?o=t:"jsonstring"===n?o=r?JSON.stringify(t):JSON.parse(t):"boolean"!==n&&n||(o=r?t?"1":"0":"1"===t),f.transValue(e,o,r)},handleChange:function(a){var u=this;return _asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,o,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=f.handleModuleItemChange(a),t=r.key,r=r.value,u.oriKeyValues.hasOwnProperty(t),n=u.keyValues[t],f.currentChangeKey=t,u.changeLoading=!0,e.prev=5,i=u.transValue(t,r,!0),e.next=9,f.beforeSetConfig(t,i);case 9:if((o=e.sent).confirmInfo)return e.next=13,f.confirm(o.confirmInfo);e.next=13;break;case 13:return e.next=15,f.setConfig(o,t);case 15:u.keyValues=_objectSpread(_objectSpread({},u.keyValues),{},_defineProperty({},t,r)),u.oriKeyValues=_objectSpread(_objectSpread({},u.oriKeyValues),{},_defineProperty({},t,r)),CRM.util.remind(1,$t("操作成功")),f.afterSetConfig({update:function(e){return u.getConfig.call(u,e,!0)}}),e.next=29;break;case 21:if(e.prev=21,e.t0=e.catch(5),u.keyValues=_objectSpread(_objectSpread({},u.keyValues),{},_defineProperty({},t,n)),"confirmCancel"===e.t0)return e.abrupt("return");e.next=27;break;case 27:i=(null==e.t0?void 0:e.t0.message)||("string"==typeof e.t0?e.t0:$t("操作失败!")),CRM.util.alert(i);case 29:u.changeLoading=!1,f.currentChangeKey=null;case 31:case"end":return e.stop()}},e,null,[[5,21]])}))()},commonSetConfig:function(){var e;return(e=this.$refs.rBackstage).commonSetConfig.apply(e,arguments)},getConfig:function(r,a){var u=this;return _asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,o,i;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.getConfig(r,a);case 3:t=e.sent,o={},i={},t.forEach(function(e){var t=e.key,e=e.value,r=(c[t]||{}).cache_key,n=u.transValue&&u.transValue(t,e,!1);r&&(CRM._cache[r]=n),a?(u.$set(u.keyValues,t,n),u.$set(u.oriKeyValues,t,e)):(o[t]=n,i[t]=e)}),a||(u.keyValues=o,u.oriKeyValues=i),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(0),CRM.util.remind(3,e.t0||$t("crm.setting.tradeconfigure.init_config_failed",null,"初始化开关失败！"));case 13:case"end":return e.stop()}},e,null,[[0,10]])}))()},getLicenses:function(){var e=this,t=this.configData.map(function(e){return e.licenseKey}).filter(function(e){return e}),r=f.getLicenseKeys();t.push.apply(t,_toConsumableArray(r)),t.length&&CRM.api.get_licenses({key:t,cb:function(){e.licenses=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}}})}}}},getLicenseKeys:function(){return[]},getConfigData:function(){return[]},getConfigKeyData:function(){return{}},getConfigKeyDataByKey:function(t){var r,n,e=this.getConfigData();return t?(r=[],(n=function(e){e.forEach(function(e){e.moduleList&&e.moduleList.length?n(e.moduleList):(r.push(e),e.children&&e.children.length&&r.push.apply(r,_toConsumableArray(e.children)))})})(e),r.find(function(e){return Array.isArray(e.key)?e.key.includes(t):e.key===t})||{}):e},getKeyValues:function(e){var t=(null==(t=this.wContent)?void 0:t.getKeyValues())||{};return e?t[e]:t},getOKeyValues:function(e){var t=(null==(t=this.wContent)?void 0:t.oriKeyValues)||{};return e?t[e]:t},transValue:function(e,t,r){return t},handleModuleItemChange:function(e){return e},beforeSetConfig:function(u,c){var s=this;return _asyncToGenerator(_regeneratorRuntime().mark(function e(){var t,r,n,o,i,a;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i=s.getConfigKeyDataByKey(u),t=i.title,r=i.enableClose,n=i.enableOpen,o=i.setUrl,i=void 0===(i=i.setConfigParam)?1:i,a=function(){var e=t?'"'.concat(t,'"'):"";return!1===r?$t("crm.setting.tradeconfigure.confirm_open_notclose",{name:e}):!1===n?$t("crm.setting.tradeconfigure.confirm_close_notopen",{name:e}):null}(),1===i)return e.abrupt("return",{url:o||"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:u,value:c},confirmInfo:a});e.next=5;break;case 5:return e.abrupt("return",{url:o||"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{tenantId:CRM.enterpriseId,moduleCode:u,openStatus:c},confirmInfo:a});case 6:case"end":return e.stop()}},e)}))()},setConfig:function(e){return this.commonSetConfigFetch(e).then(function(e){return _.isEmpty(e.Value)||e.Value.success&&0===e.Value.errCode?e:Promise.reject(new Error(e.Value.errMessage))})},afterSetConfig:function(){},getConfig:function(e){return this.getConfigValues(e)},commonSetConfigFetch:function(e){var t=e.url,r=e.data,n=e.otherOpts,n=void 0===n?{}:n,e=e.logData,e=void 0===e?{}:e;return CRM.util.showLoading_tip(),this.wContent.commonSetConfig({url:t,data:r},n,e).finally(function(){CRM.util.hideLoading_tip()})},fetch:function(e){var n=e.url,o=e.data,i=e.otherOpts;return new Promise(function(t,r){CRM.util.showLoading_tip(),CRM.util.FHHApi({url:n,data:o,success:function(e){CRM.util.hideLoading_tip(),0===e.Result.StatusCode?t(e):r(new Error(null==e||null==(e=e.Result)?void 0:e.FailureMessage))},error:function(){CRM.util.hideLoading_tip(),r(new Error($t("网络异常，请稍后重试")))}},_objectSpread({errorAlertModel:1},i))})},confirm:function(n){var o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(e,t){var r=CRM.util.confirm(n,$t("提示"),function(){r.hide(),e()},_objectSpread({cancelBack:function(){r.hide(),t("confirmCancel")}},o))})},getConfigValues:function(e){return Promise.resolve(CRM.util.getConfigValues(e))},destroy:function(){var e;null!=(e=this.wContent)&&e.destroy()}})});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var r,n=Object.keys(e);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(e),t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)),n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){_defineProperty(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _defineProperty(t,e,r){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0===r)return("string"===e?String:Number)(t);r=r.call(t,e||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function f(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(s){f=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o,i,a,u,e=e&&e.prototype instanceof y?e:y,e=Object.create(e.prototype);return f(e,"_invoke",(o=t,i=r,a=new w(n||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:s,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===s)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=s,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;n=l(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,p;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=s),r.delegate=null,p):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,p)}(r,a);if(r){if(r===p)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;r=l(o,i,a);if("normal"===r.type){if(u=a.done?4:2,r.arg===p)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(u=4,a.method="throw",a.arg=r.arg)}}),!0),e}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var p={};function y(){}function i(){}function h(){}var e={},g=(f(e,n,function(){return this}),Object.getPrototypeOf),g=g&&g(g(j([]))),b=(g&&g!==t&&c.call(g,n)&&(e=g),h.prototype=y.prototype=Object.create(e));function d(t){["next","throw","return"].forEach(function(e){f(t,e,function(t){return this._invoke(e,t)})})}function v(a,u){var e;f(this,"_invoke",function(r,n){function t(){return new u(function(t,e){!function e(t,r,n,o){var i,t=l(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&c.call(r,"__await")?u.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):u.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function m(t){this.tryEntries.push(t)}function _(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function w(t){this.tryEntries=[[-1]],t.forEach(m,this),this.reset(!0)}function j(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(c.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return f(b,"constructor",i.prototype=h),f(h,"constructor",i),i.displayName=f(h,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,f(t,o,"GeneratorFunction")),t.prototype=Object.create(b),t},a.awrap=function(t){return{__await:t}},d(v.prototype),f(v.prototype,r,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new v(u(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},d(b),f(b,o,"Generator"),f(b,n,function(){return this}),f(b,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=j,w.prototype={constructor:w,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(_),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,u=o[1],c=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=s,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),_(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,_(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:j(t),r:e,n:r},"next"===this.method&&(this.arg=s),p}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=u.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var r;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(r="Object"===(r={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function _iterableToArrayLimit(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/promotionrebate/promotionrebate",["./components/Base","./common/config"],function(t,e,r){var i=t("./components/Base"),t=t("./common/config"),n=t.CONFIG_DATA,o=t.KEY_CONFIG;r.exports=i.extend({getConfigData:function(){return n},getConfigKeyData:function(){return o},getConfig:function(t){return Promise.all([this.getConfigValues(t),this.getRebatePolicySourceConfig(),this.getPolicyObjectConfig()]).then(function(t){var t=_slicedToArray(t,3),e=t[0],r=t[1],t=t[2],e=e.map(function(t){var e=t.key;return e===r.key?r:{key:e,value:t.value}});return e.push({key:"price_policy_objects",value:t}),e})},getRebatePolicySourceConfig:function(){return this.getConfigValues(["rebate_policy_source"]).then(function(t){return _slicedToArray(t,1)[0]})},getPolicyObjectConfig:function(){var r=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,r.fetch({url:"/EM1HNCRM/API/v1/object/price_policy/service/findObjectsByModule",data:{module:"price_policy"}});case 3:return e=t.sent,t.abrupt("return",(null==e?void 0:e.Value)||[]);case 7:t.prev=7,t.t0=t.catch(0),CRM.util.alert($t("网络异常，请稍后重试"));case 10:case"end":return t.stop()}},t,null,[[0,7]])}))()},getExtraStatus:function(t,e){switch(t){case"price_policy_objects":var r={price_policy_objects:((null==e?void 0:e.price_policy_objects)||[]).map(function(t){return _objectSpread(_objectSpread({},t),{},{value:null==e?void 0:e["price_policy_".concat(t.apiName)]})})};return r.gift_fixed_attribute=null==e?void 0:e.gift_fixed_attribute,r;case"rebate":r=Array.isArray(t)?t.map(function(t){return e[t]}):e[t]||null;return["2","3"].includes(r)?{isDisable:!0,isWaiting:"2"===r,statusInfo:"2"===r?$t("crm.setting.promotionrebate.rebate.open_tip1",null,"返利开启中，开启时间较长，请等待CRM通知"):$t("crm.setting.promotionrebate.rebate.open_tip2",null,"开启失败，请查看CRM通知")}:null;default:return null}},beforeSetConfig:function(r,t){var n=arguments,o=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i.prototype.beforeSetConfig.apply(o,n);case 2:if(e=t.sent,"price_policy"===r)return t.next=6,o.isPricePolicyAllowed();t.next=8;break;case 6:t.next=18;break;case 8:if(!o.pricePolicyObjects.includes(r)){t.next=13;break}if(CRM._cache.advancedPricing){t.next=11;break}throw new Error($t("请先开启高级定价"));case 11:t.next=18;break;case 13:if("enforce_price_policy_priority"!==r){t.next=18;break}if(CRM._cache.advancedPricing){t.next=17;break}throw CRM.util.alert($t("请先开启高级定价")),new Error;case 17:e.confirmInfo=CRM._cache.openEnforcePricePolicy?$t("确认关闭高级定价优先级吗"):$t("确认开启高级定价优先级吗");case 18:return t.abrupt("return",e);case 19:case"end":return t.stop()}},t)}))()},isPricePolicyAllowed:function(){var o=this;return new Promise(function(t,e){var r=$t("crm.advancedPricingOpenFailureTitle",null,"[高级定价]开关，开启失败！"),n=[];"1"!==o.getKeyValues("28")?e(new Error($t("请先开启价目表"))):("2"===o.getKeyValues("promotion_status")&&n.push("[".concat($t("促销设置"),"]")),n.length?(r=r+"<br/>"+$t("crm.propertiesOpenFailedInfo",null,"失败原因：当前租户已开启")+n.join($t("和")),e(new Error(r))):t())})}}),r.exports.Base=i,r.exports.config=t});