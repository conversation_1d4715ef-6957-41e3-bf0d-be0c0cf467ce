define("crm-setting/businesslink/businesslink",["./components/Medical-Industry","./components/Manufacturing-Dms","./components/Fmc-Industry","./components/industryToggle","./components/industryToggle-dms","./template/businesslink-html"],function(n,e,t){var s=FS.crmUtil,o=n("./components/Medical-Industry"),r=n("./components/Manufacturing-Dms"),i=n("./components/Fmc-Industry"),u=n("./components/industryToggle"),d=n("./components/industryToggle-dms"),c=n("./template/businesslink-html"),n=Backbone.View.extend({has_more_industry:!1,currentIndustryComp:null,initialize:function(n){this.setElement(n.wrapper)},render:function(){var e=this;return this.$el.html(c()),this.getExitLicense(["manufacturing_dms_app","medical_device_dms_industry"]).then(function(n){e.has_more_industry=!!n.length,e.has_more_industry&&n.includes("manufacturing_dms_app")?(e.renderIndustryToggleCompDms(),e.handleToggleComponentsEvent("ManufacturingUniversalDms")):e.has_more_industry&&n.includes("medical_device_dms_industry")?(e.renderIndustryToggleComp(),e.handleToggleComponentsEvent("MedicalIndustry")):e.handleToggleComponentsEvent("FmcIndustry")}).catch(function(){e.handleToggleComponentsEvent("FmcIndustry")}),this},getExitLicense:function(n){return new Promise(function(e,t){s.FHHApi({url:"/EM1HNCRM/API/v1/object/ca_license/service/get_exist_licenses",data:{licenses:n},success:function(n){0===n.Result.StatusCode?e(n.Value.existLicenses):t()}})})},renderIndustryToggleCompDms:function(){var n=Vue.extend(d);this.IndustryToggleComp=new n({el:".crm-businesslink-header-slot"}),this.IndustryToggleComp.$on("toggle-components",this.handleToggleComponentsEvent.bind(this))},renderIndustryToggleComp:function(){var n=Vue.extend(u);this.IndustryToggleComp=new n({el:".crm-businesslink-header-slot"}),this.IndustryToggleComp.$on("toggle-components",this.handleToggleComponentsEvent.bind(this))},handleToggleComponentsEvent:function(n){switch(this.currentIndustryComp&&this.currentIndustryComp instanceof Vue?(this.currentIndustryComp.$destroy(),this.currentIndustryComp.$el.innerHTML=""):this.currentIndustryComp&&this.currentIndustryComp instanceof Backbone.View&&(this.currentIndustryComp.stopListening(),this.currentIndustryComp.undelegateEvents(),this.currentIndustryComp.$el.empty()),n){case"ManufacturingUniversalDms":var e=Vue.extend(r);this.currentIndustryComp=new e({el:".industry-component-slot",propsData:{has_more_industry:this.has_more_industry}});break;case"MedicalIndustry":e=Vue.extend(o);this.currentIndustryComp=new e({el:".industry-component-slot",propsData:{has_more_industry:this.has_more_industry}});break;case"FmcIndustry":$(this.$(".industry-component-slot")[0]).attr("id","fmc-industry-component"),this.currentIndustryComp=new i({wrapper:this.$(".industry-component-slot")})}},destroy:function(){this.IndustryToggleComp&&this.IndustryToggleComp.$destroy()}});t.exports=n});
define("crm-setting/businesslink/components/commonDialog",["crm-modules/common/util","crm-widget/dialog/dialog"],function(e,t,i){var o=e("crm-modules/common/util"),n=e("crm-widget/dialog/dialog").extend({attrs:{content:'<div class="switch-text"></div>',title:$t("提示"),width:500,showBtns:!0,showScroll:!1,classPrefix:"crm-c-dialog businesslink-dialog"},className:"businesslink-dialog",events:{"click .b-g-btn-cancel":"_hide","click .b-g-btn":"_submit"},_submit:function(){var e=this.type,t=this.value;"switchLevel"===e&&this.switchDistribution(t),"setStrategy"!==e&&"oneKeySet"!==e||this.setStrategy(t)},switchDistribution:function(e){var t=this;o.showLoading(!0),o.FHHApi({url:"/EM1HWaiQinV2/businessService/saveBusinessInfo",data:{distributionMode:e},success:function(e){e.Result;0===e.Value.errorCode&&(t.showLoading(!1),t.trigger("switchLevelCommit"))},complete:function(){t._hide()}})},setStrategy:function(t){var i=this,s=this.type,e={};(e="oneKeySet"==s?(i.trigger("showBtnLoading",t),i.element.hide(),$(".ui-mask").hide(),{type:"up"==t?1:2,syncType:0}):(o.showLoading(!0),{syncType:t})).crmCloseOldCategory=CRM._cache.close_old_category,o.FHHApi({url:"/EM1HWaiQinV2/tradeThroughService/sendGatherSettting",data:e,success:function(e){e.Result;e=e.Value;"oneKeySet"!==s&&o.showLoading(!1),0===e.errorCode?"oneKeySet"==s?i.trigger("oneKeySetCommit",t):i.trigger("setStrategyCommit",t):"oneKeySet"==s?i.trigger("getNewConfig",e.message):(i._hide(),FxUI.MessageBox.confirm(e.message,$t("提示"),{dangerouslyUseHTMLString:!0,showConfirmButton:!1,showClose:!1,cancelButtonText:$t("我知道了"),type:"error"}).catch(function(e){}))}})},log:function(e,t,i){_.isString(e)&&(e=e.split("_")),i=i||"Trade_Through",t=t||"",FS.util.crmLog(e[2],{biz:i,module:e[0],subModule:e[1],eventType:t,eventId:e.join("_")})},show:function(e,t,i){var s=this,o=n.superclass.show.call(this);return s.type=e,s.value=i,s.$(".switch-text").text(t),"switchLevel"===e&&"TWO_LEVEL"==i&&s.log(["businesslink","seconddealer","set"],"cl"),"oneKeySet"===e&&s.log(["businesslink","onekey","set"],"cl"),"setStrategy"===e&&s.log(["businesslink",{1:"uploadcustomer",2:"uploadorder",3:"uploadwarehouse",4:"uploadinventory",5:"uploadinvoice",6:"uploadcheckin",13:"uploadproduct",14:"uploadsize",7:"downcustomer",8:"downproduct",9:"downunit",10:"downcommodity",11:"downsize",12:"downcontact"}[i],"set"],"cl"),o},_hide:function(){return this.trigger("hide"),n.superclass.hide.call(this)},destroy:function(){return n.superclass.destroy.call(this)}});i.exports=n});
define("crm-setting/businesslink/components/Fmc-Industry",["crm-modules/common/util","./setStrategy","../template/page-html"],function(t,e,i){var o=t("crm-modules/common/util"),n=t("./setStrategy"),l=t("../template/page-html"),s=Backbone.View.extend({is_buy:!1,level_id:0,strategyConfig:{},distribution_level_desc:[{id:0,title1:$t("一级经销"),desc1:[$t("渠道模式：品牌商>>经销商"),$t("使用场景：由经销商向门店销售或配送")],title2:$t("在此模式下"),desc2:[$t("创建「客户-经销商」时，可快捷添加二者的互联关系并为经销商开通企业帐号")]},{id:1,title1:$t("二级经销"),desc1:[$t("渠道模式：品牌商 >>经销商>>二批商"),$t("使用场景：经销商可以向门店和二批商销售和配送，二批商仅可以向门店销售或者配送")],title2:$t("在此模式下"),desc2:[$t("创建「客户-二批商」时，可快速添加二批商与品牌高的互联关系、二批商与经销商的互联关系，并为二批商商开通企业帐号")]}],initialize:function(e){var t=this;this.setElement(e.wrapper),this.getLevelConfig().then(function(){t.render(),FS.util.crmLog("view",{biz:"Trade_Through",module:"businesslink",subModule:"page",eventType:"pv",eventId:"businesslink_page_view"})})},events:{"click .switch-level":"showDialogHandle"},getLevelConfig:function(){var s=this;return new Promise(function(n,e){CRM.util.FHHApi({url:"/EM1HWaiQinV2/ruleWebServiceV2/getEaInfo",data:{},success:function(e){var t,i=e.Result,e=e.Value;0===i.StatusCode?(t=e.distributionMode,e=e.isTradeThrough,s.is_buy=1===e,s.level_id=t,n(s.level_id)):o.alert(i.FailureMessage)}},{errorAlertModel:1})})},getStrategyConfig:function(){var n=this;return new Promise(function(i,e){CRM.util.FHHApi({url:"/EM1HWaiQinV2/tradeThroughService/getGatherSettting",data:{},success:function(e){var t=e.Result,e=e.Value;0===t.StatusCode&&0===e.errorCode?(t=e.tradeThroughConfig,n.strategyConfig=t,i(n.strategyConfig)):o.alert(e.message)}},{errorAlertModel:1})})},render:function(){var e,t=this;t.isShowProductCategory=CRM._cache.close_old_category,t.is_buy?(t.$el.html(l({is_buy:t.is_buy,level_id:t.level_id,distribution_level_desc:t.distribution_level_desc,isShowProductCategory:t.isShowProductCategory})),t.renderTable()):(e=this.getlangsetimg(),t.$el.html(l({is_buy:t.is_buy,img1:e[0],img2:e[1],isShowProductCategory:t.isShowProductCategory})),t.$(".crm-tit").hide())},getlangsetimg:function(){var e,t=FS.contacts.getCurrentEmployee().language,t="zh-CN"===t||"zh-TW"===t?(e=CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy1-68c0b0e133.png",CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy11-302f1860c1.png"):"ja-JP"===t?(e=CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy3-a36122a3b6.png",CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy33-bcd3d2408b.png"):(e=CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy2-93c6a4b316.png",CRMSETTING.ASSETS_PATH+"/images/businesslink/no-buy22-c02f720eb9.png");return[e,t]},renderTable:function(e){var t=this;_.isEmpty(e)?t.getStrategyConfig().then(function(e){t._renderTable(e)}):t._renderTable(e)},_renderTable:function(e){var t=this,i=n("down",e),e=n("up",e);t.downStrategy=new i,t.downStrategy.$mount(".down-strategy"),t.upStrategy=new e,t.upStrategy.$mount(".up-strategy")},showDialogHandle:function(e){var i=this,n=$(e.currentTarget).data("level"),e=[{type:"switchLevel",value:"ONE_LEVEL",text:$t("二级切换成一级后，已经增加的字段不减少，经销商将不能向二批商销售产品，历史数据不处理。确定切换为一级经销吗？")},{type:"switchLevel",value:"TWO_LEVEL",text:$t("一级切换成二级后，支持经销商向二批商销售产品，历史数据不处理。确定切换为二级经销吗？")}][n],s=e.type,o=e.text,r=e.value;t.async("./components/commonDialog",function(e){var t=this._commonDialog=new e;t.show(s,o,r),t.on("switchLevelCommit",function(){t.destroy(),FxUI.Message({message:$t("切换")+$t("成功"),type:"success"}),i.level_id=n,i.$el.html(l({is_buy:i.is_buy,distribution_level_desc:i.distribution_level_desc,level_id:i.level_id,isShowProductCategory:i.isShowProductCategory})),i.renderTable(i.strategyConfig)}),t.on("hide",function(){t.destroy()})})},destroy:function(){this.is_buy&&this.el.remove(".new-strategy-box")}});i.exports=s});
define("crm-setting/businesslink/components/industryToggle-dms",[],function(t,n,e){e.exports={template:'\n\t<div class="crm-businesslink-header">\n\t\t<div class="crm-businesslink-header_title">{{i18nLabel.header}}</div>\n\t\t<div class="industry-toggle-wrap">\n\t\t\t<fx-cascader size="small" :options="options" v-model="currentValue" @change="handleChange">\n\t\t\t\t<fx-button\n\t\t\t\t  size="small"\n\t\t\t\t  type="primary"\n\t\t\t\t  plain\n\t\t\t\t  round\n\t\t\t\t  :disabled="disabled"\n\t\t\t\t  :loading="loading"\n\t\t\t\t  slot="trigger"\n\t\t\t\t>{{currentLabel}} <span class="fx-icon-arrow-down"></span></fx-button>\n\t\t\t</fx-cascader>\n\t\t</div>\n\t</div>',name:"IndustryToggle",props:{displayValue:{type:Array,default:function(){return["ManufacturingUniversalDms"]}}},data:function(){return{options:[{value:"ManufacturingUniversalDms",label:$t("crm_businesslink_manufacturing_dms")},{value:"FmcIndustry",label:$t("crm.businesslink.header_fmc_industry")}],currentValue:["ManufacturingUniversalDms"],i18nLabel:{header:$t("crm.businesslink.header_title")}}},computed:{currentLabel:function(){var n=this;return this.options.find(function(t){return t.value===n.currentValue[0]}).label}},mounted:function(){this.currentValue=this.displayValue},destroyed:function(){},methods:{handleChange:function(t){this.$emit("toggle-components",t[0])}}}});
define("crm-setting/businesslink/components/industryToggle",[],function(t,n,e){e.exports={template:'\n\t\t<div class="crm-businesslink-header">\n\t\t    <div class="crm-businesslink-header_title">{{i18nLabel.header}}</div>\n            <div class="industry-toggle-wrap">\n\t\t    \t<fx-cascader size="small" :options="options" v-model="currentValue" @change="handleChange">\n\t\t    \t\t<fx-button\n\t\t    \t\t  size="small"\n\t\t    \t\t  type="primary"\n\t\t    \t\t  plain\n\t\t    \t\t  round\n\t\t    \t\t  :disabled="disabled"\n\t\t    \t\t  :loading="loading"\n\t\t    \t\t  slot="trigger"\n\t\t    \t\t>{{currentLabel}} <span class="fx-icon-arrow-down"></span></fx-button>\n\t\t    \t</fx-cascader>\n\t\t    </div>\n\t\t</div>',name:"IndustryToggle",props:{displayValue:{type:Array,default:function(){return["MedicalIndustry"]}}},data:function(){return{options:[{value:"MedicalIndustry",label:$t("crm.businesslink.header_medical_industry")},{value:"FmcIndustry",label:$t("crm.businesslink.header_fmc_industry")}],currentValue:["MedicalIndustry"],i18nLabel:{header:$t("crm.businesslink.header_title")}}},computed:{currentLabel:function(){var n=this;return this.options.find(function(t){return t.value===n.currentValue[0]}).label}},mounted:function(){this.currentValue=this.displayValue},destroyed:function(){},methods:{handleChange:function(t){this.$emit("toggle-components",t[0])}}}});
define("crm-setting/businesslink/components/Manufacturing-Dms",[],function(t,e,n){var s=FS.crmUtil;n.exports={template:'<div class="industry-component-slot" id="medical-industry-component" v-loading.fullscreen.lock="loading" :fx-loading-text="i18nLabel.loading" fx-loading-background="rgba(255, 255, 255, 0.8)">\n\t\t\t<div class="medical-industry-stepOne" v-if="curView===\'stepOne\'">\n\t\t\t    <div class="img-wrap" :style="{height:stepOneHeight}">\n\t\t\t\t    <img :src="image" />\n\t\t\t\t</div>\n\t\t\t    <div class="bottom">\n\t\t\t\t     <fx-button size="small" type="primary" @click=\'openMedicalDMS\'>{{i18nLabel.startNow}}</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="medical-industry-stepTwo" v-else-if="curView === \'stepTwo\' ">\n\t\t\t    <div class="medical-industry-stepTwo-textWrap">\n\t\t\t\t    <h3>{{i18nLabel.desc1}}</h3>\n\t\t\t\t    <p>{{i18nLabel.desc2}}</p>\n\t\t\t\t\t<p>{{i18nLabel.desc3}}</p>\n\t\t\t\t\t<p>{{i18nLabel.desc4}}</p>\n\t\t\t\t</div>\n\t\t\t\t<div class="params-block">\n\t\t\t\t    <div class="params-title">{{i18nLabel.planStatus}}</div>\n\t\t\t\t\t<div class="params-desc">\n\t\t\t\t\t    <div style="font-size: 14px;"><span class="fx-icon-chenggong"></span> {{i18nLabel.enabled}}</div>\n\t\t\t\t\t\t<div class="solution-tip">{{i18nLabel.planDesc1}} <fx-link :underline="false" type="standard" style="font-size:12px" @click="toDMS">{{i18nLabel.planDesc2}}</fx-link> {{i18nLabel.planDesc3}}</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\n              <fx-dialog class="dht-init-dialog" \n                :visible.sync="visible" \n                width="1000px" min-height="600px"\n                :append-to-body="true" \n                class="medical-industry-component"\n                :close-on-press-escape="false" \n                :close-on-click-modal="false" \n                :show-close="false"\n                :showHeader="false" \n                :showFooter="false"\n                >\n                    <div class="dht-init-underway-dms">\n\t\t\t\t\t\t <div class="layout-l-1"  v-if="step === \'1\'">\n\t\t\t\t\t\t\t<h1>{{i18nLabel.dht_dms_crm_anufacturing_dms}}</h1>\n\t\t\t\t\t\t\t<p>{{i18nLabel.dht_dms_crm_manufacturing_dms_desc}}</p>\n\t\t\t\t\t\t\t<div class="capabilities-section">\n\t\t\t\t\t\t\t<h3>{{i18nLabel.dht_dms_general_capabilities}}</h3>\n\t\t\t\t\t\t\t<div class="capability-row">\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{ i18nLabel.dht_dms_crm_channel_management }}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{ i18nLabel.dht_dms_channel_mpowerment}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{ i18nLabel.dht_dms_crm_inventory_management}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="capability-row">\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_crm_merchant_center}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_after_sales_service}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_distribution_management}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="capability-row">\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_transaction_management}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_channel_incentive}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_channel_policy}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="medical-section" v-if="hasLisense">\n\t\t\t\t\t\t\t<h3>{{i18nLabel.dht_dms_crm_medical_device_industry_plugin}}</h3>\n\t\t\t\t\t\t\t<div class="capability-row">\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_crm_first_purchase_management}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dht_dms_crm_channel_inventory_flow}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="capability-item">\n\t\t\t\t\t\t\t\t\t<span class="fx-icon-checkmark"></span>{{i18nLabel.dms_medical_guide_QualityInspectionReportQuery}}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="button-container">\n\t\t\t\t\t\t\t\t<fx-button size="small" type="text" @click=\'handleClose\'>{{$t(\'暂不启用\')}}</fx-button>\n\t\t\t\t\t\t\t\t<fx-button size="small" type="primary" @click=\'handleOpenDms\'>{{$t(\'立即初始化\')}}</fx-button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n                        <div class="layout-l" v-if="step === \'2\'">\n                            <div class="header">\n                                <div class="el-loading-spinner header-logo">\n                                    <span class="fx-icon-jingshi fail-style" v-if="failFlag"></span>\n                                    <svg viewBox="25 25 50 50" class="circular" v-else>\n                                        <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>\n                                    </svg>\n                                </div>\n                                <div class="header-title" v-if="failFlag">{{i18nLabel.dht_dms_init_faild}}</div>\n\t\t\t\t\t\t\t\t<div class="header-title" v-else>{{i18nLabel.dht_init_underway_header_processing}}</div>\n                                <div class="header-tip" v-if="!failFlag">\n                                    <p>{{i18nLabel.dht_dms_crm_init_notice}}</p>\n                                </div>\n                            </div>\n                            <div class="e">\n                                <div class="workflow-item" v-for="(item, index) in workflow" :key="index">\n                                    <div class="workflow-item_icon">\n                                        <span class="fx-icon-checkmark" v-if="item.status === \'1\'"></span>\n\t\t\t\t\t\t\t\t\t  \t<span class="fx-icon-jingshi" v-else-if="item.status === \'2\'"></span>\n\t\t\t\t\t\t\t\t\t\t <span class="fx-icon-process-default" v-else></span>\n                                    </div>\n                                    <div class="workflow-item_label">\n                                        {{ item.name }}\n                                    </div>\n                                </div>\n                            </div>\n                            <div class="again" v-if="failFlag">\n                                <fx-button plain type="primary2" size="small" :disabled="false" :loading="false" @click="handleReInit">{{i18nLabel.dht_dms_crm_btn_Retry}}</fx-button>\n\t\t\t\t\t\t\t\t<fx-button size="small" type="text" @click="handleClose">{{i18nLabel.dht_dms_crm_try_again}}</fx-button>\n                            </div>\n                        </div>\n\t\t\t\t\t\t<div class="layout-l" v-if="step === \'3\'">\n\t\t\t\t\t\t\t<div class="icon-container">\n\t\t\t\t\t\t\t\t<span style="font-size:28px" class="fx-icon-checkmark2"></span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<h2 class="success-h2">{{i18nLabel.dht_dms_completed_the_initial_configuration}}</h2>\n\t\t\t\t\t\t\t<p class="success-notice">{{i18nLabel.dht_dms_crm_please_roceed}} <span class="app-name">{{i18nLabel.dht_dms_crm_dealer_management}}</span> {{i18nLabel.dht_dms_crm_business_with_the_app}}</p>\n\t\t\t\t\t\t\t<div class="success-button-container">\n\t\t\t\t\t\t\t\t<fx-button size="small" type="primary" @click=\'handleToManage\'>{{i18nLabel.dht_dms_crm_enter_dealer_management}}</fx-button>\n\t\t\t\t\t\t\t\t<fx-button size="small" style="margin-top:15px" type="text" @click=\'handleToManageAfter\'>{{i18nLabel.dht_dms_come_back_later}}</fx-button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n                        <div class="layout-r">\n                            <fx-carousel height="100%" :autoplay="false">\n                                <fx-carousel-item v-for="(item, index) in images" :key="index">\n\t\t\t\t\t\t\t\t \t<div class="carousel-title">\n                                        <p>{{ item.title }}</p>\n                                    </div>\n                                    <img class="carousel-img" :src="item.imageSrc" width="200px" height="200px" />\n                                </fx-carousel-item>\n                            </fx-carousel>\n                        </div>\n                    </div>\n            </fx-dialog>\n\t\t</div>',name:"ManufacturingUniversalDms",props:{has_more_industry:{type:Boolean,default:!1}},data:function(){return{loading:!1,visible:!1,step:"1",failFlag:!1,workflow:[],curView:"",multiLevelSwitch:!1,i18nLabel:{loading:$t("crm.dms.medical_industry.loading"),startNow:$t("crm.dms.medical_industry.start_now"),desc1:$t("crm.dms.medical_industry_new.desc1"),desc2:$t("crm.dms.medical_industry_new.desc2"),desc3:$t("crm.dms.medical_industry_new.desc3"),clickCheck:$t("crm.dms.medical_industry.clickCheck"),desc4:$t("crm.dms.medical_industry_new.desc4"),planStatus:$t("crm.dms.medical_industry.planStatus"),enabled:$t("crm.dms.medical_industry.enabled"),planDesc1:$t("crm.dms.medical_industry_new.planDesc1"),planDesc2:$t("crm.businesslink.header_title"),planDesc3:$t("crm.dms.medical_industry.planDesc3"),enableMultiLevelSale:$t("crm.dms.medical_industry.enableMultiLevelSale"),confirmEnableDMS:$t("crm.dms.medical_industry.confirmEnableDMS"),confirmEnableMultiLevelSale:$t("crm.dms.medical_industry.confirmEnableMultiLevelSale"),failOpen:$t("crm.dms.medical_industry.failOpen"),dht_dms_crm_channel_management:$t("dht_dms_crm_channel_management"),dht_dms_crm_merchant_center:$t("dht_dms_crm_merchant_center"),dht_dms_crm_inventory_management:$t("dht_dms_crm_inventory_management"),dht_dms_channel_mpowerment:$t("dht_dms_channel_mpowerment"),dht_dms_after_sales_service:$t("dht_dms_after_sales_service"),dht_dms_transaction_management:$t("dht_dms_transaction_management"),dht_dms_channel_incentive:$t("dht_dms_channel_incentive"),dht_dms_channel_policy:$t("dht_dms_channel_policy"),dht_dms_crm_first_purchase_management:$t("dht_dms_crm_first_purchase_management"),dht_dms_crm_channel_inventory_flow:$t("dht_dms_crm_channel_inventory_flow"),dms_medical_guide_QualityInspectionReportQuery:$t("dms.medical.guide.QualityInspectionReportQuery"),dht_init_footer_noUse:$t("dht.init.footer.noUse"),dht_dms_init_faild:$t("dht_dms_init_faild"),dht_init_underway_header_processing:$t("dht.init.underway.header.processing"),dht_dms_crm_init_notice:$t("dht_dms_crm_init_notice"),dht_dms_completed_the_initial_configuration:$t("dht_dms_completed_the_initial_configuration"),dht_dms_crm_please_roceed:$t("dht_dms_crm_please_roceed"),dht_dms_crm_dealer_management:$t("dht_dms_crm_dealer_management"),dht_dms_come_back_later:$t("dht_dms_come_back_later"),dht_dms_crm_anufacturing_dms:$t("dht_dms_crm_anufacturing_dms"),dht_dms_general_capabilities:$t("dht_dms_general_capabilities"),dht_dms_crm_btn_Retry:$t("dht_dms_crm_btn_Retry"),dht_dms_crm_try_again:$t("dht_dms_crm_try_again"),dht_dms_crm_medical_device_industry_plugin:$t("dht_dms_crm_medical_device_industry_plugin"),dht_dms_crm_manufacturing_dms_desc:$t("dht_dms_crm_manufacturing_dms_desc"),dht_dms_distribution_management:$t("dht_dms_distribution_management"),dht_dms_crm_enter_dealer_management:$t("dht_dms_crm_enter_dealer_management")},image:"https://a9.fspage.com/FSR/frontend/assets/dht/web/dms-cn.svg"}},computed:{stepOneHeight:function(){return this.has_more_industry?"calc(100vh - 57px)":"calc(100vh)"},images:function(){var t="en"===FS.util.getCurLocale()?"en":"cn";return[{imageSrc:"https://a9.fspage.com/FSR/frontend/assets/dht/web/manufacturing1-".concat(t,".svg"),title:$t("dht_dms_crm_lifecycle_title1")},{imageSrc:"https://a9.fspage.com/FSR/frontend/assets/dht/web/manufacturing2-".concat(t,".svg"),title:$t("dht_dms_crm_lifecycle_title2")},{imageSrc:"https://a9.fspage.com/FSR/frontend/assets/dht/web/manufacturing3-".concat(t,".svg"),title:$t("dht_dms_crm_lifecycle_title3")}]}},mounted:function(){var e=this;this.getExistLicenses().then(function(t){e.hasLisense=!!t.length,e.initDms(),e.setImageByLang()})},methods:{initDms:function(){var e=this;this.getConfigValues().then(function(t){"0"===t.manufacturing_universal_dms?e.curView="stepOne":"1"===t.manufacturing_universal_dms?e.curView="stepTwo":"2"===t.manufacturing_universal_dms?(e.curView="stepOne",e.visible=!0,e.step="2",e.getStepStatus()):"3"===t.manufacturing_universal_dms&&(e.curView="stepOne",e.visible=!0,e.step="2",e.failFlag=!0)})},setImageByLang:function(){var t=FS.util.getCurLocale();this.image="zh-CN"===t||"zh-TW"===t?"https://a9.fspage.com/FSR/frontend/assets/dht/web/dms-cn.svg":"https://a9.fspage.com/FSR/frontend/assets/dht/web/dms-en.svg"},getExistLicenses:function(){return new Promise(function(e,n){s.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/get_exist_licenses",data:{licenses:["dms_medical_device_plugin_app"]},success:function(t){0===t.Result.StatusCode?e(t.Value.licenses):n()}},{errorAlertModel:1})})},getConfigValues:function(){return new Promise(function(n,i){s.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/get_config_values",data:{isAllConfig:!1,keys:["manufacturing_universal_dms"]},success:function(t){var e;0===t.Result.StatusCode?(e={},t=t.Value.values,_.each(t,function(t){e[t.key]=t.value}),n(e)):i()}},{errorAlertModel:1})})},getConfigSteps:function(){return new Promise(function(e,n){s.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/get_config_steps",data:{key:"manufacturing_universal_dms"},success:function(t){0===t.Result.StatusCode?e(t):n()}},{errorAlertModel:1})})},openMedicalDMS:function(){this.visible=!0},toDMS:function(){var t=-1==window.location.origin.indexOf("fxiaoke")?"FSAID_PaaS_fc7416455614":"FSAID_PaaS_e75557129452";window.open("".concat(window.location.origin,"/XV/UI/Home#paasapp/index/=/appId_").concat(t))},getStepStatus:function(){var e=this,n=Date.now(),i=setInterval(function(){e.getConfigSteps().then(function(t){e.workflow=t.Value.steps,"1"===t.Value.value?(e.step="3",clearInterval(i)):"3"===t.Value.value&&(e.failFlag=!0,clearInterval(i)),6e5<=Date.now()-n&&clearInterval(i)})},3e3)},handleSetConfigValue:function(){return new Promise(function(e,t){s.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/set_config_value",data:{key:"manufacturing_universal_dms",value:"1"},success:function(t){0===t.Result.StatusCode?e(t):CRM.util.alert(t.Result.FailureMessage||$t("保存失败，请重试！"))}},{errorAlertModel:1})})},handleReInit:function(){this.workflow=[],this.handleOpenDms()},handleOpenDms:function(){var e=this;this.handleSetConfigValue().then(function(t){e.step="2",e.failFlag=!1,e.getStepStatus()})},handleClose:function(){this.step="1",this.visible=!1},handleToManageAfter:function(){this.visible=!1,this.curView="stepTwo"},handleToManage:function(){this.toDMS(),this.visible=!1,this.curView="stepTwo"}}}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return o};var s,o={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",i=e.toStringTag||"@@toStringTag";function u(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{u({},"")}catch(s){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var i,a,o,l,e=e&&e.prototype instanceof m?e:m,e=Object.create(e.prototype);return u(e,"_invoke",(i=t,a=n,o=new b(r||[]),l=1,function(t,e){if(3===l)throw Error("Generator is already running");if(4===l){if("throw"===t)throw e;return{value:s,done:!0}}for(o.method=t,o.arg=e;;){var n=o.delegate;if(n){n=function t(e,n){var r=n.method,i=e.i[r];if(i===s)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=s,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;r=d(i,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,f;i=r.arg;return i?i.done?(n[e.r]=i.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=s),n.delegate=null,f):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}(n,o);if(n){if(n===f)continue;return n}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(1===l)throw l=4,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);l=3;n=d(i,a,o);if("normal"===n.type){if(l=o.done?4:2,n.arg===f)continue;return{value:n.arg,done:o.done}}"throw"===n.type&&(l=4,o.method="throw",o.arg=n.arg)}}),!0),e}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}o.wrap=l;var f={};function m(){}function a(){}function p(){}var e={},h=(u(e,r,function(){return this}),Object.getPrototypeOf),h=h&&h(h(S([]))),v=(h&&h!==t&&c.call(h,r)&&(e=h),p.prototype=m.prototype=Object.create(e));function y(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function g(o,l){var e;u(this,"_invoke",function(n,r){function t(){return new l(function(t,e){!function e(t,n,r,i){var a,t=d(o[t],o,n);if("throw"!==t.type)return(n=(a=t.arg).value)&&"object"==_typeof(n)&&c.call(n,"__await")?l.resolve(n.__await).then(function(t){e("next",t,r,i)},function(t){e("throw",t,r,i)}):l.resolve(n).then(function(t){a.value=t,r(a)},function(t){return e("throw",t,r,i)});i(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function _(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=s,t[4]=e}function b(t){this.tryEntries=[[-1]],t.forEach(_,this),this.reset(!0)}function S(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(c.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return u(v,"constructor",a.prototype=p),u(p,"constructor",a),a.displayName=u(p,i,"GeneratorFunction"),o.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===a||"GeneratorFunction"===(t.displayName||t.name))},o.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,u(t,i,"GeneratorFunction")),t.prototype=Object.create(v),t},o.awrap=function(t){return{__await:t}},y(g.prototype),u(g.prototype,n,function(){return this}),o.AsyncIterator=g,o.async=function(t,e,n,r,i){void 0===i&&(i=Promise);var a=new g(l(t,e,n,r),i);return o.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},y(v),u(v,i,"Generator"),u(v,r,function(){return this}),u(v,"toString",function(){return"[object Generator]"}),o.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},o.values=S,b.prototype={constructor:b,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){a.type="throw",a.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var i=this.tryEntries[r],a=i[4],o=this.prev,l=i[1],c=i[2];if(-1===i[0])return t("end"),!1;if(!l&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=o){if(o<l)return this.method="next",this.arg=s,t(l),!0;if(o<c)return t(c),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}var a=(i=i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]?null:i)?i[4]:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i[2],f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),w(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,i=this.tryEntries[e];if(i[0]===t)return"throw"===(n=i[4]).type&&(r=n.arg,w(i)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:S(t),r:e,n:n},"next"===this.method&&(this.arg=s),f}},o}function asyncGeneratorStep(t,e,n,r,i,a,o){try{var l=t[a](o),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(l){return function(){var t=this,o=arguments;return new Promise(function(e,n){var r=l.apply(t,o);function i(t){asyncGeneratorStep(r,e,n,i,a,"next",t)}function a(t){asyncGeneratorStep(r,e,n,i,a,"throw",t)}i(void 0)})}}define("crm-setting/businesslink/components/Medical-Industry",[],function(t,e,n){var o=FS.crmUtil;n.exports={template:'<div class="industry-component-slot" id="medical-industry-component" v-loading.fullscreen.lock="loading" :fx-loading-text="i18nLabel.loading" fx-loading-background="rgba(255, 255, 255, 0.8)">\n\t\t\t<div class="medical-industry-stepOne" v-if="curView===\'stepOne\'">\n\t\t\t    <div class="img-wrap" :style="{height:stepOneHeight}">\n\t\t\t\t    <img :src="image" />\n\t\t\t\t</div>\n\t\t\t    <div class="bottom">\n\t\t\t\t     <fx-button size="small" type="primary" @click=\'openMedicalDMS\'>{{i18nLabel.startNow}}</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="medical-industry-stepTwo" v-else-if="curView === \'stepTwo\' ">\n\t\t\t    <div class="medical-industry-stepTwo-textWrap">\n\t\t\t\t    <h3>{{i18nLabel.desc1}}</h3>\n\t\t\t\t    <p>{{i18nLabel.desc2}}</p>\n\t\t\t\t\t<p>{{i18nLabel.desc3}}<fx-link :underline="false" type="standard" style="font-size:12px" @click="onClickHere">{{i18nLabel.clickCheck}}</fx-link></p>\n\t\t\t\t\t<p>{{i18nLabel.desc4}}</p>\n\t\t\t\t</div>\n\t\t\t\t<div class="params-block">\n\t\t\t\t    <div class="params-title">{{i18nLabel.planStatus}}</div>\n\t\t\t\t\t<div class="params-desc">\n\t\t\t\t\t    <div style="font-size: 14px;"><span class="fx-icon-chenggong"></span> {{i18nLabel.enabled}}</div>\n\t\t\t\t\t\t<div class="solution-tip">{{i18nLabel.planDesc1}} <fx-link :underline="false" type="standard" style="font-size:12px" @click="toDMS">{{i18nLabel.planDesc2}}</fx-link> {{i18nLabel.planDesc3}}</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="params-block">\n\t\t\t\t    <div class="params-title">{{i18nLabel.enableMultiLevelSale}}</div>\n\t\t\t\t\t<div class="params-desc">\n\t\t\t\t\t   <fx-switch v-model="multiLevelSwitch" show-label size="small" :disabled="multiLevelSwitch" :before-change="openMedicalEquipmentDms"></fx-switch>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>',name:"MedicalIndustry",props:{has_more_industry:{type:Boolean,default:!1}},data:function(){return{loading:!1,curView:"",multiLevelSwitch:!1,i18nLabel:{loading:$t("crm.dms.medical_industry.loading"),startNow:$t("crm.dms.medical_industry.start_now"),desc1:$t("crm.dms.medical_industry.desc1"),desc2:$t("crm.dms.medical_industry.desc2"),desc3:$t("crm.dms.medical_industry.desc3"),clickCheck:$t("crm.dms.medical_industry.clickCheck"),desc4:$t("crm.dms.medical_industry.desc4"),planStatus:$t("crm.dms.medical_industry.planStatus"),enabled:$t("crm.dms.medical_industry.enabled"),planDesc1:$t("crm.dms.medical_industry.planDesc1"),planDesc2:$t("crm.businesslink.header_title"),planDesc3:$t("crm.dms.medical_industry.planDesc3"),enableMultiLevelSale:$t("crm.dms.medical_industry.enableMultiLevelSale"),confirmEnableDMS:$t("crm.dms.medical_industry.confirmEnableDMS"),confirmEnableMultiLevelSale:$t("crm.dms.medical_industry.confirmEnableMultiLevelSale"),failOpen:$t("crm.dms.medical_industry.failOpen")},image:"https://a9.fspage.com/FSR/weex/dht/web/medical_dms_cn.svg"}},computed:{stepOneHeight:function(){return this.has_more_industry?"calc(100vh - 57px)":"calc(100vh)"}},mounted:function(){this._pollEnableStatus(["medical_equipment_dms","medical_equipment_dms_multi_level"]),this.setImageByLang()},destroyed:function(){},methods:{setImageByLang:function(){var t=FS.util.getCurLocale();this.image="zh-CN"===t||"zh-TW"===t?"https://a9.fspage.com/FSR/weex/dht/web/medical_dms_cn.svg":"https://a9.fspage.com/FSR/weex/dht/web/medical_dms_en.svg"},getConfigValues:function(t){return new Promise(function(n,r){o.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/get_config_values",data:{isAllConfig:!1,keys:t},success:function(t){var e;0===t.Result.StatusCode?(e={},t=t.Value.values,_.each(t,function(t){e[t.key]=t.value}),n(e)):r()}},{errorAlertModel:1})})},_pollEnableStatus:function(e){var n=this;this._updateEnableStatus(e).finally(function(){var t;n.loading&&(t=setInterval(function(){n._updateEnableStatus(e,t)},3e3))})},_updateEnableStatus:function(r,i){var a=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",a.getConfigValues(r).then(function(e){var t=r.find(function(t){return"2"===e[t]}),n=r.find(function(t){return"3"===e[t]});t?(a.loading=!0,"1"===e.medical_equipment_dms?a.curView="stepTwo":a.curView="stepOne"):(a.loading=!1,n&&o.alert(a.i18nLabel.failOpen),"1"===e.medical_equipment_dms?a.curView="stepTwo":a.curView="stepOne","1"===e.medical_equipment_dms_multi_level&&(a.multiLevelSwitch=!0),i&&clearInterval(i))}).catch(function(){a.loading=!1,i&&clearInterval(i)}));case 1:case"end":return t.stop()}},t)}))()},openMedicalDMS:function(){var n=this;this.$confirm(this.i18nLabel.confirmEnableDMS,$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){n.loading=!0,o.FHHApi({url:"/EM1HDHT/API/v1/object/dht_medical_equipment_dms/service/open_medical_equipment_dms",data:{},success:function(t){var e;0===t.Result.StatusCode?"2"===(e=t.Value.enableStatus)?n._pollEnableStatus(["medical_equipment_dms"]):"4"===e?(o.alert(n.i18nLabel.failOpen),n.loading=!1):(n.loading=!1,n.curView="stepTwo"):(n.loading=!1,o.alert(t.Result.FailureMessage))}},{errorAlertModel:1})}).catch(function(){})},onClickHere:function(){window.open("".concat(window.location.origin,"/XV/UI/Home#paasapp/store"))},toDMS:function(){var t=-1==window.location.origin.indexOf("fxiaoke")?"FSAID_PaaS_fc7416455614":"FSAID_PaaS_e75557129452";window.open("".concat(window.location.origin,"/XV/UI/Home#paasapp/index/=/appId_").concat(t))},openMedicalEquipmentDms:function(){var a=this;return new Promise(function(r,i){a.$confirm(a.i18nLabel.confirmEnableMultiLevelSale,$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){a.loading=!0,o.FHHApi({url:"/EM1HDHT/API/v1/object/dht_medical_equipment_dms/service/open_medical_equipment_dms_multi_level",data:{},success:function(t){var e,n;0===t.Result.StatusCode?"2"===(e=t.Value.enableStatus)?n=setInterval(function(){a.getConfigValues(["medical_equipment_dms_multi_level"]).then(function(t){var e="3"===t.medical_equipment_dms_multi_level;"1"===t.medical_equipment_dms_multi_level?(a.loading=!1,clearInterval(n),r()):e&&(a.loading=!1,o.alert(a.i18nLabel.failOpen),clearInterval(n),i())}).catch(function(){a.loading=!1,clearInterval(n),i()})},3e3):"4"===e?(o.alert(a.i18nLabel.failOpen),a.loading=!1):(a.loading=!1,r()):(o.alert(t.Result.FailureMessage),a.loading=!1,i())}},{errorAlertModel:1})}).catch(function(){i()})})}}}});
define("crm-setting/businesslink/components/setStrategy",["./tabledata"],function(r,n,t){var d=r("./tabledata"),o=CRM._cache.close_old_category,e=CRMSETTING.ASSETS_PATH+"/images/businesslink/down-a955e8e088.png",i=CRMSETTING.ASSETS_PATH+"/images/businesslink/up-f565bd5820.png",s=CRMSETTING.ASSETS_PATH+"/images/businesslink/duihao-hui-a51667af68.png";_.each(d,function(n){_.each(n.configs,function(n){n.data=n.data.filter(function(n){return!n.hasOwnProperty("isProductCategory")||n.hasOwnProperty("isProductCategory")&&n.isProductCategory===o})})});t.exports=function(n,t){return Vue.extend({template:'\n                <div class="new-strategy-box">\n                    <div class="group-title" :style="{minWidth:isShowProductCategory?\'1350px\':\'1175px\',maxWidth:isShowProductCategory?\'1380px\':\'1260px\'}">\n                        <div class="group-title-text">{{type===\'down\'?$t(\'下发策略\'):$t(\'采集策略\')}}</div>\n                        <fx-button class="crm-btn crm-btn-primary one-key-set" @click="setStrategyHandel(type)" :icon="type===\'down\'? (show_down_icon?\'el-icon-check\':\'\') :(show_up_icon?\'el-icon-check\':\'\')" \n                        :disabled="type===\'down\'?  show_down_icon : show_up_icon"\n                        :loading="type===\'down\'?down_loading:up_loading">\n                        {{type===\'down\'? down_btn_txt : up_btn_txt}}</fx-button>\n                    </div>\n                    <div :class="[\'strategy-box\',type===\'up\' && \'up-box\']" :style="{minWidth:isShowProductCategory?\'1350px\':\'1175px\',maxWidth:isShowProductCategory?\'1380px\':\'1260px\'}">\n                        <div class="box-title">{{self_data.title}}</div>\n                        <div class="set-box">\n                            <div v-for="(item,index) in self_data.configs" :key="index" class="config-box">\n                                <h3 @mouseenter="show_desc(type,index,true)" @mouseleave="show_desc(type,index,false)">\n                                    {{type===\'up\'?(item.title.length>16?item.title.slice(0,16)+"...":item.title):item.title}}\n                                    <div class="hover-desc" :ref="type+\'_\'+index">\n                                        <p v-for="(desc,key) in item.hover" :key="key">\n                                            {{key == 0 ? \'\' : key+\'、\'}}{{desc}}\n                                        </p>\n                                    </div>\n                                </h3>\n                                \n                                <div class="btn-box-wrap">\n                                    <div v-for="button in item.data"  class="btn-box">\n                                        <div :class="[\'self-btn\',type===\'up\'&&\'big-btn\']"\n                                        v-html="button.syncObj"></div>\n                                        <div class="btn-border">\n                                            <img :src="type==\'down\'? down_arrow_img : up_arrow_img" class="set-arrow">\n                                            <div class="set-btn-pos">\n                                                <fx-button @click="setStrategyHandel(button.value)" type="primary" class="set-btn"  size="mini" v-if="!checkConfig(button.value)">{{$t(\'crm-crmmanage-module-businesslink-Configure\')}}</fx-button>\n                                                <span class="already-set" v-if="checkConfig(button.value)"><img :src="duihao_img"/>{{$t(\'已配置\')}}</span>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div :class="[\'strategy-box\',\'sale-box\',type===\'up\' && \'up-sale-box\']" :style="{minWidth:isShowProductCategory?\'1350px\':\'1175px\',maxWidth:isShowProductCategory?\'1380px\':\'1260px\'}">\n                        <div class="box-title">{{sale_data.title}}</div>\n                            <div class="set-box">\n                                <div v-for="item in sale_data.configs" class="config-box">\n                                    <div class="btn-box-wrap">\n                                        <div v-for="button in item.data"  class="btn-box">\n                                            <div :class="[\'self-btn\',type===\'up\'&&\'big-btn\']" v-html="button.syncObj"></div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                    </div>\n                </div>\n            ',data:function(){return{config:t,type:n,down_arrow_img:e,up_arrow_img:i,duihao_img:s,down_loading:!1,up_loading:!1,isShowProductCategory:o}},computed:{self_data:function(){return"down"===n?d.downSelf:d.upSelf},sale_data:function(){return"down"===n?d.downSale:d.upSale},show_down_icon:function(){var t=this.config;return d.downSelf.configs.map(function(n){return n.data.map(function(n){return n.value})}).flat(2).every(function(n){return t[n]})},show_up_icon:function(){var t=this.config;return d.upSelf.configs.map(function(n){return n.data.map(function(n){return n.value})}).flat(2).every(function(n){return t[n]})},down_btn_txt:function(){return this.down_loading?$t("初始化中"):this.show_down_icon?$t("已配置"):$t("一键配置")},up_btn_txt:function(){return this.up_loading?$t("初始化中"):this.show_up_icon?$t("已配置"):$t("一键配置")}},methods:{show_desc:function(n,t,o){this.$refs[n+"_"+t][0].style.display=o?"block":"none"},checkConfig:function(n){var t=this.config;return Array.isArray(n)?n.every(function(n){return t[n]}):t[n]},getStrategyConfig:function(){var i=this;return new Promise(function(o,e){CRM.util.FHHApi({url:"/EM1HWaiQinV2/tradeThroughService/getGatherSettting",data:{},success:function(n){var t=n.Result,n=n.Value;0===t.StatusCode&&0===n.errorCode?(t=n.tradeThroughConfig,i.config=t,o(!0)):e(!1)}},{errorAlertModel:1})})},setStrategyHandel:function(o){var e=this,i=Array.isArray(o),s=i?o[0]:o,n=["up","down"].includes(o),a=n?"oneKeySet":"setStrategy",c=n?"down"===o?$t("确定一键配置{{name}}策略吗？",{name:$t("下发")}):$t("确定一键配置{{name}}策略吗？",{name:$t("采集")}):$t("确定配置该策略吗？");r.async("./commonDialog",function(n){var t=new n;t.show(a,c,s),t.on("setStrategyCommit",function(){t.destroy(),i?o.forEach(function(n){e.config[n]=!0}):e.config[o]=!0,FxUI.MessageBox.confirm($t("已成功配置{{name}}策略并启用！",{name:""}),$t("提示"),{showConfirmButton:!1,showClose:!1,cancelButtonText:$t("我知道了"),type:"success"}).catch(function(n){})}),t.on("showBtnLoading",function(n){"down"===n&&(e.down_loading=!0),"up"===n&&(e.up_loading=!0)}),t.on("getNewConfig",function(n){e.getStrategyConfig().then(function(){t.destroy(),e.down_loading=!1,e.up_loading=!1,FxUI.MessageBox.confirm(n,$t("提示"),{dangerouslyUseHTMLString:!0,showConfirmButton:!1,showClose:!1,cancelButtonText:$t("我知道了"),type:"error"}).catch(function(n){})})}),t.on("oneKeySetCommit",function(n){t.destroy(),n="down"===n?(d.downSelf.configs.forEach(function(n){n.data.forEach(function(n){e.config[n.value]=!0})}),e.down_loading=!1,$t("已成功配置{{name}}策略并启用！",{name:$t("下发")})):(d.upSelf.configs.forEach(function(n){n.data.forEach(function(n){e.config[n.value]=!0})}),e.up_loading=!1,$t("已成功配置{{name}}策略并启用！",{name:$t("采集")})),FxUI.MessageBox.confirm(n,$t("提示"),{showConfirmButton:!1,showClose:!1,cancelButtonText:$t("我知道了"),type:"success"}).catch(function(n){})}),t.on("hide",function(){t.destroy()})})}}})}});
define("crm-setting/businesslink/components/tabledata",[],function(t,$,a){return{downSelf:{title:$t("本企业"),configs:[{title:$t("门店信息统一管理"),hover:[$t("应用场景说明")+"：",$t("将本企业创建的门店下发到所属的经销商或二批商"),$t("适用于门店统一由品牌商管理，或者新开经销商或二批商时将该地区门店一次性下发到经销商或二批商")],data:[{value:7,syncObj:$t("门店、门店地址")},{value:12,syncObj:$t("联系人")}]},{title:$t("产品信息统一管理"),hover:[$t("应用场景说明")+"：",$t("将本企业的产品、商品、规格、计量单位下发到所属的经销商或二批商"),$t("适用于产品、商品、规格、计量单位由品牌商统一管理，经销商或二批商直接使用的场景")],data:[{value:16,syncObj:$t("产品分类"),isProductCategory:!0},{value:8,syncObj:$t("产品")},{value:10,syncObj:$t("商品")},{value:11,syncObj:$t("规格")+"、"+$t("规格值")},{value:9,syncObj:$t("单位")}]}]},downSale:{title:$t("经销商")+"/"+$t("二批商"),configs:[{data:[{value:7,syncObj:$t("门店、门店地址")},{value:12,syncObj:$t("联系人")}]},{data:[{value:16,syncObj:$t("产品分类"),isProductCategory:!0},{value:8,syncObj:$t("产品")},{value:10,syncObj:$t("商品")},{value:11,syncObj:$t("规格")+"、"+$t("规格值")},{value:9,syncObj:$t("单位")}]}]},upSelf:{title:$t("本企业"),configs:[{title:$t("门店")+$t("信息采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的门店、门店地址数据，采集到本企业系统内"),$t("适用于获取经销商或二批商拓展的所有门店数据后，做全面的渠道数据分析")],data:[{value:1,syncObj:$t("门店、门店地址")}]},{title:$t("门店")+$t("订单")+$t("采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的销售订单、订单产品数据，采集到本企业系统内"),$t("适用于获取经销商或二批商的所有销售数据后，做全面的渠道销售数据分析")],data:[{value:2,syncObj:$t("经销商门店订单")+"、<br>"+$t("订单产品")}]},{title:$t("门店")+$t("发货单")+$t("采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的发货单、发货单产品数据，采集到本企业系统内"),$t("适用于获取经销商或二批商发货数据后，协助企业做业务数据分析")],data:[{value:5,syncObj:$t("经销商")+$t("发货单")+"、<br>"+$t("发货单产品")}]},{title:$t("规格")+$t("采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的规格数据，采集到本企业系统内"),$t("适用于获取经销商或二批商规格数据后，协助企业做业务数据分析")],data:[{value:15,syncObj:$t("产品分类"),isProductCategory:!0},{value:14,syncObj:$t("规格")+"、"+$t("规格值")}]},{title:$t("经销商")+$t("库存")+$t("采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的经销商仓库、库存数据，采集到本企业系统内"),$t("适用于获取经销商或二批商库存数据后，协助企业做补货分析")],data:[{value:4,syncObj:$t("经销商")+$t("仓库")},{value:3,syncObj:$t("经销商")+$t("库存")}]},{title:$t("拜访")+$t("采集"),hover:[$t("应用场景说明")+"：",$t("将经销商或二批商的高级外勤数据，采集到本企业系统内"),$t("适用于了解经销商或二批商对门店拜访的执行情况")],data:[{value:6,syncObj:$t("经销商")+$t("外勤")}]}]},upSale:{title:$t("经销商")+"/"+$t("二批商"),configs:[{data:[{value:1,syncObj:$t("门店、门店地址")}]},{data:[{value:2,syncObj:$t("销售订单")+"、<br>"+$t("订单产品")}]},{data:[{value:5,syncObj:$t("发货单")+"、<br>"+$t("发货单产品")}]},{data:[{value:15,syncObj:$t("产品分类"),isProductCategory:!0},{value:14,syncObj:$t("规格")+"、"+$t("规格值")}]},{data:[{value:4,syncObj:$t("仓库")},{value:3,syncObj:$t("库存")}]},{data:[{value:6,syncObj:$t("高级外勤")}]}]}}});
define("crm-setting/businesslink/template/businesslink-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div id="crm-businesslink-wrap"> <div class="crm-businesslink-header-slot"></div> <div class="industry-component-slot"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/businesslink/template/page-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-businesslink"> ';
            if (!is_buy) {
                __p += ' <div class="no-buy-box" > <section class="shadow-box"> <p>' + ((__t = $t("贵公司未购买该模块，请联系您的企业销售人员购买，或拨打客服电话phone进行咨询", {
                    phone: "<span>400-1122-778</span>"
                })) == null ? "" : __t) + '</p> <img src="' + ((__t = img1) == null ? "" : __t) + '" alt=""> <img src="' + ((__t = img2) == null ? "" : __t) + '" alt=""> </section> </div> ';
            } else {
                __p += ' <div class="crm-tit"> <h1> <span class="tit-txt">' + ((__t = $t("经销商管理")) == null ? "" : __t) + '</span> </h1> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20"> <section> <h2 class="title">' + ((__t = $t("经销层级配置")) == null ? "" : __t) + '</h2> <p class="title-desc">' + ((__t = $t("请选择适合本企业的经销层级，选择后会自动启用对应的功能。")) == null ? "" : __t) + '</p> <div class="distribution-level" style="' + ((__t = isShowProductCategory ? "min-width:1350px;max-width:1380px" : "min-width:1175px;max-width:1260px") == null ? "" : __t) + '"> ';
                for (var i = 0; i < distribution_level_desc.length; i++) {
                    __p += ' <div class="level-box ' + ((__t = level_id == distribution_level_desc[i].id ? "current-config" : "") == null ? "" : __t) + '"> ';
                    if (i == 0) {
                        __p += ' <div class="img-text-wrap"> <div class="img-item"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/brand-f6179f9f2e.png") == null ? "" : __t) + '"> <span>' + ((__t = $t("品牌商")) == null ? "" : __t) + '</span> </div> <div class="img-item arrow-pos"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/big_arrow-59d6740ec4.png") == null ? "" : __t) + '" class="big-arrow-img"> </div> <div class="img-item"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/sale-4373447e7d.png") == null ? "" : __t) + '"> <span>' + ((__t = $t("经销商")) == null ? "" : __t) + "</span> </div> </div> ";
                    } else {
                        __p += ' <div class="img-text-wrap"> <div class="img-item"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/brand-f6179f9f2e.png") == null ? "" : __t) + '"> <span>' + ((__t = $t("品牌商")) == null ? "" : __t) + '</span> </div> <div class="img-item arrow-pos"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/small_arrow-82efbd7a2b.png") == null ? "" : __t) + '" class="small-arrow-img"> </div> <div class="img-item"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/sale-4373447e7d.png") == null ? "" : __t) + '"> <span>' + ((__t = $t("经销商")) == null ? "" : __t) + '</span> </div> <div class="img-item arrow-pos"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/small_arrow-82efbd7a2b.png") == null ? "" : __t) + '" class="small-arrow-img"> </div> <div class="img-item"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/second_sale-4e3a7c39b7.png") == null ? "" : __t) + '"> <span>' + ((__t = $t("二批商")) == null ? "" : __t) + "</span> </div> </div> ";
                    }
                    __p += ' <div class="box-desc"> <h2 class="top-title">' + ((__t = distribution_level_desc[i].title1) == null ? "" : __t) + '</h2> <div class="top-desc"> ';
                    for (var j = 0; j < distribution_level_desc[i].desc1.length; j++) {
                        __p += " <p> " + ((__t = j + 1 + ".") == null ? "" : __t) + "" + ((__t = distribution_level_desc[i].desc1[j]) == null ? "" : __t) + "</p> ";
                    }
                    __p += ' </div> <p class="bottom-title">' + ((__t = distribution_level_desc[i].title2) == null ? "" : __t) + '：</p> <div class="bottom-desc"> <p>' + ((__t = distribution_level_desc[i].desc2) == null ? "" : __t) + '</p> </div> <div class="level-btn ' + ((__t = level_id == distribution_level_desc[i].id ? "current-use-wrap" : "") == null ? "" : __t) + '"> ';
                    if (level_id == distribution_level_desc[i].id) {
                        __p += ' <span class="current-use"><img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/businesslink/duihao-9bc25405c2.png") == null ? "" : __t) + '">' + ((__t = $t("当前使用")) == null ? "" : __t) + "</span> ";
                    } else {
                        __p += ' <span class="crm-btn crm-btn-primary switch-level" data-level="' + ((__t = distribution_level_desc[i].id) == null ? "" : __t) + '">' + ((__t = $t("切换为此层级")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " </div> </div> </div> ";
                }
                __p += ' </div> </section> <section> <h2 class="title">' + ((__t = $t("数据同步策略配置")) == null ? "" : __t) + '</h2> <div class="title-desc-wrap" style="' + ((__t = isShowProductCategory ? "min-width:1350px;max-width:1380px" : "min-width:1175px;max-width:1260px") == null ? "" : __t) + '"> <h3>' + ((__t = $t("使用场景")) == null ? "" : __t) + '：</h3> <p class="title-desc">' + ((__t = $t("管理经销商时，需要为经销商帐号下发门店、产品等数据，便于统一管理本企业产品；也需要采集经销商新拓展的门店、经销商库存等数据，便于做渠道洞察。")) == null ? "" : __t) + '</p> </div> <div class="title-desc-wrap" style="' + ((__t = isShowProductCategory ? "min-width:1350px;max-width:1380px" : "min-width:1175px;max-width:1260px") == null ? "" : __t) + '"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + '：</h3> <div class="title-desc"> <p>' + ((__t = $t("此功能即可快速配置数据下发及采集策略，请根据企业管理需求进行配置。")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("如需查看、修改、新建策略，可在应用")) == null ? "" : __t) + '<a href="#/app/shuttles/index" target="_blank" class="dataSync">【' + ((__t = $t("数据同步")) == null ? "" : __t) + "】</a>" + ((__t = $t("中处理")) == null ? "" : __t) + '。</p> </div> </div> <div class="down-strategy"></div> <div class="up-strategy"></div> </section> </div> ';
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});