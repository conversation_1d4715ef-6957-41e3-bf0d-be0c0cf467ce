function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return i};var c,i={},t=Object.prototype,s=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function h(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{h({},"")}catch(c){h=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o,a,i,u,e=e&&e.prototype instanceof p?e:p,e=Object.create(e.prototype);return h(e,"_invoke",(o=t,a=r,i=new _(n||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:c,done:!0}}for(i.method=t,i.arg=e;;){var r=i.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===c)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=c,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;n=l(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,f;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=c),r.delegate=null,f):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,f)}(r,i);if(r){if(r===f)continue;return r}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(1===u)throw u=4,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);u=3;r=l(o,a,i);if("normal"===r.type){if(u=i.done?4:2,r.arg===f)continue;return{value:r.arg,done:i.done}}"throw"===r.type&&(u=4,i.method="throw",i.arg=r.arg)}}),!0),e}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}i.wrap=u;var f={};function p(){}function a(){}function y(){}var e={},d=(h(e,n,function(){return this}),Object.getPrototypeOf),d=d&&d(d(x([]))),v=(d&&d!==t&&s.call(d,n)&&(e=d),y.prototype=p.prototype=Object.create(e));function g(t){["next","throw","return"].forEach(function(e){h(t,e,function(t){return this._invoke(e,t)})})}function m(i,u){var e;h(this,"_invoke",function(r,n){function t(){return new u(function(t,e){!function e(t,r,n,o){var a,t=l(i[t],i,r);if("throw"!==t.type)return(r=(a=t.arg).value)&&"object"==_typeof(r)&&s.call(r,"__await")?u.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):u.resolve(r).then(function(t){a.value=t,n(a)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function b(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=c,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(b,this),this.reset(!0)}function x(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(s.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=c,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return h(v,"constructor",a.prototype=y),h(y,"constructor",a),a.displayName=h(y,o,"GeneratorFunction"),i.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===a||"GeneratorFunction"===(t.displayName||t.name))},i.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,h(t,o,"GeneratorFunction")),t.prototype=Object.create(v),t},i.awrap=function(t){return{__await:t}},g(m.prototype),h(m.prototype,r,function(){return this}),i.AsyncIterator=m,i.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var a=new m(u(t,e,r,n),o);return i.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},g(v),h(v,o,"Generator"),h(v,n,function(){return this}),h(v,"toString",function(){return"[object Generator]"}),i.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},i.values=x,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&s.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){a.type="throw",a.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],a=o[4],i=this.prev,u=o[1],s=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=i){if(i<u)return this.method="next",this.arg=c,t(u),!0;if(i<s)return t(s),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var a=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o[2],f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),w(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,w(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:x(t),r:e,n:r},"next"===this.method&&(this.arg=c),f}},i}function asyncGeneratorStep(t,e,r,n,o,a,i){try{var u=t[a](i),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,o)}function _asyncToGenerator(u){return function(){var t=this,i=arguments;return new Promise(function(e,r){var n=u.apply(t,i);function o(t){asyncGeneratorStep(n,e,r,o,a,"next",t)}function a(t){asyncGeneratorStep(n,e,r,o,a,"throw",t)}o(void 0)})}}define("crm-setting/syncrules/syncrules",["./template/tpl-html","./syncRulesHandle"],function(t,e,r){var n,o,a,i=CRM.util,u=t("./template/tpl-html"),s=t("./syncRulesHandle");return Backbone.View.extend({template:u,events:{"click .add-btn":function(t){this.showSyncRule()}},initialize:function(t){this.$el.html(this.template()),this.render()},render:(a=_asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.getRules();case 2:e=t.sent,e=e.result,this.renderTable(e||[]);case 5:case"end":return t.stop()}},t,this)})),function(){return a.apply(this,arguments)}),getRules:function(){return i.ajax_base("/EM1HNCRM/API/v1/object/master_data_app/service/list",{})},renderTable:function(e){var r=this;this.table&&this.table.destroy(),t.async("crm-widget/table/table",function(t){r.table=new t({$el:r.$(".sync-rules-table"),showPage:!1,doStatic:!0,colResize:!0,columns:[{data:"display_name",title:$t("对象名称")},{data:null,title:$t("操作"),width:"100px",render:function(t,e,r){return'<span class="btn js-detail">'+$t("查看详情")+"</span>"}}]}),r.table.on("trclick",function(t,e,r){r.hasClass("js-detail")&&(location.hash="app/shuttles/index")}),r.table.doStaticData(e)})},showSyncRule:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"add",e=1<arguments.length?arguments[1]:void 0,r=this,n=$t("添加操作行为");r.sr&&r.sr.destroy(),r.sr=new s({title:n}),r.sr.on("suc",function(t){t.object_api_name&&r.handleRule("add",{api_name:t.object_api_name})}),r.sr.show(e,t,r.getTableData())},handleRule:(o=_asyncToGenerator(_regeneratorRuntime().mark(function t(e,r,n){var o,a;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return o={add:"/EM1HNCRM/API/v1/object/master_data_app/service/add",stop:"/EM1HNCRM/API/v1/object/master_data_app/service/stop",start:"/EM1HNCRM/API/v1/object/master_data_app/service/start",delete:"/EM1HNCRM/API/v1/object/master_data_app/service/delete"},t.next=3,i.ajax_base(o[e],r);case 3:if(a=t.sent,n)return n(a),t.abrupt("return");t.next=7;break;case 7:a&&this.refreshTable();case 8:case"end":return t.stop()}},t,this)})),function(t,e,r){return o.apply(this,arguments)}),getTableData:function(){var e=[];return _.each(this.table.getCurData()||[],function(t){e.push(t.api_name)}),e},refreshTable:(n=_asyncToGenerator(_regeneratorRuntime().mark(function t(){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.getRules();case 2:e=t.sent,this.table.doStaticData(e.result||[]);case 4:case"end":return t.stop()}},t,this)})),function(){return n.apply(this,arguments)})})});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var u,a={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function l(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{l({},"")}catch(u){l=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var o,i,a,s,e=e&&e.prototype instanceof p?e:p,e=Object.create(e.prototype);return l(e,"_invoke",(o=t,i=r,a=new _(n||[]),s=1,function(t,e){if(3===s)throw Error("Generator is already running");if(4===s){if("throw"===t)throw e;return{value:u,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,o=e.i[n];if(o===u)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=u,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),f;n=h(o,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,f;o=n.arg;return o?o.done?(r[e.r]=o.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=u),r.delegate=null,f):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,f)}(r,a);if(r){if(r===f)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===s)throw s=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=3;r=h(o,i,a);if("normal"===r.type){if(s=a.done?4:2,r.arg===f)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=4,a.method="throw",a.arg=r.arg)}}),!0),e}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=s;var f={};function p(){}function i(){}function d(){}var e={},y=(l(e,n,function(){return this}),Object.getPrototypeOf),y=y&&y(y(j([]))),v=(y&&y!==t&&c.call(y,n)&&(e=y),d.prototype=p.prototype=Object.create(e));function g(t){["next","throw","return"].forEach(function(e){l(t,e,function(t){return this._invoke(e,t)})})}function m(a,s){var e;l(this,"_invoke",function(r,n){function t(){return new s(function(t,e){!function e(t,r,n,o){var i,t=h(a[t],a,r);if("throw"!==t.type)return(r=(i=t.arg).value)&&"object"==_typeof(r)&&c.call(r,"__await")?s.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):s.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)});o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=u,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function j(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(c.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=u,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return l(v,"constructor",i.prototype=d),l(d,"constructor",i),i.displayName=l(d,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,l(t,o,"GeneratorFunction")),t.prototype=Object.create(v),t},a.awrap=function(t){return{__await:t}},g(m.prototype),l(m.prototype,r,function(){return this}),a.AsyncIterator=m,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new m(s(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},g(v),l(v,o,"Generator"),l(v,n,function(){return this}),l(v,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=j,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=u)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){i.type="throw",i.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,s=o[1],c=o[2];if(-1===o[0])return t("end"),!1;if(!s&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<s)return this.method="next",this.arg=u,t(s),!0;if(a<c)return t(c),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o[2],f):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),b(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,o=this.tryEntries[e];if(o[0]===t)return"throw"===(r=o[4]).type&&(n=r.arg,b(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:j(t),r:e,n:r},"next"===this.method&&(this.arg=u),f}},a}function asyncGeneratorStep(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(s){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=s.apply(t,a);function o(t){asyncGeneratorStep(n,e,r,o,i,"next",t)}function i(t){asyncGeneratorStep(n,e,r,o,i,"throw",t)}o(void 0)})}}define("crm-setting/syncrules/syncRulesHandle",["crm-widget/dialog/dialog"],function(n,t,e){var r,o=n("crm-widget/dialog/dialog").extend({attrs:{width:640,className:"crm-s-customer-follow",title:$t("添加跟进行为"),showBtns:!0,content:'<div class="follow-box"><div class="follow-objTitle">'+$t("所属对象")+'</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"saveData"},saveData:function(){this.trigger("suc",_.omit(this.state,["filterData"])),this.hide()},getObjectList:function(){var r=this;return new Promise(function(e){r.objectList?e(r.objectList):CRM.util.ajax_base("/EM1HNCRM/API/v1/object/master_data_app/service/object_list",{}).then(function(t){r.objectList=r.mapData(t.result||[]),r.objectList=r.filterApiName(r.objectList),e(r.objectList)})})},mapData:function(t){return t.map(function(t){return{name:t.display_name,value:t.api_name}})},filterApiName:function(t){var e=this;return _.filter(t,function(t){return!e.state.filterData.includes(t.value)})},createObjSelect:(r=_asyncToGenerator(_regeneratorRuntime().mark(function t(){var e,r;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=this,t.next=3,this.getObjectList();case 3:r=t.sent,this.select&&this.select.destroy(),n.async("crm-widget/select/select",function(t){e.select=new t({$wrap:e.$(".follow-objSelect"),zIndex:999,width:280,options:r,defaultValue:e.state.object_api_name||""}),e.updateSelectValue(e.select.getValue()),e.select.on("change",function(t){e.updateSelectValue(t)})});case 6:case"end":return t.stop()}},t,this)})),function(){return r.apply(this,arguments)}),updateSelectValue:function(e){var t;e&&(this.setState("object_api_name",e),t=_.find(this.objectList,function(t){return t.value===e}).name,this.setState("displayName",t))},setState:function(t,e){this.state[t]=e},show:function(t,e,r){var n=o.superclass.show.call(this);return this.type=e,this.state=_.extend({},t||{}),this.state.filterData=r||[],"add"===e&&(this.$(".follow-objTitle").show(),this.createObjSelect()),this.resizedialog(),n},hide:function(){var t=o.superclass.hide.call(this);return this.destroy(),t},destroy:function(){var t=o.superclass.destroy.call(this);return this.select&&this.select.destroy(),t}});e.exports=o});
define("crm-setting/syncrules/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-syncrules"> <p class="page-title">' + ((__t = $t("同步规则")) == null ? "" : __t) + '</p> <div class="behavior-btn-box"> <span class="behavior-btn-box add-btn">' + ((__t = $t("添加")) == null ? "" : __t) + '</span> </div> <div class="sync-rules-table"> </div> </div>';
        }
        return __p;
    };
});