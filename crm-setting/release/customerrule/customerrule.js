define("crm-setting/customerrule/contactpersonnelrelation/contactpersonnelrelation",["./tpl-html"],function(t,e,n){var i=t("./tpl-html");n.exports=Backbone.View.extend({initialize:function(t){this.$el.html(i(_.extend({accountObjName:$t("crm.客户"),leadObjName:$t("crm.销售线索"),contactObjName:$t("crm.联系人")},t))),this.renderCon(t)},renderCon:function(t){this.widget=FxUI.create({wrapper:this.$(".cprelation-content")[0],template:'\n                    <div class="cprelation-content-inner">\n                        <h2 class="cprelation-title">{{$t("人脉关系雷达")}}</h2>\n                        <div class="cprelation-switch">\n                            <fx-switch \n                            :inactive-text="text" \n                            v-model="value" \n                            size="small" \n                            @change="openRelation" \n                            :disabled="disabled">\n                            </fx-switch>\n                        </div>\n                        <div v-show="showStatus" class="cprelation-status">{{statusText}}</div>\n                        <div class="cprelation-tips">{{$t("人脉关系雷达配置注意点")}}</div>\n                    </div>',data:function(){return{text:$t("开启布局组件"),isOpenCPRelation:t.isOpenCPRelation,value:"0"!=t.isOpenCPRelation}},computed:{disabled:function(){return this.value},showStatus:function(){return this.value},statusText:function(){return"2"==this.isOpenCPRelation?$t("已初始化完成"):$t("布局组件已增加，正在处理已有数据，以让已有数据关系在图谱中展示。")}},methods:{openRelation:function(){this.value=!0,this.isOpenCPRelation="1",CRM.util.setConfigValue({key:"contact_member_relationship_func_setting",value:this.isOpenCPRelation})},destroy:function(){this.$destroy()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.widget&&this.widget.destroy&&this.widget.destroy()}})});
define("crm-setting/customerrule/contactpersonnelrelation/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="cprelation-container"> <div class="crm-intro" style="padding-right: 110px;"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <ol> <li>1、" + ((__t = $t("功能开启后会新增「人脉关系雷达」业务组件")) == null ? "" : __t) + "</li> <li>2、" + ((__t = $t("可见组件的人员将会看到组件中的所有数据")) == null ? "" : __t) + "</li> <li>3、" + ((__t = $t("开启布局组件后将不可关闭")) == null ? "" : __t) + "</li> <li>4、" + ((__t = $t("关系展示逻辑：以客户为中心", {
                accountObjName: obj.accountObjName
            })) == null ? "" : __t) + '</li> <li class="crm-ml8">4.1、' + ((__t = $t("展示关联客户的联系人", {
                accountObjName: obj.accountObjName,
                contactObjName: obj.contactObjName
            })) == null ? "" : __t) + '</li> <li class="crm-ml8">4.2、' + ((__t = $t("未转换的线索开启联合查重建立的关系", {
                accountObjName: obj.accountObjName,
                leadObjName: obj.leadObjName
            })) == null ? "" : __t) + '</li> <li class="crm-ml8">4.3、' + ((__t = $t("未转换的线索未开启联合查重建立的关系", {
                accountObjName: obj.accountObjName,
                leadObjName: obj.leadObjName
            })) == null ? "" : __t) + '</li> <li class="crm-ml8">4.4、' + ((__t = $t("已转换的线索通过转换记录数据查询关系", {
                accountObjName: obj.accountObjName,
                leadObjName: obj.leadObjName
            })) == null ? "" : __t) + '</li> <li class="crm-ml8">4.5、' + ((__t = $t("新建编辑客户时建立关系", {
                accountObjName: obj.accountObjName,
                leadObjName: obj.leadObjName
            })) == null ? "" : __t) + '</li> </ol> </div> <div class="cprelation-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/contactrelations",["crm-modules/common/util","./template/contactrelations-html"],function(s,t,e){s("crm-modules/common/util");var i=s("./template/contactrelations-html"),a=Backbone.View.extend({config:{page1:{id:"relations",wrapper:".relations-box"},page2:{id:"rulesetting",wrapper:".rulesetting-box"}},initialize:function(t){this.pages={},this.apiname=t.apiname,this.firstLoaded=!1,this.setElement(t.wrapper),this.$el.html(i({data:this.options}))},events:{"click .crm-contactrelations-tab a":"onHandle"},onHandle:function(t){if(this.firstLoaded)return(t=$(t.target)).hasClass("page1")?this.switchPage("page1"):t.hasClass("page2")&&this.switchPage("page2"),!1},switchPage:function(t){this.renderPage(t)},render:function(t){this.renderPage(t)},renderPage:function(t,e){var i=this,a=i.$(".crm-contactrelations-tab .item"),n=(a.removeClass("cur"),a.filter("."+t).addClass("cur"),i.curId=t);_.map(i.pages,function(t){t.hide()}),i.pages[n]?i.pages[n].show():(a=[".",i.config[n].id,i.config[n].id].join("/"),s.async(a,function(t){i.firstLoaded=!0;t=new t(_.extend({wrapper:i.config[n].wrapper,apiname:i.apiname,firstLoad:e,contractRelationRights:i.options.contractRelationRights}));t.show(),i.pages[n]=t}))},show:function(){this.$el.show(),this.renderPage(this.options.contractRelationRights?"page1":"page2")},hide:function(){this.$el.hide()},destroy:function(){}});e.exports=a});
define("crm-setting/customerrule/contactrelations/relations-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(data, function(item) {
                __p += ' <li class="relation"> <div class="relation-item"> <input class="relation-input j-relation-input" ' + ((__t = item.value == "1" ? 'disabled="disabled"' : "") == null ? "" : __t) + ' value="' + ((__t = item.label) == null ? "" : __t) + '"/> ';
                if (item.value !== "1") {
                    __p += "<span class='delete j-delete'>+</span>";
                }
                __p += " </div> <span class='error-tip'></span> </li>";
            });
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/relations/relations-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(data, function(item) {
                __p += ' <li class="relation"> <div class="relation-item"> <input class="relation-input j-relation-input" value="' + ((__t = item.label) == null ? "" : __t) + '"/> ';
                if (item.value !== "1") {
                    __p += "<span class='delete j-delete'>+</span>";
                }
                __p += " </div> <span class='error-tip'></span> </li>";
            });
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/relations/relations",["crm-modules/common/util","./tpl-html","./relations-html"],function(e,t,n){var i=e("crm-modules/common/util"),a=e("./tpl-html"),l=e("./relations-html");return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.render()},render:function(){var t=this;this.getConfig().done(function(e){t.relations=JSON.parse(e),t.options.contractRelationRights&&t.$el.html(a()),t.options.contractRelationRights&&$(".contactrelations-wrapper").append(l({data:t.relations}))})},show:function(){this.options.contractRelationRights&&this.$el.show()},hide:function(){this.$el.hide()},events:{"click .j-add-item":"addItem","click .j-delete":"deleteItem","blur .j-relation-input":"blurInput","click .j-save":"saveHandle"},addItem:function(){var e={value:"",label:""};this.relations.push(e),$(".contactrelations-wrapper").append(l({data:[e]}))},blurInput:function(e){var e=$(e.target),t=e.parents(".relation"),n=t.index(),i=e.val(),a=(this.relations[n].value,this.relations[n].label);i?i==a?t.removeClass("error"):this.duplicateCheck(i)?(e.val(""),t.addClass("error"),t.find(".error-tip").html($t("关系名称不可重复，请重新编辑"))):(t.removeClass("error"),this.relations[n].label=i):this.emptyError(n)},emptyError:function(e){e=$(".relation:nth-child("+(+e+1)+")");e.addClass("error"),e.find(".error-tip").html($t("关系不可为空"))},deleteItem:function(e){var t=this,n=$(e.target).parents(".relation").index(),e=t.relations[n].value;e?this.checkUsed(e).then(function(e){e?CRM.util.alert($t("关系已被使用，不可删除")):t.deleteHandle(n)}):this.deleteHandle(n)},deleteHandle:function(e){this.relations.splice(e,1),$(".relation:nth-child("+(+e+1)+")").remove()},duplicateCheck:function(t){return 0<=this.relations.findIndex(function(e){return e.label==t})},getConfig:function(){return new Promise(function(t,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"contact_relationship_type"},success:function(e){0===e.Result.StatusCode?e.Value&&e.Value.value&&t(e.Value.value):i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},saveHandle:function(){var t,e;this.validate()||(e=(t=this).parseRelations(),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"contact_relationship_type",value:JSON.stringify(e)},success:function(e){0===e.Result.StatusCode?(t._saveContactRelationLog(),CRM.util.remind(1,$t("保存成功"))):(t.render(),CRM.util.alert(e.Result.FailureMessage))}},{errorAlertModel:1}))},checkUsed:function(n){return new Promise(function(t,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/contact_atlas/service/check_relation_type_is_used",data:{relation_type:n},success:function(e){0===e.Result.StatusCode?t(e.Value.has_used):CRM.util.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},validate:function(){var n=this;return this.relations.find(function(e,t){e.label&&""!=e.label||n.emptyError(t)}),this.$el&&this.$(".error")[0]},parseRelations:function(){var t=this,n=[];return this.relations.forEach(function(e){e.label&&(e={value:e.value||t.randomID(),label:e.label},n.push(e))}),n},randomID:function(){for(var e="",t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",n=8;0<n;--n)e+=t[Math.floor(Math.random()*t.length)];return e+=(new Date).getTime()},_saveContactRelationLog:function(){CRM.util.sendLog("AccountObj","ManageContactRelation",{eventId:"addContactRelation"})}})});
define("crm-setting/customerrule/contactrelations/relations/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-contactrelations"> <h3 class="title">' + ((__t = $t("关系列表")) == null ? "" : __t) + '</h3> <div class="content"> <div class="contactrelations-wrapper"></div> <div class="add-wrapper"><a class="add-item j-add-item">+ ' + ((__t = $t("添加选项")) == null ? "" : __t) + '</a></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/rulesetting/rulesetting",["crm-modules/common/util","./tpl-html"],function(e,t,c){var n=e("crm-modules/common/util"),i=e("./tpl-html"),o=0;return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.getConfig()},render:function(){this.comps={},this.$el.html(i()),this.renderConcatSetting()},events:{"click .j-save":"setConfig","click .j-checked-item":"change"},renderConcatSetting:function(){this.comps.checkbox=FxUI.create({wrapper:this.$el.find(".j-checked-item")[0],template:"<fx-checkbox v-model=\"checked\">{{$t('同步变更本客户下联系人的负责人')}}</fx-checkbox>",data:function(){return{checked:!1}}}),this.comps.button=FxUI.create({wrapper:this.$el.find(".j-save")[0],template:'<fx-button type="primary" :disabled="disabled">{{$t(\'保存\')}}</fx-button>',data:function(){return{disabled:!0}}})},getConfig:function(){var t=this;t._getAjax&&(t._getAjax.abort(),t._getAjax=null),t._getAjax=n.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"contact_owner_rule_setting"},success:function(e){t.show(),t.render(),0===e.Result.StatusCode?"1"==(o=e.Value.value)&&(t.comps.checkbox.checked=!0):n.alert(e.Result.FailureMessage)},complete:function(){t._getAjax=null}},{errorAlertModel:1})},setConfig:function(){var t,e,c;this.comps.button.disabled||(t=0,t=this.comps.checkbox.checked?1:0,e=o,c=this,n.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"contact_owner_rule_setting",oldValue:e,value:t},success:function(e){0==e.Result.StatusCode?(n.remind(1,$t("保存成功")),o=t,c.comps.button.disabled=!0):n.alert(e.Result.FailureMessage)},complete:function(){c.isSetting=!1}},{errorAlertModel:1}))},change:function(){this.comps.button.disabled=!1},show:function(){this.$el.show()},hide:function(){this.$el.hide()}})});
define("crm-setting/customerrule/contactrelations/rulesetting/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-contactrelations"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ol> <li>1、" + ((__t = $t("开启后客户进行退回、收回时将会同步清空联系人负责人，客户进行分配领取时将同步变更客户下联系人的负责人为客户的负责人。")) == null ? "" : __t) + '</li> </ol> </div> <div class="content"> <!-- <div class="contactrelations-wrapper"></div> --> <div class="add-wrapper"><a class="add-item j-checked-item"></a></div> <div class="mn-checkbox-box j-button"> <span class="j-save"></span> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/template/contactrelations-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="contactrelations-container"> <div class="crm-module-con-contactrelations"> <div class="crm-contactrelations-tab"> ';
            if (data.contractRelationRights) {
                __p += ' <a class="item page1 cur" href="#crm/setting/customerrule/contactrelations/=/page1">' + ((__t = $t("客户联系人关系设置")) == null ? "" : __t) + "</a> <i></i> ";
            }
            __p += ' <a class="item page2" href="#crm/setting/customerrule/contactrelations/=/page2">' + ((__t = $t("联系人负责人规则设置")) == null ? "" : __t) + "</a> </div> <div> ";
            if (data.contractRelationRights) {
                __p += ' <div class="item relations-box"> <div class="crm-loading"></div> </div> ';
            }
            __p += ' <div class="item rulesetting-box" style="display:none;"> <div class="crm-loading"></div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/contactrelations/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-contactrelations"> <h3 class="title">' + ((__t = $t("关系列表")) == null ? "" : __t) + '</h3> <div class="content"> <div class="contactrelations-wrapper"></div> <div class="add-wrapper"><a class="add-item j-add-item">+ ' + ((__t = $t("添加选项")) == null ? "" : __t) + '</a></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerbehavior/checkboxComponent/checkboxComponent",["./template/checkboxComponent-html","crm-modules/common/util"],function(t,e,n){var o=t("./template/checkboxComponent-html");t("crm-modules/common/util");n.exports=Backbone.View.extend({options:{el:$("body"),title:$t("复选框"),data:[{name:$t("新建"),id:"add",status:!0}]},events:{"click .mn-checkbox-item":"selectEve","click .checkboxComponent-selectAll":"selectAll","click .checkboxComponent-clear":"clearAll"},initialize:function(){this.$el=this.options.el,this.$el.append(o({title:this.options.title})),this.updateBtn(),this.render()},render:function(){var e,n="";this.options.data.length&&(_.forEach(this.options.data,function(t){this._hasValue(t.name)&&this._hasValue(t.id)&&_.isBoolean(t.status)&&(e=t.status?"mn-selected":"",n+='<span class="mn-checkbox-box"><span class="mn-checkbox-item '+e+'" data-id="'+t.id+'"></span><span class="checkbox-lable" title="'+t.name+'">'+t.name+"</span></span>")},this),this.$(".checkboxComponent-main").html(n))},_hasValue:function(t){return!(!_.isString(t)||""===t)},updateBtn:function(){var e=!0;_.each(this.options.data,function(t){t.status||(e=!1)}),e?(this.$(".checkboxComponent-selectAll").hide(),this.$(".checkboxComponent-clear").show()):(this.$(".checkboxComponent-selectAll").show(),this.$(".checkboxComponent-clear").hide())},selectAll:function(){_.each(this.options.data,function(t){t.status=!0}),this.updateBtn(),this.render()},clearAll:function(){_.each(this.options.data,function(t){t.status=!1}),this.updateBtn(),this.render()},selectEve:function(t){var t=$(t.currentTarget),e=t.data().id;t.hasClass("mn-selected")?this.updateData(e,!1):this.updateData(e,!0),this.updateBtn()},updateData:function(t,e){for(var n=this.options.data.length;n--;)if(this.options.data[n].id===t)return void(this.options.data[n].status=e)},getValue:function(){return this.options.data},destroy:function(){this.events=null,this.$(".checkboxComponent").remove()}})});
define("crm-setting/customerrule/customerbehavior/checkboxComponent/template/checkboxComponent-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="checkboxComponent b-g-crm"> <div class="checkboxCom-title"> <span class="checkboxCom-titleName"> ' + ((__t = title) == null ? "" : __t) + ' </span> <span class="checkboxComponent-selectAll">' + ((__t = $t("选择全部")) == null ? "" : __t) + '</span> <span class="checkboxComponent-clear">' + ((__t = $t("清空")) == null ? "" : __t) + '</span> </div> <div class="checkboxComponent-main"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerbehavior/config",[],function(e,i,t){t.exports={AccountObj:[{is_enable:!1,setting_type:"1",action_code:"Edit",action_label:$t("编辑"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"Return",action_label:$t("退回"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"TakeBack",action_label:$t("收回"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"AddAttach",action_label:$t("添加附件"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"ChangePartner",action_label:$t("更换合作伙伴"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!0,setting_type:"1",action_code:"ChangeOwner",action_label:$t("更换负责人"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"SendMail",action_label:$t("发邮件"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"},{is_enable:!1,setting_type:"1",action_code:"Merge",action_label:$t("合并"),is_visible:!0,is_system:!1,source_object_api_name:"AccountObj"}],OpportunityObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"ChangeStage",action_label:$t("商机阶段变更"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}],NewOpportunityObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"ChangeStage",action_label:$t("商机2.0阶段变更"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}],ContactObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建"),is_visible:!0,is_system:!1,source_object_api_name:"ContactObj"},{is_enable:!1,setting_type:"1",action_code:"Edit",action_label:$t("编辑"),is_visible:!0,is_system:!1,source_object_api_name:"ContactObj"},{is_enable:!1,setting_type:"1",action_code:"Associate",action_label:$t("关联客户"),is_visible:!0,is_system:!1,source_object_api_name:"ContactObj"},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1,source_object_api_name:"ContactObj"},{is_enable:!1,setting_type:"1",action_code:"SendMail",action_label:$t("发邮件"),is_visible:!0,is_system:!1,source_object_api_name:"ContactObj"}],VisitingObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建拜访"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"FinishVisit",action_label:$t("crm.完成拜访"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}],otherObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}],CheckinsObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建外勤签到"),is_visible:!0,is_system:!1,source_object_api_name:"CheckinsObj"},{is_enable:!1,setting_type:"1",action_code:"FinishCheckin",action_label:$t("完成外勤"),is_visible:!0,is_system:!1,source_object_api_name:"CheckinsObj"},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1,source_object_api_name:"CheckinsObj"}]}});
define("crm-setting/customerrule/customerbehavior/customerbehavior",["crm-modules/common/util","./followbehaviordialog/followbehaviordialog","../template/customerbehavior-html","./config","crm-widget/table/table"],function(t,e,i){var a=t("crm-modules/common/util"),n=t("./followbehaviordialog/followbehaviordialog"),o=t("../template/customerbehavior-html"),r=t("./config"),s=t("crm-widget/table/table");i.exports=Backbone.View.extend({initialize:function(){this.isActiveArr=[],this.hasComplete=!1,this.initCustomerBehavior()},events:{"click .crm-g-radio":"toggleHandle","click .setting-btn":"fbHandle","click .add-btn":"fbHandle"},initCustomerBehavior:function(){var t=this;this.getAllObject(function(){t.getFollowDealSetting(function(){t.resetData(),t.renderCustomerBehavior()})})},resetData:function(){var a=this,s=this.CustomerFollowDealSetting.CustomerFollowSetting,l=[];this.checkCustomer(this.allObj),_.each(this.allObj,function(t,e){var i,o=t.api_name,t={apiName:o,displayName:t.display_name,actionList:[]},n=((r=$.extend(!0,{},r)).hasOwnProperty(o)?t.actionList=_.map(r[o],function(t){return t.source_object_api_name=o,t}):(i=$.extend(!0,{},r.otherObj),t.actionList=_.map(i,function(t){return t.source_object_api_name=o,t})),_.find(s,function(t){return t.apiName===o}));n?_.each(t.actionList,function(e){var t=_.find(n.actionList,function(t){return t.action_code===e.action_code});t&&(e=_.extend(e,t))}):_.each(t.actionList,function(t){t.is_enable=!1}),t.timestamp=a.getTime()+e,a.checkAction(t)&&l.push(t)}),this.CustomerFollowDealSetting.CustomerFollowSetting=l,this.currentFollowObj=this.CustomerFollowDealSetting.CustomerFollowSetting[0]},getTime:function(){return(new Date).getTime()},checkCustomer:function(t){_.find(t,function(t){return"AccountObj"===t.api_name})||t.push({api_name:"AccountObj",display_name:$t("客户")})},checkAction:function(t){if(!["LeadsObj","MarketingEventObj","ProductObj","SalesOrderProductObj","ReturnedGoodsInvoiceProductObj"].includes(t.apiName)){var e=_.find(t.actionList,function(t){return t.is_visible}),i=_.find(t.actionList,function(t){return t.is_enable});if(e)return e;i&&this.isActiveArr.push(t)}return!1},getAllObject:function(e){var i=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList",data:{describeApiName:"AccountObj",includeRefList:!0,includeDetailList:!0,excludeInvalid:!0},success:function(t){0==t.Result.StatusCode?(i.allObj=i.filterObject(t.Value.lookupDescribeList),e&&e()):a.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},filterObject:function(t){var e=["LeadsTransferLogObj"];return _.filter(t,function(t){return!e.includes(t.api_name)})},getFollowDealSetting:function(e){var i=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/get_object_follow_deal_setting",data:{objectApiName:"AccountObj",followDealSettingType:1},success:function(t){0==t.Result.StatusCode?(i.CustomerFollowDealSetting={},t.Value.dataList.length?i.CustomerFollowDealSetting.CustomerFollowSetting=_.find(t.Value.dataList,function(t){return 1==t.setting_type}).settingList:i.CustomerFollowDealSetting.CustomerFollowSetting=[],e()):a.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},toggleHandle:function(t){t=$(t.target);t.is(".state-active")||(t.closest(".trade-behavior-wrap").find(".crm-g-radio").removeClass("state-active"),t.addClass("state-active"))},transformData:function(t){var i=this,t=$.extend(!0,{},t),e=[],o={},n=[];return i.isActiveArr.length&&(t.CustomerFollowSetting=t.CustomerFollowSetting.concat(i.isActiveArr)),o.setting_type=1,_.each(t.CustomerFollowSetting,function(t){var e=a.deepClone(t.actionList);(e="AccountObj"===t.apiName?i.supplementAccountAction(e):e)&&e.length&&(t.actionList=e,n.push(t))}),o.settingList=n,e.push(o),e},supplementAccountAction:function(t){var e=[{is_enable:!0,setting_type:"1",action_code:"Allocate",action_label:$t("分配"),is_visible:!0,is_system:!0,source_object_api_name:"AccountObj"},{is_enable:!0,setting_type:"1",action_code:"Choose",action_label:$t("领取"),is_visible:!0,is_system:!0,source_object_api_name:"AccountObj"}];return t.concat(e)},setCustomerFollowDealSetting:function(e){var i=this,t=this.transformData(e);i.hasComplete||(i.hasComplete=!0,a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/save_object_follow_deal_setting",data:{objectApiName:"AccountObj",dataList:t,followDealSettingType:1,userDefineSetting:!1},success:function(t){i.hasComplete=!1,0==t.Result.StatusCode?(i.CustomerFollowDealSetting=e,i.renderCustomerBehavior(),a.remind(1,$t("设置成功"))):a.remind(3,t.Result.FailureMessage||$t("设置失败，请联系客服"))},error:function(){a.remind(3,$t("设置失败，请联系客服")),i.hasComplete=!1}},{errorAlertModel:1}))},fbHandle:function(){this.createDialog("add")},createDialog:function(t){var i=this,e=this.CustomerFollowDealSetting,o="add"===t?$t("添加跟进行为"):$t("编辑{{name}}下的跟进行为",{name:i.currentFollowObj.displayName});this.fb=new n({title:o}),this.fb.on("suc",function(t,e){i.CustomerFollowDealSetting=$.extend(!0,{},t),i.currentFollowObj=$.extend(!0,{},e),i.setCustomerFollowDealSetting(i.CustomerFollowDealSetting)}),this.fb.show(e,this.currentFollowObj,t)},renderCustomerBehavior:function(){this.el.innerHTML=o(this.CustomerFollowDealSetting),this.calcHeight(),this.createBehaviorTable()},calcHeight:function(){var t=this.$el.parents(".tab-con").height()-200;this.$(".behavior-table-box").height(t)},createBehaviorTable:function(){var n=this,t=this.formatDataToTable(this.CustomerFollowDealSetting.CustomerFollowSetting);this.table&&this.table.destroy(),t.length?(this.$(".setting-btn").hide(),this.$(".add-btn").show(),this.table=new s({$el:this.$(".behavior-table-box"),showPage:!1,doStatic:!0,colResize:!0,columns:[{data:"objName",title:$t("对象名称")},{data:"behavior",title:$t("跟进行为")},{data:null,title:$t("操作"),width:"100px",render:function(t,e,i){return'<span class="js-edit">'+$t("编辑")+'</span><span class="js-del">'+$t("删除")+"</span>"}}]}),this.table.on("trclick",function(e,t,i){var o;i.hasClass("js-edit")?(n.currentFollowObj=_.find(n.CustomerFollowDealSetting.CustomerFollowSetting,function(t){return t.apiName===e.apiName}),n.createDialog("edit")):i.hasClass("js-del")&&(o=a.confirm("<p>"+$t("确定要删除该对象下的所有跟进行为吗")+"?</p>",$t("删除"),function(){o.hide(),n.actionSetting(e.apiName),n.setCurrentObj(e.apiName),n.setCustomerFollowDealSetting(n.CustomerFollowDealSetting)}))}),this.table.doStaticData(t)):(this.$(".setting-btn").show(),this.$(".add-btn").hide())},actionSetting:function(t){for(var e=this.CustomerFollowDealSetting.CustomerFollowSetting,i=e.length;i--;)if(e[i].apiName===t)return void _.each(e[i].actionList,function(t){t.is_enable=!1})},setCurrentObj:function(e){this.currentFollowObj=_.find(this.CustomerFollowDealSetting.CustomerFollowSetting,function(t){return t.apiName===e})},formatDataToTable:function(t){var o=[];return t=this.sortData(t),t=_.filter(t,function(t){return"AccountAddrObj"!=t.apiName&&"AccountFinInfoObj"!=t.apiName}),_.forEach(t,function(t){var e="",i=!1;_.forEach(t.actionList,function(t){t.is_enable&&t.is_visible&&(i=!0,e+=t.action_label+"、")}),i&&(e=e.substring(0,e.length-1),o.push({objName:t.displayName,apiName:t.apiName,behavior:e}))}),o},sortData:function(t){return t.sort(function(t,e){return e.timestamp-t.timestamp})},setBasicHandle:function(t){var t=$(t.target),e=t.attr("data-key"),i=t.hasClass("mn-selected")?"1":"0";this.setConfig({key:e,value:"11"==e?"0"==i?"1":"0":i},t)},setConfig:function(t,e){a.setConfigValues(t).then(function(){a.remind(1,$t("设置成功"))},function(){a.remind(3,$t("设置失败")),e.toggleClass("mn-selected")})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.fb&&(this.fb.destroy(),this.fb=null)}})});
define("crm-setting/customerrule/customerbehavior/followbehaviordialog/followbehaviordialog",["crm-widget/dialog/dialog","../checkboxComponent/checkboxComponent","crm-widget/select/select"],function(t,e,i){var o=t("crm-widget/dialog/dialog"),s=t("../checkboxComponent/checkboxComponent"),c=t("crm-widget/select/select"),a=o.extend({attrs:{width:640,className:"crm-s-customer-follow",title:$t("添加跟进行为"),showBtns:!0,content:'<div class="follow-box"><div class="follow-objTitle">'+$t("所属对象")+'</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"setData"},setData:function(){this.saveSelectBox(),this.trigger("suc",this.data,this.currentObj),this.hide()},createObjSelect:function(){var t=this;this.select&&this.select.destroy(),this.select=new c({$wrap:this.$(".follow-objSelect"),zIndex:999,width:280,options:_.map(_.filter(t.data.CustomerFollowSetting,function(t){return"AccountAddrObj"!=t.apiName&&"AccountFinInfoObj"!=t.apiName}),function(t){return{name:t.displayName,value:t.apiName}}),defaultValue:t.currentObj.apiName}),this.select.on("change",function(e){t.saveSelectBox(),t.currentObj=_.find(t.data.CustomerFollowSetting,function(t){return t.apiName===e}),t.createCheckbox()})},saveSelectBox:function(){var t=this.checkboxCom.getValue(),e=(this.currentObj.actionList=_.map(t,function(t){return t.is_enable=t.status,delete t.name,delete t.id,delete t.status,t}),_.findWhere(this.data.CustomerFollowSetting,{apiName:this.currentObj.apiName}));"add"===this.type&&(e.timestamp=this.getTime()),e.actionList=t},getTime:function(){return(new Date).getTime()},show:function(t,e,i){var o=a.superclass.show.call(this);return this.type=i,this.data=t,this.currentObj=e,"add"===i&&(this.$(".follow-objTitle").show(),this.createObjSelect()),this.createCheckbox(i),this.resizedialog(),o},createCheckbox:function(){this.checkboxCom&&this.checkboxCom.destroy(),this.checkboxCom=new s({el:this.$(".follow-wrap"),data:this.formatDataToCheckbox(this.currentObj.actionList),title:$t("跟进行为")})},formatDataToCheckbox:function(t){var e=[];return _.each(t,function(t){t.is_visible&&(t.name=t.action_label,t.id=t.action_code,t.status=t.is_enable,e.push(t))}),e},hide:function(){var t=a.superclass.hide.call(this);return this.destroy(),t},destroy:function(){var t=a.superclass.destroy.call(this);return this.select&&this.select.destroy(),this.checkboxCom&&this.checkboxCom.destroy(),t}});i.exports=a});
define("crm-setting/customerrule/customercheck/customercheck",["crm-modules/common/util","../template/customercheck-html","./setcheckerdialog/setcheckerdialog"],function(e,t,a){var l=e("crm-modules/common/util"),s=e("../template/customercheck-html"),c=e("./setcheckerdialog/setcheckerdialog");a.exports=Backbone.View.extend({initialize:function(e){this.el.innerHTML=s(e)},events:{"click .mn-radio-item":"setCheckBoxHandle","click .add-checker":"addCheckerHandle","click .close-name":"delCherkNameHandle"},setCheckBoxHandle:function(e){var t,a,s,c,n=this,i=$(e.currentTarget);if(!i.hasClass("mn-selected")&&!i.hasClass("disabled-selected"))return a=i.closest(".crm-column"),s=i.attr("data-value"),e=i.attr("data-title"),c=[$t("未开启"),$t("已开启")][s]+e,e=['<span class="crm-ico-bigwarn" style="display:inline-block;vertical-align:top;"></span>','<span style="display:inline-block;width:380px;padding:4px 0 0 10px;">'+$t("当您选择关闭客户报备功能时将不再支持开启并在系统中移除本功能。请在")+'<a href="#crmmanage/=/module-approval">'+$t("审批流程管理")+"</a>"+$t("中设置您的客户审批流程。")+"</span>"].join(""),0==i.attr("data-value")?(t=l.confirm(e,$t("提示"),function(){t.destroy(),i.parent().find(".mn-selected").removeClass("mn-selected"),i.addClass("mn-selected"),i.siblings().addClass("disabled-selected"),i.closest(".mn-radio-box").addClass("mn-disabled"),l.setConfigValue({key:3,value:i.attr("data-value")}).then(function(){l.remind(1,$t("设置成功")),$(".status",a).html(c),$(".status",a),$(".b-g-btn",a).removeClass("b-g-btn-disabled"),$(".choose-checker",a).toggle("1"==s),$(".repeat-config",a).toggle("1"==s),n.$(".crm-warn-bar").hide().eq(1).show()},function(){l.remind(3,$t("设置失败")),i.toggleClass("mn-selected")})},{stopPropagation:!1}),!1):void l.setConfigValue({key:3,value:i.attr("data-value")}).then(function(){l.remind(1,$t("设置成功")),$(".status",a).html(c),$(".status",a),$(".b-g-btn",a).removeClass("b-g-btn-disabled"),$(".choose-checker",a).toggle("1"==s),$(".repeat-config",a).toggle("1"==s)},function(){l.remind(3,$t("设置失败")),i.toggleClass("mn-selected")})},addCheckerHandle:function(e){var a=this,t=a.$(".check-employees"),s=[];$(".name-item",t).each(function(e,t){s.push($(t).attr("data-id"))}),a.setCheckerDialog||(a.setcheckerdialog=new c,a.setcheckerdialog.on("suc",function(e){var t=a.getCheckerDatas();_.each(e,function(e){t.push(+e)}),a.saveChecker(!0,t)})),a.setcheckerdialog.show(s)},delCherkNameHandle:function(e){var e=$(e.target).parent(".name-item").data("id")||null,t=-1,a=this.getCheckerDatas(),s="1"==this.$(".mn-radio-item.mn-selected").attr("data-value");null!=e&&(t=a.indexOf(+e)),a.splice(t,1),this.saveChecker(s,a)},getCheckerDatas:function(){var a=[];return this.$(".name-item").each(function(e,t){a.push(+$(t).attr("data-id"))}),a},saveChecker:function(e,t){var a=this;l.FHHApi({url:"/EM1HNCRM/API/v1/object/account/service/set_account_filling_checker ",data:{ConfigValue:e,CheckerIDs:t},success:function(e){0==e.Result.StatusCode?(l.remind(1,$t("保存成功")),a.renderChecker(t)):l.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},renderChecker:function(e){var t=this.$(".check-employees"),a="";_.each(e,function(e){e=l.getEmployeeById(e);a+='<div class="name-item" data-id="'+e.id+'"><img src="'+e.profileImage+'" /><span class="name-text">'+e.name+'</span><span class="close-name">×</span></div>'}),t.html(a)},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.setcheckerdialog&&(this.setcheckerdialog.destroy(),this.setcheckerdialog=null)}})});
define("crm-setting/customerrule/customercheck/setcheckerdialog/setcheckerdialog",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector"],function(e,t,s){e("crm-modules/common/util");var l=e("crm-widget/dialog/dialog"),c=e("crm-widget/selector/selector"),i=l.extend({attrs:{width:500,className:"crm-s-customerrule",title:$t("新增审核人"),showBtns:!0,content:'<div class="set-dialog"><div class="lb-tit">'+$t("审核人")+'</div><div class="select-box"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"enterHandle"},enterHandle:function(){var e=this,t=e.selectbar.getValue("member")||[];e.hide(),e.trigger("suc",t)},initSelectBar:function(){var e=this;e.selectbar=new c({$wrap:$(".select-box",e.element),zIndex:1e3,member:!0,excludeItems:{member:e.ids},single:!1,label:$t("添加员工")})},show:function(e){var t=i.superclass.show.call(this);return this.ids=e,this.initSelectBar(),t},hide:function(){var e=i.superclass.hide.call(this);return this.selectbar&&this.selectbar.destroy(),this.selectbar=null,$(".select-box",this.element).html(""),e},destroy:function(){var e=i.superclass.destroy.call(this);return this.selectbar&&this.selectbar.destroy(),this.selectbar=null,e}});s.exports=i});
define("crm-setting/customerrule/customerlimit/circlelimitdialog/circlelimitdialog",["crm-widget/dialog/dialog","../template/limit-circle-html"],function(e,t,i){var c=e("crm-widget/dialog/dialog"),l=e("../template/limit-circle-html"),s=c.extend({conflictRuleList:[],attrs:{title:$t("提示"),width:640,height:450,showScroll:!1,showBtns:!0,className:"crm-s-customerrule"},events:{"click .b-g-btn-cancel":"hide","click .climits-employee":"checkNavHandle","click .b-g-btn":"submit"},show:function(e){var t=s.superclass.show.call(this);return this.setContent(l(e)),this.conflictRuleList=e.ConflictRuleList,t},checkNavHandle:function(e){e=$(e.currentTarget);e.hasClass("active")||(this.$(".climits-employee").removeClass("active"),e.addClass("active"),this.$(".climits-circle").hide(),this.$("."+e.data("employee")+"circle").show())},submit:function(){var i,c=this.$(".climits-circle .mn-selected"),l=[];this.conflictRuleList.forEach(function(e,t){i=c.eq(t).data("index"),l.push(_.extend(e.EmployeeConflictRuleList[i],{EmployeeID:e.EmployeeID}))}),this.trigger("success",l)},hide:function(){return s.superclass.hide.call(this)},destroy:function(){return s.superclass.destroy.call(this)}});i.exports=s});
define("crm-setting/customerrule/customerlimit/customerlimit",["crm-modules/common/util","crm-modules/components/formbox/formbox","base-modules/vue-selector/selector-input","./template/limit-html","./template/limit-circle-html","./template/limit-rule-item-html","./template/limit-rule-container-html"],function(l,e,t){var h=l("crm-modules/common/util"),i=l("crm-modules/components/formbox/formbox").CheckBox,r=l("base-modules/vue-selector/selector-input"),a=l("./template/limit-html"),n=(l("./template/limit-circle-html"),l("./template/limit-rule-item-html")),s=l("./template/limit-rule-container-html");t.exports=Backbone.View.extend({initialize:function(){var t=this;this.el.innerHTML=a(),FS.contacts.getUserGroups().then(function(e){t.userGroups=e,t.renderPage()}),h.fixInputEvent(".b-g-ipt",function(e){var t=$(this).val();$(this).val(h.normalizeNum(t))},this.$el)},events:{"click .edite-limit":"editeLimitHandle","click .preview-limit":"previewLimitHandle","click .save-limit":"saveLimitHandle","click .add-limit":"addLimitHandle","click .del-limit":"delLimitHandle","click .cancel-limit":"cancelLimitHandle"},renderPage:function(){var t=this;h.FHHApi({url:"/EM1HNCRM/API/v1/object/account_limit_rule/service/get_account_limit_rule",success:function(e){0==e.Result.StatusCode?(t.limitList=t.formatLimitData(e.Value.limitRuleList),t.$(".rule-container").html(s({limitList:t.limitList}))):h.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},editeLimitHandle:function(e){e=$(e.target).closest(".rule-item").index();this.$(".b-g-ipt").length&&this.$(".rule-container").html(s({limitList:this.limitList})),this.renderLimitItemRule(this.$(".rule-item").eq(e))},renderLimitItemRule:function(e,t){var i=$(".item-id",e),l=$(".num",e),r=e.data(),a=r.members.toString(),n=r.groups.toString(),r=r.usergroups.toString();this.initSelectorInput($(".item-tit",e).empty(),0<(a+n+r).length&&{member:a?a.split(","):[],group:n?n.split(","):[],usergroup:r?r.split(","):[]}),i.html('<input type="text" maxlength="10" class="b-g-ipt" value="'+(t?e.prev().length?Number(e.prev().find(".item-id").html())+1:1:i.html())+'" />'),l.html('<input type="text" maxlength="9" class="b-g-ipt" value="'+l.html()+'" />'),this.initCheckBoxs(e),$(".item-operate",e).closest(".rule-item").toggleClass("cur")},initSelectorInput:function(e,t){this.selectorinput&&this.selectorinput.destroy(),this.selectorinput=new r(_.extend({el:e,parentNode:e,member:!0,group:!0,usergroup:!0,label:$t("选择同事部门用户组")},t?{defaultSelectedItems:t}:{}))},initCheckBoxs:function(e){var t=$(".highseas-claim",e),e=$(".deal-claim",e);this._checkbox&&this._checkbox.destroy(),this._dealCheckbox&&this._dealCheckbox.destroy(),this._checkbox=new i({el:t,options:[{name:t.data("name"),checked:t.data("value"),text:$t("占有保有量")}]}),this._dealCheckbox=new i({el:e,options:[{name:e.data("name"),checked:e.data("value"),text:$t("占有保有量")}]})},previewLimitHandle:function(e){var t=this,e=$(e.target).closest(".rule-item"),i={groupId:e.data("groupid"),title:$t("预览规则")+e.find(".item-id").html()};t.preveiwlimitdialog?t.preveiwlimitdialog.show(i):l.async("./preveiwlimitdialog/preveiwlimitdialog",function(e){t.preveiwlimitdialog=new e,t.preveiwlimitdialog.show(i)})},saveLimitHandle:function(e){var t=this,e=$(e.target),i=e.closest(".rule-item"),l=($(".item-tit",i),i.data("groupid")),r=$(".item-id .b-g-ipt",i).val(),a=$(".num .b-g-ipt",i).val(),n=$(".highseas-claim",i).data("name"),i=$(".deal-claim",i).data("name"),s=t.selectorinput.getSelectedItems(),n=t._checkbox.getValueByName(n),i=t._dealCheckbox.getValueByName(i),o=(n?$t("是"):$t("否"),i?$t("是"):$t("否"),[]),u=[],m=[],c=[],d=[],p=[];s.group.length<=0&&s.member.length<=0&&s.usergroup.length<=0?h.remind(3,$t("请选择设置范围")):""==r||10<r.length?h.remind(3,$t("请填写规则名")):10<r.length?h.remind(3,$t("规则名不能超过10个字符")):-1<_.pluck(t.$(".item-id"),"textContent").indexOf(r)?h.remind(3,$t("规则名不能重复")):""==a?h.remind(3,$t("请填写上限个数")):"0"==a.slice(0,1)?h.remind(3,$t("上限格式不正确")):(s={departmentList:_.map(s.group,function(e){FS.contacts.getCircleById(e.id);return e.name&&(o.push(e.id),c.push(e.name)),e.id+""}),employeeList:_.map(s.member,function(e){FS.contacts.getEmployeeById(e.id);return e.name&&(u.push(e.id),d.push(e.name)),e.id+""}),userGroupList:_.map(s.usergroup,function(e){return e.name&&(m.push(e.id),p.push(e.name)),e.id+""})},t.curCricleLimitsData=null,t.curCricleLimitsData={limitRule:{name:r,dataRule:s,limitNumber:a,includeHighSeasCustomer:n,includeDealCustomer:i,groupId:l,id:l},employeeRuleList:[]},e.removeClass("save-limit"),t.saveLimit(t.curCricleLimitsData,function(e){t.renderPage()},!0))},addLimitHandle:function(){var e=$(n());this.$(".b-g-ipt").length&&this.$(".rule-container").html(s({limitList:this.limitList})),this.$(".rule-container").append(e),this.renderLimitItemRule(e,!0)},delLimitHandle:function(e){var t=this,e=$(e.target).closest(".rule-item").data();e.groupid;h.FHHApi({url:"/EM1HNCRM/API/v1/object/account_limit_rule/service/delete_account_limit_rule",data:{groupId:e.groupid},success:function(e){0==e.Result.StatusCode?e.Value.ConflictRuleList&&0<e.Value.ConflictRuleList.length?t.showCircleLimitsDialog(e.Value):(h.remind(1,$t("删除成功")),t.renderPage()):h.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},cancelLimitHandle:function(e){e=$(e.target).closest(".rule-item");""==e.data().groupid?e.remove():this.$(".rule-container").html(s({limitList:this.limitList}))},formatLimitData:function(e){var l=[],r=this;return e=_.groupBy(e,function(e){return"limit_"+e.groupId}),_.each(e,function(e,t){var i=l.length;l[i]=l[i]||{},l[i].employeeRuleList=[],_.each(e,function(e){l[i].name=e.name,l[i].dataRule=e.dataRule&&r.parseDataRule(e.dataRule),l[i].employeeRuleList.concat(e.employeeRuleList||[]),l[i].groupId=t.split("_")[1]||"",l[i].id=t.split("_")[1]||"",l[i].limitNumber=e.limitNumber,l[i].includeHighSeasCustomer=e.includeHighSeasCustomer,l[i].includeDealCustomer=e.includeDealCustomer})}),l},parseDataRule:function(e){var t=[],l=this,r=(e.departmentList&&0<e.departmentList.length&&e.departmentList.forEach(function(e){FS.contacts.getCircleById(e)&&t.push(FS.contacts.getCircleById(e))}),e.departmentList=t,e.employeeList=e.employeeList&&0<e.employeeList.length&&e.employeeList.map(function(e){return FS.contacts.getEmployeeById(e)}),[]);return e.userGroupList=e.userGroupList&&0<e.userGroupList.length?e.userGroupList:[],e.userGroupList.forEach(function(i){l.userGroups.forEach(function(e,t){e.id==i&&r.push(l.userGroups[t])})}),e.userGroupList=r,e},saveLimit:function(e,t,i){var l=this;h.FHHApi({url:"/EM1HNCRM/API/v1/object/account_limit_rule/service/save_account_limit_rule",data:e,success:function(e){0==e.Result.StatusCode?(i&&e.Value.ConflictRuleList&&0<e.Value.ConflictRuleList.length?l.showCircleLimitsDialog(e.Value):h.remind("1",$t("保存成功")),t&&t(e.Value)):h.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},showCircleLimitsDialog:function(t){var i=this;i.circlelimitdialog?i.circlelimitdialog.show(t):l.async("./circlelimitdialog/circlelimitdialog",function(e){i.circlelimitdialog=new e,i.circlelimitdialog.show(t),i.circlelimitdialog.on("success",function(e){i.saveLimit({CustomerLimit:i.curCricleLimitsData.CustomerLimit,groupId:i.curCricleLimitsData.groupId,employeeList:e},function(){i.circlelimitdialog.hide()},!1)})})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var t=this;["_checkbox","_dealCheckbox","selectorinput","preveiwlimitdialog","circlelimitdialog"].forEach(function(e){t[e]&&(t[e].destroy(),t[e]=null)})}})});
define("crm-setting/customerrule/customerlimit/preveiwlimitdialog/preveiwlimitdialog",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/table/table","crm-widget/selector/selector"],function(t,e,i){t("crm-modules/common/util");var a=t("crm-widget/dialog/dialog"),n=t("crm-widget/table/table"),r=t("crm-widget/selector/selector"),l=a.extend({attrs:{width:920,showScroll:!1,title:$t("共享规则"),data:null},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"checkSubmitDatasHandle"},show:function(e){var i=this,a=l.superclass.show.call(this);this.setTitle(e.title),this.getContentWrap().addClass("crm-s-customerrule-dialog").html(['<div class="t-head">','<span class="item-tit">'+$t("搜索范围:")+"</span>",'<div class="item-con"></div>',"</div>",'<div class="t-wrap">','<div class="crm-loading"></div>',"</div>"].join("")),this.$(".t-wrap").height(Math.min($(window).height()-52-120-75,440)),FS.contacts.getUserGroups().then(function(t){return i.userGroups=t,i.initTable(e),i.resizedialog(),a})},initTable:function(t){var r=this;r.table=new n({$el:r.$(".t-wrap"),url:"/EM1HNCRM/API/v1/object/account_limit_rule/service/get_employee_account_limit_rule",requestType:"FHHApi",postData:{groupId:t.groupId,keyword:"",filterStatus:!0},columns:[{data:"employeeId",title:$t("员工名称"),render:function(t,e,i){return t=FS.contacts.getEmployeeById(t)&&FS.contacts.getEmployeeById(t).name||"--",'<span data-pos="top" class="employeename-ico '+(i.differentGroup?"tip-btn crm-ui-title":"")+'" data-title="'+$t("应用规则")+i.name+'"></span><span class="employeename-title" title='+t+">"+t+"</span>"}},{data:"departmentIds",title:$t("crm.所在部门"),render:function(t,e,i){return(t=t.map(function(t){return FS.contacts.getCircleById(t)&&FS.contacts.getCircleById(t).name||"--"})).join(",")}},{data:"userGroupIds",title:$t("所属用户组"),render:function(t){var a=[];return t.forEach(function(i){r.userGroups.forEach(function(t,e){t.id==i&&a.push(r.userGroups[e].name)})}),a.join(",")}},{data:"limitNumber",title:$t("客户保有量上限")},{data:"includeHighSeasCustomer",title:$t("公海客户是否占有保有量"),render:function(t,e,i){return t?$t("是"):$t("否")}},{data:"includeDealCustomer",title:$t("成交后是否占有保有量"),render:function(t,e,i){return t?$t("是"):$t("否")}}],formatData:function(t){return{data:t.employeeLimitRuleList,totalCount:t.totalNumber||0}}}),r.initRange()},initRange:function(){var e=this;e.range=new r({$wrap:e.$(".item-con"),width:160,size:1,single:!0,member:!0,label:$t("选择员工"),zIndex:+e.get("zIndex")+10,selectedAfterHideLabel:!0}),e.range.on("change",function(t){e.table.setParam({keyword:t.member[0]?t.member[0]+"":""},!0,!0)})},hide:function(){return this.table&&(this.table.destroy(),this.table=null),this.range&&this.range.destroy(),l.superclass.hide.call(this)},destroy:function(){return this.table&&(this.table.destroy(),this.table=null),this.range&&this.range.destroy(),l.superclass.destroy.call(this)}});i.exports=l});
define("crm-setting/customerrule/customerlimit/template/limit-circle-html", [ "crm-modules/common/util" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var util = require("crm-modules/common/util");
            __p += ' <div class="limit-circle-box"> <h3>' + ((__t = $t("crm.客户保有量编辑")) == null ? "" : __t) + '</h3> <div class="climits-box fn-clear"> <div class="fn-left box-left crm-scroll"> <div class="climits-title">' + ((__t = $t("员工")) == null ? "" : __t) + "</div> ";
            _.each(ConflictRuleList, function(item, index) {
                __p += " ";
                var EmployeeID = item.EmployeeID;
                __p += " ";
                var employeeObj = util.getEmployeeById(EmployeeID);
                __p += " ";
                var name = employeeObj ? employeeObj.name : "--";
                __p += ' <div class="climits-employee ';
                if (index === 0) {
                    __p += " active";
                }
                __p += '" data-employee="' + ((__t = EmployeeID) == null ? "" : __t) + '" title="' + ((__t = name) == null ? "" : __t) + '">' + ((__t = name) == null ? "" : __t) + "</div> ";
            });
            __p += ' </div> <div class="fn-left box-right crm-scroll"> <div class="climits-title">' + ((__t = $t("规则")) == null ? "" : __t) + "</div> ";
            var members = groups = usergroups = "";
            __p += " ";
            _.each(ConflictRuleList, function(item, index) {
                __p += ' <div class="climits-circle mn-radio-box ' + ((__t = " " + item.EmployeeID) == null ? "" : __t) + "circle ";
                if (index !== 0) {
                    __p += " b-g-hide";
                }
                __p += '"> ';
                _.each(item.EmployeeConflictRuleList, function(ite, idx) {
                    members = groups = usergroups = "";
                    switch (ite.DataType) {
                      case 1:
                        members = ite.DataID;
                        break;

                      case 2:
                        groups = ite.DataID;
                        break;

                      case 3:
                        usergroups = ite.DataID;
                        break;
                    }
                    __p += ' <div class="circle-radio"> <span class="mn-radio-item ';
                    if (ite.IsSelected) {
                        __p += " mn-selected";
                    }
                    __p += '" data-index=' + ((__t = idx) == null ? "" : __t) + "></span> <span>" + ((__t = $t("规则")) == null ? "" : __t) + "" + ((__t = ite.RuleName || "--") == null ? "" : __t) + "</span> </div> ";
                    var customer_t = $t("crm.客户保有量", {
                        LimitNum: ite.LimitNum,
                        IsClaimLimitIncludeHighSeasCustomers: ite.IsClaimLimitIncludeHighSeasCustomers ? "" : $t("不"),
                        IsClaimLimitIncludeDealedCustomers: ite.IsClaimLimitIncludeDealedCustomers ? "" : $t("不")
                    });
                    __p += ' <p class="rule-item">' + ((__t = customer_t) == null ? "" : __t) + "</p> ";
                });
                __p += " </div> ";
            });
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerlimit/template/limit-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("1客户保有量上限是员工可以保有的最大客户数。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2不在规则中的部门或员工无保有量限制。")) == null ? "" : __t) + '</li> </ol> <div class="share-group-box" data-title="rulelimit"></div> </div> <div class="rule-box"> <h3 class="rule-tit">' + ((__t = $t("设置客户保有量上限")) == null ? "" : __t) + '</h3> <div class="rule-subtit b-g-clear"> <span class="item-id" title="' + ((__t = $t("规则编号")) == null ? "" : __t) + '" style="text-indent: 0;">' + ((__t = $t("规则编号")) == null ? "" : __t) + '</span> <span title="' + ((__t = $t("设置范围")) == null ? "" : __t) + '">' + ((__t = $t("设置范围")) == null ? "" : __t) + '</span> <span class="ml" title="' + ((__t = $t("客户保有量上限")) == null ? "" : __t) + '">' + ((__t = $t("客户保有量上限")) == null ? "" : __t) + '</span> <span class="ml-c" title="' + ((__t = $t("公海客户是否占有保有量")) == null ? "" : __t) + '">' + ((__t = $t("公海客户是否占有保有量")) == null ? "" : __t) + '</span> <span class="ml-c" title="' + ((__t = $t("成交后是否占有保有量")) == null ? "" : __t) + '">' + ((__t = $t("成交后是否占有保有量")) == null ? "" : __t) + '</span> <span class="ml-con" title="' + ((__t = $t("操作")) == null ? "" : __t) + '">' + ((__t = $t("操作")) == null ? "" : __t) + '</span> </div> <ul class="rule-container"></ul> <span class="add-btn add-limit">+' + ((__t = $t("添加规则")) == null ? "" : __t) + "</span> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerlimit/template/limit-rule-container-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(limitList, function(item) {
                __p += " ";
                var CustomerLimitDataRule = item.dataRule;
                function getIds(value) {
                    return _.map(value, function(ite) {
                        return ite && ite.id || "--";
                    }).join(",");
                }
                function getNameList(value) {
                    return _.map(value, function(ite) {
                        return ite && ite.name || "--";
                    });
                }
                __p += ' <li class="rule-item" data-groupid="' + ((__t = item.groupId) == null ? "" : __t) + '" data-groups="' + ((__t = getIds(CustomerLimitDataRule.departmentList)) == null ? "" : __t) + '" data-members="' + ((__t = getIds(CustomerLimitDataRule.employeeList)) == null ? "" : __t) + '" data-num="' + ((__t = item.limitNumber) == null ? "" : __t) + '" data-usergroups="' + ((__t = getIds(CustomerLimitDataRule.userGroupList)) == null ? "" : __t) + '"> <div class="item-id">' + ((__t = item.name) == null ? "" : __t) + '</div> <div class="item-tit"> ' + ((__t = _.filter(getNameList(CustomerLimitDataRule.employeeList).concat(getNameList(CustomerLimitDataRule.departmentList), getNameList(CustomerLimitDataRule.userGroupList)), function(value) {
                    return value && value.name !== "";
                }).join(",") || "--") == null ? "" : __t) + " </div> <div> " + ((__t = JSON.stringify(limitList.userGroupList)) == null ? "" : __t) + ' </div> <div class="ipt-box"><span class="num">' + ((__t = item.limitNumber) == null ? "" : __t) + '</span><span class="unit">' + ((__t = $t("个")) == null ? "" : __t) + '</span></div> <div class="checkbox-sec highseas-claim" data-name="IsClaimLimitIncludeHighSeasCustomers" data-value="' + ((__t = item.includeHighSeasCustomer) == null ? "" : __t) + '">' + ((__t = item.includeHighSeasCustomer ? $t("是") : $t("否")) == null ? "" : __t) + '</div> <div class="checkbox-sec deal-claim" data-name="IsClaimLimitIncludeDealedCustomers" data-value="' + ((__t = item.includeDealCustomer) == null ? "" : __t) + '">' + ((__t = item.includeDealCustomer ? $t("是") : $t("否")) == null ? "" : __t) + '</div> <div class="item-operate"> <span title=\'' + ((__t = $t("编辑")) == null ? "" : __t) + '\' class="edite-limit">' + ((__t = $t("编辑")) == null ? "" : __t) + "</span> <span title='" + ((__t = $t("预览")) == null ? "" : __t) + '\' class="preview-limit">' + ((__t = $t("预览")) == null ? "" : __t) + "</span> <span title='" + ((__t = $t("删除")) == null ? "" : __t) + '\' class="del-limit">' + ((__t = $t("删除")) == null ? "" : __t) + "</span> <span title='" + ((__t = $t("保存")) == null ? "" : __t) + '\' class="save-limit">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> <span title='" + ((__t = $t("取 消")) == null ? "" : __t) + '\'class="cancel-limit">' + ((__t = $t("取 消")) == null ? "" : __t) + "</span> </div> </li> ";
            });
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerlimit/template/limit-rule-item-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<li class="rule-item" data-groupid="" data-groups="" data-members="" data-usergroups=""> <div class="item-id"></div> <div class="item-tit"></div> <div class="ipt-box"><span class="num"></span><span class="unit">' + ((__t = $t("个")) == null ? "" : __t) + '</span></div> <div class="checkbox-sec highseas-claim" data-name="IsClaimLimitIncludeHighSeasCustomers" data-value="false">' + ((__t = $t("否")) == null ? "" : __t) + '</div> <div class="checkbox-sec deal-claim" data-name="IsClaimLimitIncludeDealedCustomers" data-value="false">' + ((__t = $t("否")) == null ? "" : __t) + '}}</div> <div class="item-operate"> <span class="edite-limit">' + ((__t = $t("编辑")) == null ? "" : __t) + '</span> <span class="save-limit">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="preview-limit">' + ((__t = $t("预览")) == null ? "" : __t) + '</span> <span class="del-limit">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span class="cancel-limit">' + ((__t = $t("取 消")) == null ? "" : __t) + "</span> </div> </li>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/customerrule",["crm-modules/common/util","./template/index-html"],function(n,e,a){function o(e){this.wrapper=e,this.id=c.uuid(),this.init()}var c=n("crm-modules/common/util"),i=n("./template/index-html"),t=(o.prototype={init:function(){var e=this,a=c.getShareGroupQxSessionId();a?e.create(a):FS.tpl.event.one("shareGroupAdd",e.create,e)},create:function(t){t&&(this.wrapper.each(function(e,a){$('<div class="share-group"><em class="crm-ico-bee"></em>'+$t("在线客服")+"</div>").appendTo($(a)).on("click",function(e){return FS.tpl.event.trigger("qxOpenChat",t,"session"),e&&e.stopPropagation(),!1})}),this.rendered=!0)},destroy:function(){var e=this;e.rendered||FS.tpl.event.off("shareGroupAdd",e.create,e)}},Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},events:{"click .crm-tab a":"onTab"},onTab:function(e){var a=$(e.target);e.preventDefault();e=(e=a.attr("href")).split("/=/");this.switchPage([e[1]])},switchPage:function(e){this.renderPage(e)},render:function(t){var n=this;CRM.util.getCountryAreaOptions().then(function(){n.getAllConfig(function(a){a=_.extend(a,{crmVersion:CRM.control.crmVersion,isWeChat:CRM.control.isWeChat,isYunZhiJia:CRM.control.isYunZhiJia,order:!0,opp:!1,contracts:!1,hierarchyRights:n.hierarchyRights,contractRelationRights:n.contractRelationRights,contactPersonnelRelationRights:n.contactPersonnelRelationRights}),CRM.api.get_licenses({key:"accounts_leads_limit_app",objectApiName:"AccountObj",cb:function(e){a.is_new_rownership=!!e.accounts_leads_limit_app,n.$el.html(i(a)),n.data=a;e=c.getTplQueryParams(),e=_.values(e);t=t||[e[1]],n.renderPage(t),n.shareGroup=new o(n.$(".share-group-box")),a.is_new_rownership}})})})},renderPage:function(e){var a=this,t=a.$(".crm-tab .item");switch(t.removeClass("cur"),(e?t.filter("."+e[0]):t.eq(0)).addClass("cur"),a.page1&&a.page1.hide(),a.page2&&a.page2.hide(),a.page3&&a.page3.hide(),a.page4&&a.page4.hide(),a.page5&&a.page5.hide(),a.page6&&a.page6.hide(),a.page7&&a.page7.hide(),a.page8&&a.page8.hide(),a.page9&&a.page9.hide(),a.page10&&a.page10.hide(),a.page11&&a.page11.hide(),a.page12&&a.page12.hide(),e&&e[0]){case"page2":a.page2?a.page2.show():n.async("./nonhighseasrule/nonhighseasrule",function(e){a.page2=new e(_.extend({el:a.$(".recover-box")},a.data)),a.page2.show()});break;case"page3":a.page3?a.page3.show():n.async("./customercheck/customercheck",function(e){a.page3=new e(_.extend({el:a.$(".check-box")},a.data)),a.page3.show()});break;case"page4":a.page4?a.page4.show():n.async("./customerlimit/customerlimit",function(e){a.page4=new e({el:a.$(".limit-box")}),a.page4.show()});break;case"page5":a.page5?a.page5.show():n.async("./customerrulesetting/customerrulesetting",function(e){a.page5=new e(_.extend({el:a.$(".basic-box")},a.data)),a.page5.show()});break;case"page6":a.page6?a.page6.show():n.async("./customerbehavior/customerbehavior",function(e){a.page6=new e(_.extend({el:a.$(".behavior-box")},a.data)),a.page6.show()});break;case"page7":a.page7?a.page7.show():n.async("./makeDealBehavior/makeDealBehavior",function(e){a.page7=new e(_.extend({el:a.$(".makeDeal-box")},a.data)),a.page7.show()});break;case"page8":a.page8?a.page8.show():n.async("crm-setting/ownership/ownership",function(e){a.page8=new e(_.extend({wrapper:a.$(".ownership-box")},a.data,{apiname:"AccountObj"})),a.page8.show()});break;case"page9":a.page9?a.page9.show():n.async("crm-setting/hierarchicalconfig/hierarchicalconfig",function(e){a.page9=new e(_.extend({el:a.$(".hierarchy-box")},a.data,{apiname:"AccountObj"})),a.page9.show()});break;case"page10":a.page10?a.page10.show():n.async("./contactrelations/contactrelations",function(e){a.page10=new e(_.extend({wrapper:a.$(".contactrelations-box")},a.data,{apiname:"AccountObj"})),a.page10.show()});break;case"page12":a.page12?a.page12.show():n.async("./contactpersonnelrelation/contactpersonnelrelation",function(e){a.page12=new e(_.extend({el:a.$(".contactpersonnelrelation-box")},a.data)),a.page12.show()});break;case"page11":a.page11?a.page11.show():n.async("./mapmodesetting/mapmodesetting",function(e){a.page11=new e(_.extend({el:a.$(".mapmodesetting-box")},a.data)),a.page11.show()});break;default:a.page1?a.page1.show():n.async("./highseasmanage/highseasmanage",function(e){a.page1=new e(_.extend({el:a.$(".highseas-box")},a.data)),a.page1.show()})}},getAllConfig:function(o){var i=this;$.when(c.getConfigValues(!0),i.getHierarchyRights(),i.getContractRelationRights(),i.getContactPersonnelRelationRights()).then(function(e,a,t,n){i.hierarchyRights=a,i.contractRelationRights=t,i.contactPersonnelRelationRights=n,o&&o(i.formatData(e[1]))})},getHierarchyRights:function(){var a=$.Deferred();return c.FHHApi({url:"/EM1HNCRM/API/v1/object/account_path/service/get_has_account_hierarchy_license",success:function(e){0===e.Result.StatusCode?a.resolve(e.Value.hasAccountHierarchyLicense):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1}),a.promise()},getContractRelationRights:function(){var a=$.Deferred();return c.FHHApi({url:"/EM1HNCRM/API/v1/object/contact_atlas/service/get_has_contact_relation_license",success:function(e){0===e.Result.StatusCode?a.resolve(e.Value.hasAccountHierarchyLicense):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1}),a.promise()},getContactPersonnelRelationRights:function(){var a=$.Deferred();return c.FHHApi({url:"/EM1HNCRM/API/v1/object/contact_relationship_service/service/get_contact_member_relationship_license",success:function(e){0===e.Result.StatusCode?a.resolve(e.Value.hasLicense):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1}),a.promise()},getKeyValue:function(e,a){return(_.findWhere(e,{key:a})||_.findWhere([{key:"1",value:"1"},{key:"2",value:"1"},{key:"3",value:"0"},{key:"4",value:"1"},{key:"5",value:"1,2,6,5,3,4"},{key:"6",value:"1"},{key:"11",value:"0"},{key:"check_account_name_duplicated",value:"0"},{key:"is_open_account_addr_config",value:"0"},{key:"add_account_meanwhile_add_addr",value:"1"},{key:"account_and_addr_sync_upd_location",value:"1"}],{key:a})).value},formatData:function(e){var a=this,t={},n=e.values||[];return t.notImport="0"==a.getKeyValue(n,"1"),t.notAdd="0"==a.getKeyValue(n,"2"),t.changeCustomer="1"==a.getKeyValue(n,"11"),t.isCheckOpen="1"==a.getKeyValue(n,"3"),t.isBlurSearch="1"==a.getKeyValue(n,"4"),t.repeatShowItems=a.getKeyValue(n,"5").split(","),t.isRepeatOpen="1"==a.getKeyValue(n,"6"),t.repetitionIsHint="1"==a.getKeyValue(n,"check_account_name_duplicated"),t.checker=c.getEmployeesByIds(e.EmployeeIDs)||[],t.isOpenCPRelation=a.getKeyValue(n,"contact_member_relationship_func_setting"),t.isOpenAddress="1"==a.getKeyValue(n,"is_open_account_addr_config"),t.isSyncAddress="1"==a.getKeyValue(n,"add_account_meanwhile_add_addr"),t.isSyncLocation="1"==a.getKeyValue(n,"account_and_addr_sync_upd_location"),t.UnHighSeasCusRecyclingRuleInfoList=e.UnHighSeasCusRecyclingRuleInfoList,t.roleList=a.formatRole(e.RoleFunctionGroupInfoList||[]),t},formatRole:function(e){var a={FunctionGroup1:0,FunctionGroup2:0,FunctionGroup3:0,FunctionGroup4:0,FunctionGroup5:0,FunctionGroup6:0,FunctionGroup7:0,FunctionGroup8:0,FunctionGroup9:0};return _.each(e,function(e){a["FunctionGroup"+e.FunctionGroup]=e.Enabled}),a},destroy:function(){var t=this;_.each(["shareGroup","page1","page2","page3","page4","page5","page6","page7","page8","page9","page10","page11","page12"],function(e,a){t[e]&&(t[e].destroy(),t[e]=null)})}}));a.exports=t});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return s};var u,s={},t=Object.prototype,c=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",i=e.toStringTag||"@@toStringTag";function d(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{d({},"")}catch(u){d=function(t,e,n){return t[e]=n}}function a(t,e,n,r){var i,o,s,a,e=e&&e.prototype instanceof h?e:h,e=Object.create(e.prototype);return d(e,"_invoke",(i=t,o=n,s=new _(r||[]),a=1,function(t,e){if(3===a)throw Error("Generator is already running");if(4===a){if("throw"===t)throw e;return{value:u,done:!0}}for(s.method=t,s.arg=e;;){var n=s.delegate;if(n){n=function t(e,n){var r=n.method,i=e.i[r];if(i===u)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=u,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;r=l(i,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,f;i=r.arg;return i?i.done?(n[e.r]=i.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=u),n.delegate=null,f):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}(n,s);if(n){if(n===f)continue;return n}}if("next"===s.method)s.sent=s._sent=s.arg;else if("throw"===s.method){if(1===a)throw a=4,s.arg;s.dispatchException(s.arg)}else"return"===s.method&&s.abrupt("return",s.arg);a=3;n=l(i,o,s);if("normal"===n.type){if(a=s.done?4:2,n.arg===f)continue;return{value:n.arg,done:s.done}}"throw"===n.type&&(a=4,s.method="throw",s.arg=n.arg)}}),!0),e}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}s.wrap=a;var f={};function h(){}function o(){}function p(){}var e={},v=(d(e,r,function(){return this}),Object.getPrototypeOf),v=v&&v(v(x([]))),y=(v&&v!==t&&c.call(v,r)&&(e=v),p.prototype=h.prototype=Object.create(e));function m(t){["next","throw","return"].forEach(function(e){d(t,e,function(t){return this._invoke(e,t)})})}function g(s,a){var e;d(this,"_invoke",function(n,r){function t(){return new a(function(t,e){!function e(t,n,r,i){var o,t=l(s[t],s,n);if("throw"!==t.type)return(n=(o=t.arg).value)&&"object"==_typeof(n)&&c.call(n,"__await")?a.resolve(n.__await).then(function(t){e("next",t,r,i)},function(t){e("throw",t,r,i)}):a.resolve(n).then(function(t){o.value=t,r(o)},function(t){return e("throw",t,r,i)});i(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=u,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function x(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(c.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=u,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return d(y,"constructor",o.prototype=p),d(p,"constructor",o),o.displayName=d(p,i,"GeneratorFunction"),s.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},s.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,d(t,i,"GeneratorFunction")),t.prototype=Object.create(y),t},s.awrap=function(t){return{__await:t}},m(g.prototype),d(g.prototype,n,function(){return this}),s.AsyncIterator=g,s.async=function(t,e,n,r,i){void 0===i&&(i=Promise);var o=new g(a(t,e,n,r),i);return s.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},m(y),d(y,i,"Generator"),d(y,r,function(){return this}),d(y,"toString",function(){return"[object Generator]"}),s.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},s.values=x,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&c.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=u)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){o.type="throw",o.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var i=this.tryEntries[r],o=i[4],s=this.prev,a=i[1],c=i[2];if(-1===i[0])return t("end"),!1;if(!a&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=s){if(s<a)return this.method="next",this.arg=u,t(a),!0;if(s<c)return t(c),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}var o=(i=i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]?null:i)?i[4]:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i[2],f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),b(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,i=this.tryEntries[e];if(i[0]===t)return"throw"===(n=i[4]).type&&(r=n.arg,b(i)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:x(t),r:e,n:n},"next"===this.method&&(this.arg=u),f}},s}function asyncGeneratorStep(t,e,n,r,i,o,s){try{var a=t[o](s),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(a){return function(){var t=this,s=arguments;return new Promise(function(e,n){var r=a.apply(t,s);function i(t){asyncGeneratorStep(r,e,n,i,o,"next",t)}function o(t){asyncGeneratorStep(r,e,n,i,o,"throw",t)}i(void 0)})}}define("crm-setting/customerrule/customerrulesetting/customerrulesetting",["crm-modules/common/util"],function(t,e,n){var r=t("crm-modules/common/util");n.exports=Backbone.View.extend({initialize:function(t){var e=t.changeCustomer?"mn-selected":"",n=t.repetitionIsHint?"mn-selected":"";this.el.innerHTML='<div class="crm-account-rule-setting crm-account-name-setting">\n            <div class="title"><h1>'.concat($t("sfa.customer.name.setting.title"),'</h1></div>\n            <div class="setting-content">\n                <div class="crm-intro">\n                    <ol>\n                        <li>').concat($t("开启多组织后此配置失效"),'</li>\n                    </ol>\n                </div>\n                <div class="setting-setrule">\n                    <span class="set-basic mn-checkbox-item ').concat(e,'" data-key="11"></span><span class="check-lb">').concat($t("允许修改客户名称"),'</span>\n                </div>\n                <div class="setting-setrule">\n                    <span class="set-basic-two mn-checkbox-item ').concat(n,'" data-key=\'check_account_name_duplicated\'></span><span class="check-lb">').concat($t("新建编辑客户页面，当客户名称与系统中已有客户名称重复时进行提示。"),'</span>\n                </div>\n            </div>\n            </div>\n            <div class="crm-account-rule-setting crm-account-address-setting"></div>\n            '),CRM.util.isGrayScale("SFA_ACCOUNT_RULE_OPEN_ADDR_CONFIG")&&this.renderAccountAddressSetting(t)},events:{"click .set-basic":"setBasicHandle","click .set-basic-two":"setRepetitionIsHint"},setBasicHandle:function(t){var t=$(t.target),e=t.attr("data-key"),n=t.hasClass("mn-selected")?"1":"0";this.setConfig({key:e,value:"11"==e?"0"==n?"1":"0":n},t)},setRepetitionIsHint:function(t){var t=$(t.target),e=t.attr("data-key");value=t.hasClass("mn-selected")?"1":"0",this.setConfig({key:e,value:"check_account_name_duplicated"==e?"0"==value?"1":"0":value},t)},setConfig:function(t,e){r.setConfigValue(t).then(function(){r.remind(1,$t("设置成功"))},function(){r.remind(3,$t("设置失败")),e.toggleClass("mn-selected")})},renderAccountAddressSetting:function(t){this.widget=FxUI.create({wrapper:this.$(".crm-account-address-setting")[0],template:'\n                    <div class="address-setting-box">\n                        <h2 class="title"><h1>{{$t("sfa.customer.rule.address.setting")}}</h1></h2>\n                        <div class="setting-content">\n                            <div class="backstage-switch">\n                                <div class="crm-module-title">{{$t("sfa.customer.rule.address.setting.open.title")}}</div>\n                                <div class="on-off">\n                                    <label>\n                                        {{$t(\'crm.setting.tradeconfigure.warn_cannot_closed\', null, \'一旦启用，将无法停用\')}}\n                                    </label>\n                                    <fx-switch\n                                        :disabled="isDisabled"\n                                        v-model="isOpenAddress" \n                                        size="small" \n\t\t\t\t\t\t\t\t\t\t:before-change="openAutoAddress"\n                                    />\n                                </div>\n                            </div>\n                            <div class="open-address-tip tip-box">\n                                    <div>{{$t("sfa.customer.rule.address.setting.tip1")}}</div>\n                                    <div>{{$t("sfa.customer.rule.address.setting.tip2")}}</div>\n                                    <div>{{$t("sfa.customer.rule.address.setting.tip2.1")}}</div>\n                                    <div>{{$t("sfa.customer.rule.address.setting.tip2.2")}}</div>\n                            </div>\n                            <div class="add-switch-box" v-show="showStatus">\n                                <div class="setting-add-sync">\n                                    <div class="switch-content-box">\n                                        <fx-checkbox @change="onSyncAddress" v-model="isSyncAddress">'.concat($t("sfa.customer.rule.address.setting.sync.address"),'</fx-checkbox>\n                                    </div>\n                                    <div class="add-sync-tip tip-box">\n                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1")}}</div>\n                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1.1")}}</div>\n                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1.2")}}</div>\n                                    </div>\n                                </div>\n                                <div class="setting-position-sync">\n                                    <div class="switch-content-box">\n                                        <fx-checkbox @change="onSyncLocation" v-model="isSyncLocation"  :disabled="isLocationDisabled">').concat($t("sfa.customer.rule.address.setting.sync.location"),'</fx-checkbox>\n                                    </div>\n                                    <div class="position-sync-tip tip-box">\n                                        <div>{{$t("sfa.customer.rule.address.setting.sync.location.tip")}}</div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>'),data:function(){return{isOpenAddress:t.isOpenAddress,isSyncAddress:t.isSyncAddress,isSyncLocation:t.isSyncLocation,isLocationDisabled:t.isSyncAddress}},computed:{isDisabled:function(){return this.isOpenAddress},showStatus:function(){return this.isOpenAddress}},methods:{openAutoAddress:function(){var e=this;this.$confirm($t("sfa.customer.rule.address.settiing.open.confirm"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(_asyncToGenerator(_regeneratorRuntime().mark(function t(){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:CRM.util.setConfigValue({key:"is_open_account_addr_config",value:"1"}).then(function(t){e.isOpenAddress=!0});case 1:case"end":return t.stop()}},t)}))).catch(function(){e.isOpenAddress=!1})},onSyncAddress:function(e){var n=this;e?CRM.util.setConfigValues([{key:"add_account_meanwhile_add_addr",value:"1"},{key:"account_and_addr_sync_upd_location",value:"1"}]).then(function(){n.isSyncAddress=e,n.isSyncLocation=!0,n.isLocationDisabled=!0}):this.$confirm($t("sfa.customer.rule.address.setting.sync.address.close.tip"),$t("提示"),{confirmButtonText:$t("继续"),cancelButtonText:$t("取消"),type:"warning"}).then(_asyncToGenerator(_regeneratorRuntime().mark(function t(){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:CRM.util.setConfigValue({key:"add_account_meanwhile_add_addr",value:"0"}).then(function(){n.isSyncAddress=e,n.isLocationDisabled=!1});case 1:case"end":return t.stop()}},t)}))).catch(function(){n.isSyncAddress=!0,n.isLocationDisabled=!0,n.isLocationDisabled=!0})},onSyncLocation:function(t){var e=this;CRM.util.setConfigValue({key:"account_and_addr_sync_upd_location",value:t?"1":"0"}).then(function(){e.isSyncLocation=t})},destroy:function(){this.$destroy()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){}})});
define("crm-setting/customerrule/hierarchy/hierarchy",["./tpl-html"],function(e,t,i){var a=e("./tpl-html");i.exports=Backbone.View.extend({initialize:function(e){this.apiname="AccountObj",this.$el.html(a(e)),this.fetch()},events:{"click .j-save":"onSave"},fetch:function(){var i=this;Promise.all([CRM.util.getDescribeLayout({include_detail_describe:!1,include_layout:!1,apiname:i.apiname,layout_type:"add",recordType_apiName:"default__c"}),CRM.util.getConfigValue("account_tree_fields")]).then(function(e){i.fieldsObject=e[0].objectDescribe.fields;var t=i.parseData(i.parseData1(i.fieldsObject)),e=e[1].split(","),e=_.filter(e,function(e){return i.fieldsObject[e]&&i.fieldsObject[e].is_active});i.initTransfer(t,e)})},parseData:function(e){var t=[];return _.each(e,function(e){t.push({label:e.label,key:e.api_name,disabled:"name"==e.api_name})}),t},parseData1:function(e){var t=[],i=/^UD.+__c$/;return _.each(e,function(e){return!(!e.is_active||"group"==e.type||!e.is_index&&"formula"==e.type||-1!=["is_overtime","lock_rule","extend_obj_data_id","life_status_before_invalid","package","create_time","version","reated_by","relevant_team","data_own_department","_id","is_duplicated","tenant_id","lock_user","is_deleted","object_describe_api_name","last_modified_by","order_by","last_modified_time","leads_pool_id","object_describe_id","account_path","industry_ext"].indexOf(e.api_name))&&((e=i.test(e.api_name)?_.extend(e,{config:{display:1}},e):e).config||(e.config={display:1}),void(e.config&&0!==e.config.display&&t.push(e)))}),t},initTransfer:function(e,t){this.transfer=FxUI.create({wrapper:this.$(".setting-hierarchy-transfer")[0],template:'<fx-transfer :draggable="true" is-item-break :titles="titles" target-order="push" :filterable="true" v-model="value" :data="data"></fx-transfer>',data:function(){return{data:e,value:t,titles:[$t("全选"),$t("全选")]}}})},onSave:function(){var e=this.transfer.value.join(",");CRM.util.setConfigValue({key:"account_tree_fields",value:e}).then(function(e){CRM.util.remind(1,$t("设置成功"))},function(e){CRM.util.remind(3,e||$t("设置失败"))})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){}})});
define("crm-setting/customerrule/hierarchy/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-hierarchy"> <div class="setting-hierarchy-item"> <div class="setting-hierarchy-item-hd">' + ((__t = $t("客户层级列表上所展示的字段")) == null ? "" : __t) + '</div> <div class="setting-hierarchy-transfer-hd"> <div class="transfer-title">' + ((__t = $t("全部字段")) == null ? "" : __t) + '</div> <div class="transfer-title">' + ((__t = $t("显示字段")) == null ? "" : __t) + '</div> </div> <div class="setting-hierarchy-transfer"></div> </div> <div class="setting-hierarchy-btns"> <div class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/highseasmanage/customerrule/customerrule",["crm-modules/common/slide/slide","./template/tpl-html","crm-modules/components/parserule/parserule","crm-modules/common/highseas/highseas","../format"],function(e,t,a){var r=CRM.util,i=e("crm-modules/common/slide/slide"),s=e("./template/tpl-html"),o=e("crm-modules/components/parserule/parserule"),n=e("crm-modules/common/highseas/highseas"),l=n.rulesFieldStatic,d=n.rulesConfig,n=e("../format"),n=i.extend(_.extend({options:{className:"crm-d-customerrule fs__tg__dialog"},hsID:"",events:{"click .j-del":"delHandle","click .j-transfer":"transferHandle","click .j-edite":"editeHandle","click .j-clone":"cloneHandle"},show:function(e,t){this.hsID=e._id,this.dt=t,this.listData=e,this.setUsersInfo(),this.getDetail(this.hsID)},getDetail:function(e){var t=this;this.highSeasID=e,r.getHighSeasByID(e,!0).then(function(e){e.pool_permission_templates=t.filterHideField(e.pool_permission_templates),e=t.formatObj(e,t.listData),t.data=e,t.renderDetail(e),i.prototype.show.call(t)})},getRuleList:function(e,t){var a=this;e?(a.xhr&&a.xhr.abort(),a.xhr=r.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/get_recycling_rule_list",data:{api_name:"HighSeasObj",data_id:e},success:function(e){0==e.Result.StatusCode?(a.ruleList=e.Value.recyclingRuleList,0<a.ruleList.length&&(a.ruleList=a.ruleList.sort(function(e,t){return e.priority-t.priority})),t&&t()):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})):(CRM.util.sendLog("highseas","manage",{operationId:"view",eventType:"cl",pageData:{highseasid:e,highseaslist:a.dt.dt.curData.data,param:a.dt.opts.postData}}),a.ruleList=[],t&&t())},getFieldList:function(a){var i=this;r.FHHApi({url:"/EM1HNCRM/API/v1/object/AccountObj/controller/DescribeLayout",data:{include_detail_describe:!1,include_layout:!1,apiname:"AccountObj",layout_type:"add",recordType_apiName:"record_sKbe4__c"},success:function(e){var t;0==e.Result.StatusCode?(t=i.parseData(e.Value.objectDescribe.fields),i.fieldlist=i.filterField(t),a&&a()):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},setUsersInfo:function(){CRM.util.getUserGroups2(),CRM.util.getUserRoles2()},filterField:function(e){return _.filter(e||[],function(e){var t="formula"==e.type&&_.contains(["date_time","date","time"],e.return_type)||"formula"==e.type&&!e.is_index;"formula"==e.type&&(e.type=e.return_type||e.type);return!t&&!["extend_obj_data_id","_id","package","tenant_id","object_describe_id","object_describe_api_name","version","is_deleted","high_seas_id","high_seas_name"].includes(e.api_name)})},parseData:function(e){var t,a=[];for(t in e)e[t]&&e[t]instanceof Object&&a.push(e[t]);return a},addStaticField:function(e){return e=e.concat(l)},renderDetail:function(e){var t=this;t.getFieldList(function(){t.renderDetailHandle(e)})},renderDetailHandle:function(e){var t=this;e.moment=FS.moment,t.formatRuleList=[],_.each(e.recycling_rule_list,function(e){e=CRM.util.parseRecycleRule(e,t.fieldlist,"AccountObj");t.formatRuleList.push(e)}),$(".slide-con",this.$el).html(s(t.formatPostData(e)))},getRulesArea:function(e){var t,a=[];return e=e.recycling_rule_list,t=t||"wheres",_.each(e,function(e){e=e[t];e=CRM.util.getRuleAreaData(e),a=_.union(a,e)}),a},formatFilterList:function(e){var r=this,e=JSON.parse(e.wheres)||{},n=[];return e.filters.forEach(function(e){var t,a,i,s=o.getField(r.fieldlist,e.field_name);s&&("out_owner"===s.api_name&&(s.type="exemployee"),t=r.getCompareByValue(e.operator),i=19===(a=[17].includes(t)?19:t)?e.field_values.join("|"):e.field_values,n.push({FieldName:s.label,FieldValue:CRM.util.isEmptyValue(i)?"":o.getValue(s,i,a),Compare:o.getCompare(e.field_name,t,s),hideFieldValue:9==a||10==a}))}),n},getCompareByValue:function(e){e=_.findWhere(d.getCompareConfig(),{value1:e});return e?e.value:"--"},formatPostData:function(i){var e=i.data.recycling_rule_type;2==e?i.data.deal_days:3==e&&i.data.follow_up_days;return i.data.isOpenPRM=CRM.get("isOpenPRM"),i.RecyclingCondition=this.formatRuleList,i.data.RemindRuleWords=[],i.txts={display_name:$t("crm.公海"),object_display_name:$t("crm.客户")},_.each(i.remind_rule_list,function(e,t){var a=2==e.rule_type?e.deal_days:e.follow_up_days,e=[$t("不收回"),$t("未成交"),$t("未跟进")][e.rule_type-1];i.data.RemindRuleWords.push(a+$t("天")+e+$t("提醒负责人")+(i.data.isOpenPRM?$t("和外部负责人"):""))}),i.data.RemindRuleWords=i.data.RemindRuleWords.join($t("；"))||$t("未设置"),i.data.Administrator=this.getDisplayNames(i.admin_pool_permissions),i.data.EmployeeName=this.getDisplayNames(i.member_pool_permissions),i.data.accounts_count=this.data.data.accounts_count||0,i.data.last_modified_time=i.data.last_modified_time||(new Date).getTime(),i.data.CollaboratorPoolPermissions=this.getDisplayNames(i.collaborator_pool_permissions),this.data=_.extend(this.data,i),this.data},getDisplayNames:function(e){var t,a={},i="",s="";for(t in _.each(e,function(e){a[e.type]||(a[e.type]=[]),a[e.type].push(e.data_id)}),a){switch(t){case"1":i=r.getNameByIds(a[t],"p",!0);break;case"2":i=r.getNameByIds(a[t],"g",!0);break;case"3":i=_.pluck(r.getExContactByIds(a[t],"en"),"name").join($t("，"));break;case"4":i=_.pluck(r.getExContactByIds(a[t]),"name").join($t("，"));break;case"5":i=_.pluck(r.getUserGroupByIds(a[t]),"name").join($t("，"));break;case"6":i=_.pluck(r.getRoleByIds(a[t]),"name").join($t("，"))}i&&(s+=s?$t("，")+i:i)}return s},editeHandle:function(){var a=this;e.async("crm-modules/components/customerrule/customerrule",function(e){a.highseas||(a.highseas=new e,a.highseas.on("refresh",function(e,t){a.getDetail(a.hsID),a.trigger("refresh")})),a.highseas.edit(a.hsID)})},cloneHandle:function(){var a=this;e.async("crm-modules/components/customerrule/customerrule",function(e){a.highseas||(a.highseas=new e,a.highseas.on("refresh",function(e,t){a.getDetail(a.hsID),a.trigger("refresh")})),a.highseas.clone(a.hsID)})},createOpration:function(t){var a=this;e.async("crm-modules/action/customer/customer",function(e){a.opr||(a.opr=new e,a.opr.on("refresh",function(e){"delhighsea"==e&&a.hide(),a.trigger("refresh",e)})),t&&t(a.opr)})},transferHandle:function(){var e=this;CRM.api.transfer4mgr({hsID:e.hsID,success:function(){e.hide(),e.data.data.accounts_count=0,e.data.data.last_modified_time=(new Date).getTime(),e.renderDetail(e.data),e.trigger("refresh")}})},delHandle:function(){var a=this;e.async("../delete/delete.js",function(t){CRM.util.getHighSeasList(!0).then(function(e){e=_.filter(e,function(e){return e._id!=a.data.data._id});t({data:a.data.data,options:e,count:a.data.data.accounts_count,success:function(){a.hide(),a.trigger("refresh","delhighsea")}})})})},destroy:function(){this.edite&&this.edite.destroy(),this.transfer&&this.transfer.destroy(),this.highseas&&this.highseas.destroy(),this.del&&this.del.destroy(),i.prototype.destroy.apply(this,arguments)}},n));a.exports=n});
define("crm-setting/customerrule/highseasmanage/customerrule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-d-detail"> <div class="detail-tit"> <h2>' + __e(data.name) + '</h2> <div class="detail-btns"> <span class="crm-ico-clone j-clone">' + ((__t = $t("复制")) == null ? "" : __t) + "</span> ";
            if (data.accounts_count > 0) {
                __p += ' <span class="crm-ico-shift j-transfer">' + ((__t = $t("转移")) == null ? "" : __t) + "</span> ";
            }
            __p += ' <span class="crm-ico-del j-del">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span class="crm-ico-edite j-edite">' + ((__t = $t("编辑")) == null ? "" : __t) + '</span> </div> <div class="detail-status"> ';
            var n = $t("客户总数{{CustomerCounts}} 个", {
                CustomerCounts: data.accounts_count
            });
            __p += ' <span style="max-width: 200px;" class="maxwidth">' + ((__t = n) == null ? "" : __t) + '</span> </div> </div> <div class="detail-con"> <h3 class="detail-sec-tit base-tit"><span>' + ((__t = $t("基本信息")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("crm.公海名称")) == null ? "" : __t) + '</span> <div class="item-con">' + __e(data.name) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("描述")) == null ? "" : __t) + '</span> <div class="item-con">' + __e(data.describe_info) + "</div> </div> ";
            if (data.isOpenPRM) {
                __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("公海类型")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.pool_type == 1 ? $t("伙伴独家") : $t("常规")) == null ? "" : __t) + "</div> </div> ";
            }
            __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("管理员")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.Administrator || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("sfa.pool.field.collaborator_pool_permissions")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.CollaboratorPoolPermissions || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("公海成员")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.EmployeeName || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("分配领取规则")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.is_visible_to_member == 0 ? $t("成员不可见管理员可分配") : $t("成员可见可领取管理员可分配")) == null ? "" : __t) + '</div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("规则设置")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("领取规则")) == null ? "" : __t) + "</span> ";
            var val = $t("{{ClaimIntervalDays}}天内不能连续领取同一个客户", {
                ClaimIntervalDays: data.claim_interval_days
            });
            __p += ' <div class="item-con">' + ((__t = data.claim_interval_days == 0 ? $t("随时") : val) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("保有规则")) == null ? "" : __t) + "</span> ";
            var claimNum = $t("crm.每个成员最多保有{{ClaimLimitNum}}个公海客户", {
                ClaimLimitNum: data.claim_limit_num
            });
            __p += ' <div class="item-con">' + ((__t = claimNum) == null ? "" : __t) + "， ";
            if (data.is_claim_limit_include_dealed_customers) {
                __p += " " + ((__t = $t("成交后占有公海保有量")) == null ? "" : __t) + " ";
            } else {
                __p += " " + ((__t = $t("成交后不在占有公海保有量")) == null ? "" : __t) + " ";
            }
            __p += ' </div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("转移退回规则")) == null ? "" : __t) + "</span> ";
            if (!data.allow_member_move && !data.only_allow_member_move && !data.only_allow_member_return) {
                __p += ' <div class="item-con">' + ((__t = $t("未设置")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.allow_member_move) {
                __p += ' <div class="item-con">' + ((__t = $t("成员领取后可以转移该公海客户到别的公海")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.only_allow_member_move) {
                __p += ' <div class="item-con">' + ((__t = $t("只允许本公海成员转移客户到该公海")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.only_allow_member_return) {
                __p += ' <div class="item-con">' + ((__t = $t("只允许本公海成员退回客户到该公海")) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("收回规则")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            if (RecyclingCondition.length < 1) {
                __p += " " + ((__t = $t("无收回规则")) == null ? "" : __t) + " ";
            } else {
                __p += " ";
                _.each(RecyclingCondition, function(item, num) {
                    __p += " <!-- ";
                    var html = "";
                    __p += " ";
                    _.each(item.datas, function(data, index) {
                        __p += " ";
                        var strVal = data.FieldValue ? '"' + data.FieldValue + '"' : data.hideFieldValue ? "" : "--";
                        __p += " ";
                        if (index == item.datas.length - 1) {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal;
                            __p += " ";
                        } else {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal + " ," + $t("且");
                            __p += " ";
                        }
                        __p += " ";
                    });
                    __p += ' --> <div class="assign-rule-container"> <div class="assign-rule-label"> ' + ((__t = $t("优先级")) == null ? "" : __t) + "" + ((__t = num + 1) == null ? "" : __t) + ' </div> <div class="assign-rule"> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = $t("客户范围")) == null ? "" : __t) + "</div> ";
                    if (item.ruleText) {
                        __p += ' <div class="assign-rule-dd">' + ((__t = item.ruleText) == null ? "" : __t) + "</div> ";
                    } else {
                        __p += ' <div class="assign-rule-dd">' + ((__t = $t("全部客户")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = $t("收回规则:")) == null ? "" : __t) + '</div> <div class="assign-rule-dd">' + ((__t = item.scopeText) == null ? "" : __t) + "</div> </div> </div> </div> ";
                });
                __p += " ";
            }
            __p += ' </div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("提醒条件")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = data.RemindRuleWords) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("清空规则")) == null ? "" : __t) + "</span> ";
            if (!data.is_recycling_team_member && !data.is_recycling_out_owner && !data.is_recycling_out_ordinary_team_member && !data.is_clean_owner && !data.is_clean_out_owner) {
                __p += ' <div class="item-con">' + ((__t = $t("未设置")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.is_recycling_team_member) {
                __p += ' <div class="item-con">' + ((__t = $t("公海客户负责人变化（包括负责人变为空时清空相关团队成员）")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.is_recycling_out_owner) {
                __p += " ";
                var txt1 = $t("{{pool}}{{object}}负责人清空时，清空{{object}}外部负责人", {
                    pool: txts.display_name,
                    object: txts.object_display_name
                });
                __p += ' <div class="item-con">' + ((__t = txt1) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.is_recycling_out_ordinary_team_member) {
                __p += " ";
                var txt2 = $t("清空{{object}}外部负责人时，清空{{object}}外部相关团队", {
                    pool: txts.display_name,
                    object: txts.object_display_name
                });
                __p += ' <div class="item-con">' + ((__t = txt2) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.is_clean_owner) {
                __p += ' <div class="item-con">' + ((__t = $t("客户转移到该公海时清空客户负责人")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (data.is_clean_out_owner) {
                __p += " ";
                var txt3 = $t("{{object}}转移到该{{pool}}时，清空{{object}}外部负责人", {
                    pool: txts.display_name,
                    object: txts.object_display_name
                });
                __p += ' <div class="item-con">' + ((__t = txt3) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("更换负责人规则")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            if (data.allow_admin_change_owner) {
                __p += " " + ((__t = $t("公海客户被领取分配后可进行更换负责人操作")) == null ? "" : __t) + " ";
            } else {
                __p += " " + ((__t = $t("公海客户被领取分配后不可进行更换负责人操作")) == null ? "" : __t) + " ";
            }
            __p += ' </div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("显示设置")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("公开字段")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            var templates = _.filter(pool_permission_templates, function(item) {
                __p += " ";
                return item.is_visible;
                __p += " ";
            });
            __p += " ";
            _.each(templates, function(item, index) {
                __p += " ";
                if (index == templates.length - 1) {
                    __p += " " + __e(item.field_caption) + " ";
                } else {
                    __p += " " + __e(item.field_caption) + "， ";
                }
                __p += " ";
            });
            __p += " " + ((__t = templates.length == 0 ? "--" : "") == null ? "" : __t) + ' </div> </div> <h3 class="detail-sec-tit base-tit"><span>' + ((__t = $t("其它")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("客户总数")) == null ? "" : __t) + '</span> <div class="item-con customer-count">' + ((__t = data.accounts_count) == null ? "" : __t) + "" + ((__t = $t("个")) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</span> <div class="item-con updata-time">' + ((__t = moment.unix(data.last_modified_time / 1e3, true).format("YYYY-MM-DD HH:mm")) == null ? "" : __t) + "</div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/highseasmanage/delete/delete",[],function(t,e,i){return function(o){o.options=_.map(o.options,function(t){return{value:t._id,label:t.name}}),o.options.unshift({value:"",label:$t("请选择")}),FxUI.create({template:["<fx-dialog",' class="highseas-delete-dialog"',' :visible.sync="dialogVisible"',' width="500px"',' ref="deleteDialog"',' :append-to-body="true"',' title="'.concat($t("删除"),'"'),' z-index="999"',' max-height="200px"',' @closed="closed"',">",'<div class="dialog-content">',"<div>{{countText}}</div>",'<p><fx-radio v-model="type" label="1">'.concat($t("转移到公海"),"</fx-radio>"),'<fx-select ref="highseasSelect" v-model="value" size="small" :options="options" @change="onChange"></fx-select></p>','<p><fx-radio v-model="type" label="2">'.concat($t("全部删除"),"</fx-radio></p>"),"</div>",'<div slot="footer" class="dialog-footer">','<fx-button size="small" @click="dialogVisible = false">'.concat($t("app_sail.common.dialog.btn_cancel"),"</fx-button>"),'<fx-button ref="confirmBtn" size="small" type="primary" :loading="saving" @click="onSave">'.concat($t("app_sail.common.dialog.btn_ensure"),"</fx-button>"),"</div>","</fx-dialog>"].join(""),data:function(){return{dialogVisible:!0,type:"1",count:parseInt(o.count)||0,value:"",options:o.options||[],saving:!1}},computed:{countText:function(){var t=this.count?$t("{{count}}条",{count:this.count}):"";return $t("请选择将其中的{{countText}}客户",{countText:t})+"："}},methods:{onSave:function(){var t=this,e={id:o.data._id,api_name:"HighSeasObj"};t.dialogVisible=!0,"2"==this.type?t.$confirm($t("确定删除这个公海吗"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){t.doSave(e)}):t.value?(e.target_pool_id=t.value,t.doSave(e)):CRM.util.showErrmsg($(t.$refs.highseasSelect.$el),$t("请选择公海"))},doSave:function(t){var e=this,i={type:e.type};e.saving=!0,CRM.util.poolDelete(t,i).then(function(){e.dialogVisible=!1,o.success&&o.success()}).finally(function(){e.saving=!1})},onChange:function(){this.value&&CRM.util.hideErrmsg($(this.$refs.highseasSelect.$el))},closed:function(){this.$destroy()}}})}});
define("crm-setting/customerrule/highseasmanage/format",[],function(t,e,a){return{formatObj:function(t,e){return _.isString(t.data.pool_type)&&""==t.data.pool_type&&(t.data.pool_type=0),_.isString(t.data.limit_type)&&""==t.data.limit_type&&(t.data.limit_type=1),_.isString(t.data.pool_type)&&(t.data.pool_type={private:1,normal:0,0:0,1:1}[t.data.pool_type]),_.isString(t.data.limit_type)&&(t.data.limit_type={personal:0,enterprise:1,0:0,1:1}[t.data.limit_type]),t.data.accounts_count=e.accounts_count,t},filterHideField:function(t){return _.filter(t||[],function(t){return!["extend_obj_data_id","_id","package","tenant_id","object_describe_id","object_describe_api_name","version","is_deleted","high_seas_id","high_seas_name"].includes(t.field_api_name)})}}});
function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var a;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(a="Object"===(a={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:a)||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}function _iterableToArrayLimit(t,e){var a=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=a){var n,i,o,r,c=[],s=!0,l=!1;try{if(o=(a=a.call(t)).next,0===e){if(Object(a)!==a)return;s=!1}else for(;!(s=(n=o.call(a)).done)&&(c.push(n.value),c.length!==e);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=a.return&&(r=a.return(),Object(r)!==r))return}finally{if(l)throw i}}return c}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/customerrule/highseasmanage/highseas/highseas",["crm-modules/common/util","crm-modules/components/objecttable/objecttable","../format"],function(i,t,e){var n=i("crm-modules/common/util"),a=i("crm-modules/components/objecttable/objecttable"),o=(i("../format"),Backbone.View.extend({initialize:function(t){var a=this;a.apiname="HighSeasObj",Promise.all([a.getEnterpriseQuotaStatistics(),a.getGrayByServerForLeads("graySfaOutTenantPoolBatchAdd")]).then(function(t){var t=_slicedToArray(t,2),e=t[0];a.quotaStatic=e&&t[1]||!1,a.getPRMRight(function(){a.initTable()})}).catch(function(t){a.quotaStatic=!1,a.getPRMRight(function(){a.initTable()})}),this.on("refresh",function(){a.refresh()}),a.totalCount=0},MAX_NUM:2e3,events:{"click .j-add":"addHandle"},getPRMRight:function(e){var a=this;void 0===a.isOpenPRM?$.when(CRM.util.getPRMRight()).then(function(t){a.isOpenPRM=t,e&&e()}):e&&e()},getEnterpriseQuotaStatistics:function(){return new Promise(function(e,t){CRM.util.FHHApi({url:"/EM1HER2/admin/enterpriseMeta/getEnterpriseQuotaStatistics",data:{},success:function(t){0===t.Result.StatusCode?e(t.Value.data.hasLicense):CRM.util.alert(t.Result.FailureMessage)}},{errorAlertModel:1})})},getGrayByServerForLeads:function(){var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"LeadsPoolObj";return new Promise(function(e,a){n||a();try{CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit",data:{api_name:t},success:function(t){0===t.Result.StatusCode?e(null==(t=t.Value)?void 0:t[n]):e(!1)}},{errorAlertModel:1})}catch(t){a()}})},addHandle:function(){var a=this;a.totalCount<a.MAX_NUM?i.async("crm-modules/components/customerrule/customerrule",function(t){a.highseas||(a.highseas=new t,a.highseas.on("refresh",function(t,e){a.totalCount++,a.refresh()})),a.highseas.add()}):n.remind(3,$t("贵公司可创建的公海数量已达上限"))},addfromdpmHandle:function(){var a=this;a.totalCount>=a.MAX_NUM?n.remind(3,$t("贵公司可创建的公海数量已达上限")):(n.sendLog("".concat(this.apiname,"Manage"),"batchnew",{operationId:"create"}),i.async("crm-modules/components/dpmdialog/dpmdialog",function(t){t({selectHandle:function(e){CRM.util.waiting(),i.async("vcrm/sdk",function(t){t.getComponent("poolBatchnew").then(function(t){t=t.default;a.batchnewDialog=new t({apiname:a.apiname,dData:e,type:"DepartmentObj"}),a.batchnewDialog.$on("success",function(){a.refresh()})})})}})}))},addfromOtherHandle:function(){this.selectDpmDialog()},selectDpmDialog:function(){var e=this;this.getDataDialog&&this.getDataDialog.$destroy(),this.getDataDialog=FxUI.create({template:'    <fx-dialog\n      :title="$t(\'请选择新建方式\')"\n      size="small"\n      :visible.sync="dialogSelectType"\n      custom-class="select-dpm-dialog"\n    >\n      <ul>\n        <li\n          v-for="item in retentionCordType"\n          :key="item.apiName"\n          @click="nowType = item.apiName"\n          :class="{\n            \'red-border\': item.apiName === nowType,\n          }"\n        >\n          {{ item.label }}\n          <span class="fx-icon-question" v-show="item.description" :title="item.description"></span>\n          <span class="fx-icon-ok-2"></span>\n        </li>\n      </ul>\n      <div slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="addTable" size="small"\n          >{{$t(\'确定\')}}</fx-button\n        >\n        <fx-button @click="dialogSelectType = false" size="small"\n          >{{$t(\'取消\')}}</fx-button\n        >\n      </div>\n    </fx-dialog>',data:function(){return{dialogSelectType:!0,nowType:"DepartmentObj",retentionCordType:[{apiName:"DepartmentObj",label:$t("从部门新建"),description:$t("借助部门快速生成对应{{name}}",{name:$t("crm.线索池")})},{apiName:"EnterpriseRelationObj",label:$t("从互联企业新建"),description:"LeadsPoolObj"===e.apiname?$t("sfa.crm.leadsPoolAddFromRelation"):$t("sfa.crm.highseasAddFromRelation")}]}},mounted:function(){},methods:{addTable:function(){var t=this.nowType;"DepartmentObj"===t&&(n.sendLog("".concat(e.apiname,"Manage"),"batchnew",{operationId:"create"}),e.renderDpmDialog(t)),"EnterpriseRelationObj"===t&&(e.isOpenPRM?e.renderDpmDialog(t):CRM.util.alert($t("sfa.noHavePrm")))}}})},renderDpmDialog:function(a){var n=this;i.async("crm-modules/components/dpmdialog/dpmdialog",function(t){t({objname:n.apiname,type:a,selectHandle:function(e){n.getDataDialog.dialogSelectType=!1,CRM.util.waiting(),i.async("vcrm/sdk",function(t){t.getComponent("poolBatchnew").then(function(t){t=t.default;n.batchnewDialog=new t({apiname:n.apiname,dData:e,authority:n.authority,type:a}),n.batchnewDialog.$on("success",function(){n.refresh()})})})}})})},cloneHandle:function(t,e){this.totalCount<this.MAX_NUM?this.editHandle(t,e,"clone"):n.remind(3,$t("贵公司可创建的公海数量已达上限"))},editHandle:function(e,t,a){var n=this;a=a||"edit",i.async("crm-modules/components/customerrule/customerrule",function(t){n.highseas||(n.highseas=new t,n.highseas.on("refresh",function(t,e){n.trigger("refresh")})),n.highseas[a](e)})},batcheditCheck:function(){var o=this;return new Promise(function(t){var e,a,n=o.table.getRemberData(),i=n?n.length:0;i?20<i?CRM.util.remind(3,$t("单次批量编辑不能超过20条")):(e=[],a=[],_.each(n,function(t){("private"==t.pool_type?e:a).push(t)}),e.length?CRM.util.confirm($t("独家伙伴类型的数据不允许批量编辑"),null,function(){o.table.setUncheckedRow("_id",e),o.table.reduceRemberData(e,!0),this.hide(),a.length?t(a):CRM.util.remind(3,$t("请至少选择一条数据"))},{btnLabel:{confirm:$t("继续保存")}}):t(a)):CRM.util.remind(3,$t("请至少选择一条数据"))})},batcheditHandle:function(){var a=this;a.batcheditCheck().then(function(e){i.async("vcrm/sdk",function(t){t.getComponent("poolBatchedit").then(function(t){t=t.default;a.batcheditDialog=new t({apiname:a.apiname,model:{isOpenPRM:a.isOpenPRM,outer:CRM.get("outer")||{},dataList:e},isValid:!0,isSubmit:!0}),a.batcheditDialog.$on("success",function(){a.refresh()})})})})},transferHandle:function(t,e){var a=this;CRM.api.transfer4mgr({hsID:t,success:function(){e.accounts_count=0,e.last_modified_time=(new Date).getTime(),a.trigger("refresh")}})},delHandle:function(t,a){var n=this;i.async("../delete/delete.js",function(e){CRM.util.getHighSeasList(!0).then(function(t){t=_.filter(t,function(t){return t._id!=a._id});e({data:a,options:t,count:a.accounts_count,success:function(){n.trigger("refresh","delhighsea")}})})})},initTable:function(){var i=this;if(i.table)return this;var t=[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")},{action:"addfromdpm",attrs:"data-action=addfromdpm",className:"j-action",isFold:!1,text:"".concat($t("从部门新建"),'<i class="crm-ui-title btn-addfromdpm-tip" data-pos="bottom" data-title="').concat($t("借助部门快速生成对应{{name}}",{name:$t("crm.线索池")}),'">?</i>')},{action:"batchedit",attrs:"data-action=batchedit ",className:"j-action",isFold:!1,text:$t("批量编辑")}],e=(i.quotaStatic&&(t=t.filter(function(t){return"addfromdpm"!==t.action}),e={action:"addfromOther",attrs:"data-action=addfromOther",className:"j-action",isFold:!1,text:"".concat($t("批量新建"))},t.splice(t.length-1,0,e)),a.extend({trclickHandle:function(t,e,a){var n;a.hasClass("action-btn")?(n=a.data("action"),i["".concat(n,"Handle")]&&i["".concat(n,"Handle")](a.data("id"),t)):i.showDetail(t)},operateBtnClickHandle:function(t){t=$(t.target).attr("data-action");i["".concat(t,"Handle")]&&i["".concat(t,"Handle")]()},getOptions:function(){var t=a.prototype.getOptions.apply(this,arguments),t=_.extend(t,{custom_className:"crm-highseas-table",searchTerm:!1,isOrderBy_allColumn:!1,showFilerBtn:!1,showMultiple:!0,checked:{idKey:"_id",data:[]}});return _.each(t.columns,function(t){"name"===t.data&&(t.render=function(t){return'<a href="javascript:;">'.concat(t,"</a>")})}),t},getColumns:function(){var t=this.options.columns;return t.push({data:null,width:200,title:$t("操作"),lastFixed:!0,render:function(t,e,a){var n="";return n+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="edit" href="javascript:;">').concat($t("编辑"),"</a>"),0<a.accounts_count&&(n+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="transfer" href="javascript:;">').concat($t("转移"),"</a>")),n=(n+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="del" href="javascript:;">').concat($t("删除"),"</a>"))+'<a data-id="'.concat(a._id,'" class="action-btn" data-action="clone" href="javascript:;">').concat($t("复制"),"</a>")}}),t},getExtendAttribute:function(){var t=this.get("baseScenes"),t=t&&t[0]?t.find(function(t){return"All"===t.api_name}):{};return{scene_id:t._id,scene_type:t.type,scene_api_name:t.api_name}}}));i.table=new e({el:i.$el,apiname:i.apiname,showTitle:!1,showTermBatch:!0,search:{placeHolder:$t("搜索"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:t}}),this.table.render()},showDetail:function(e){var a=this;i.async("../customerrule/customerrule",function(t){a.detail||(a.detail=new t({width:600}),a.detail.on("refresh",function(t){"delhighsea"===t&&a.totalCount--,a.refresh()})),a.detail.show(e,a.dt)})},refresh:function(){this.table&&this.table.table._clearChecked(),this.table&&this.table.refresh()},destroy:function(){var e=this;e.batchnewDialog&&e.batchnewDialog.cusDestroy(),_.each(["dt","add","detail","highseas"],function(t){e[t]&&(e[t].destroy(),e[t]=null)})}}));e.exports=o});
define("crm-setting/customerrule/highseasmanage/highseasmanage",["crm-modules/common/util","./highseas/highseas"],function(e,s,i){e("crm-modules/common/util");var h=e("./highseas/highseas");i.exports=Backbone.View.extend({initialize:function(e){this.renderhighseas()},events:{"click .set-basic":"setBasicHandle"},renderhighseas:function(){var e=this;e.highseasMange||(e.highseasMange=new h({el:e.$el}))},show:function(){this.$el.show()},hide:function(){this.highseasMange&&this.highseasMange.detail&&this.highseasMange.detail.hide(),this.$el.hide()},destroy:function(){this.highseasMange.destroy()}})});
define("crm-setting/customerrule/makeDealBehavior/makeDealBehavior",["crm-modules/common/util","../template/makeDealBehavior-html"],function(e,t,i){var n=e("crm-modules/common/util"),a=e("../template/makeDealBehavior-html");i.exports=Backbone.View.extend({initialize:function(){this.isActiveArr=[],this.hasComplete=!1,this.initCustomerBehavior()},events:{"click .crm-g-radio":"toggleHandle","click .trade-behavior-tabs .mn-radio-item":"ruleHandle","click .j-save":"save"},initCustomerBehavior:function(){var t=this;t.isGray=CRM.util.isGrayScale("CRM_ACCOUNTOBJ_DEALRULE"),t.CustomerFollowDealSetting={isGray:t.isGray},t.isGray?Promise.all([t.getFollowDealSetting(),t.getConfig()]).then(function(e){t.renderCustomerBehavior()},function(){t.renderCustomerBehavior(!0)}):t.getFollowDealSetting(function(){t.renderCustomerBehavior()})},getAllObject:function(t){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList",data:{describeApiName:"AccountObj",includeRefList:!0,includeDetailList:!0,excludeInvalid:!0},success:function(e){0==e.Result.StatusCode&&(i.allObj=e.Value.lookupDescribeList,t)&&t()}},{errorAlertModel:1})},getFollowDealSetting:function(a){var o=this;return new Promise(function(t,i){n.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/get_object_follow_deal_setting",data:{objectApiName:"AccountObj",followDealSettingType:2},success:function(e){return 0==e.Result.StatusCode?e.Value.dataList.length?(o.CustomerFollowDealSetting.CustomerDealSetting=_.find(e.Value.dataList,function(e){return 2==e.setting_type}).settingList,o.CustomerFollowDealSetting.userDefineSetting=e.Value.dataList[0].userDefineSetting||!1,o.userDefineSetting=o.CustomerFollowDealSetting.userDefineSetting,a&&a(),t(o.CustomerFollowDealSetting),o.CustomerFollowDealSetting):(n.alert($t("没有关联对象")),void t()):(CRM.util.remind(3,e.Result.FailureMessage),i(e.Result.FailureMessage))}},{errorAlertModel:1})})},getConfig:function(){var a=this;return new Promise(function(t,i){CRM.util.getConfigValue("allow_after_action_change").then(function(e){return a.CustomerFollowDealSetting.allow_after_action_change=e,t(e),a.CustomerFollowDealSetting},function(e){CRM.util.alert(e),i(e)})})},ruleHandle:function(e){var e=$(e.target),t=this.$(".trade-behavior-panel[data-type=".concat(e.data("value"),"]"));t.hasClass("panel-on")||(this.CustomerFollowDealSetting.userDefineSetting=!!+e.data("value"),this.$(".trade-behavior-panel").removeClass("panel-on"),t.addClass("panel-on"))},toggleHandle:function(e){e=$(e.target);e.is(".state-active")||(e.closest(".trade-behavior-wrap").find(".crm-g-radio").removeClass("state-active"),e.addClass("state-active"))},collectBehaviorData:function(){var s=this.CustomerFollowDealSetting,e=this.$(".trade-behavior-wrap .crm-g-radio");return _.each(e,function(e,t){for(var i=$(e).data(),a=!!$(e).is(".state-active"),o=s.CustomerDealSetting.length;o--;){var n=s.CustomerDealSetting[o];if(n.apiName===i.obj){1<n.actionList.length&&n.actionList.pop();for(var l=n.actionList.length;l--;)if(n.actionList[l].action_code===i.name){n.actionList[l].is_enable=a,delete n.actionList[l]._id;break}break}}}),s.allow_after_action_change=$(".trade-behavior-checkbox .mn-checkbox-item",this.$el).hasClass("mn-selected")?"1":"0",s},transformData:function(e){var e=$.extend(!0,{},e),t=(this.isActiveArr.length&&(e.CustomerFollowSetting=e.CustomerFollowSetting.concat(this.isActiveArr)),[]),i={setting_type:2};return i.settingList=e.CustomerDealSetting,t.push(i),t},save:function(){var e=this,t=null,i="";e.CustomerFollowDealSetting.userDefineSetting?i=$t("crm.customTransactionRulesTip"):e.CustomerFollowDealSetting.userDefineSetting!=e.userDefineSetting&&(i=$t("crm.presetTransactionRulesTip")),i?t=CRM.util.confirm($t(i),$t("提示"),function(){e.doSave(),t.hide()}):e.doSave()},doSave:function(){var t=this;this.isGray?Promise.all([this.saveConfig(),this.setCustomerFollowDealSetting()]).then(function(e){n.remind(1,$t("设置成功")),t.updateTagStatus()},function(e){n.remind(3,e||$t("设置失败，请联系客服"))}):this.setCustomerFollowDealSetting().then(function(){n.remind(1,$t("设置成功")),t.updateTagStatus()},function(e){n.remind(3,e||$t("设置失败，请联系客服"))})},updateTagStatus:function(){var e=this.$(".trade-behavior-tabs"),t=this.$(".mn-selected",e),e=this.$(".radio-tag",e),t=t.siblings(".radio-tag");e.removeClass("tag-on"),t.addClass("tag-on")},setCustomerFollowDealSetting:function(){var a=this,o=this.collectBehaviorData(),e=this.transformData(o);return a.hasComplete?Promise.resolve():(a.hasComplete=!0,new Promise(function(t,i){n.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/save_object_follow_deal_setting",data:{objectApiName:"AccountObj",dataList:e,followDealSettingType:2,userDefineSetting:a.CustomerFollowDealSetting.userDefineSetting},success:function(e){return a.hasComplete=!1,0==e.Result.StatusCode?(a.userDefineSetting=a.CustomerFollowDealSetting.userDefineSetting,a.CustomerFollowDealSetting=o,t(a.CustomerFollowDealSetting),a.CustomerFollowDealSetting):i(e.Result.FailureMessage)},error:function(){n.remind(3,$t("设置失败，请联系客服")),a.hasComplete=!1}},{errorAlertModel:1})}))},saveConfig:function(){return CRM.util.setConfigValue({key:"allow_after_action_change",value:$(".trade-behavior-checkbox .mn-checkbox-item",this.$el).hasClass("mn-selected")?"1":"0"}).then(function(e){return!0})},fbHandle:function(){this.CustomerFollowDealSetting},renderCustomerBehavior:function(e){this.el.innerHTML=e?"":a(this.CustomerFollowDealSetting)},setBasicHandle:function(e){var e=$(e.target),t=e.attr("data-key"),i=e.hasClass("mn-selected")?"1":"0";this.setConfig({key:t,value:"11"==t?"0"==i?"1":"0":i},e)},setConfig:function(e,t){n.setConfigValue(e).then(function(){n.remind(1,$t("设置成功"))},function(){n.remind(3,$t("设置失败")),t.toggleClass("mn-selected")})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){}})});
define("crm-setting/customerrule/mapmodesetting/mapmodesetting",["crm-modules/common/util","../template/mapmodesetting-html"],function(t,e,o){var i=t("crm-modules/common/util"),n=t("../template/mapmodesetting-html");o.exports=Backbone.View.extend({initialize:function(){this.comps={},this.is_active=null,this.selectOption=null,this.apiname="AccountObj",this.optionsArr=null,this.saveColorsObj={},this.optionsInputs=null,this.colorOptionsObj=null,this.isDisabled=!1,this.presetColorsArr=["#FF5730","#30C776","#189DFF","#8054DE","#FF8000","#16B4AB","#0C6CFF","#FF506B","#94CE55","#737C8C"],this.selectColorsArr=["#BF3514","#0C773D","#065FA4","#3E1D83","#CB5E0F","#00756E","#003E9B","#C1233B","#52841D","#545861"],this.initMapModeSetting(),this.getConfig()},events:{"click .j-save":"save","click .update-tip .title":"_toggleTip"},getConfig:function(){var e=this;e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=i.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"map_mode_setting_type"},success:function(t){0===t.Result.StatusCode?(e.colorOptionsObj=t.Value&&""==t.Value.value?t.Value.value:JSON.parse(t.Value.value),e.isDisabled=!(!t.Value||""!=t.Value.value),e.getDescribe(e.colorOptionsObj)):i.alert(t.Result.FailureMessage)},complete:function(){e._getAjax=null}},{errorAlertModel:1})},getConfigByChange:function(){var e=this;e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=i.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"map_mode_setting_type"},success:function(t){0===t.Result.StatusCode?e.changeObj=t.Value&&""==t.Value.value?t.Value.value:JSON.parse(t.Value.value):i.alert(t.Result.FailureMessage)},complete:function(){e._getAjax=null}},{errorAlertModel:1})},getDescribe:function(o){var i=this;CRM.util.getDescribeLayout({include_detail_describe:!1,include_layout:!1,apiname:i.apiname,layout_type:"add",recordType_apiName:"default__c"}).then(function(t){i.fieldsObject=t&&t.objectDescribe&&t.objectDescribe.fields,i.optionsArr=i.filterDataByType(i.fieldsObject),i.is_active=i.fieldsObject[o.apiname]&&i.fieldsObject[o.apiname].is_active;var t=""==o||i.is_active||!i.fieldsObject[o.apiname]?i.optionsArr:[],e=i.is_active?o.apiname:"";i.renderSelect(t,e),i.renderConfig(),i.fieldsObject[o.apiname]&&o&&o.options&&0<o.options.length?(t=i.is_active?i.updateOptionsByDescribe(o):o,i.initializeColorsList(t),""==o||i.is_active?i.hideError():i.showError()):i.renderColorsOption("",!1,"","")})},filterDataByType:function(t){if(t)return _.filter(t,function(t){if("select_one"===t.type&&0!=t.is_active)return t.value=t.api_name,t})},getOptionsByType:function(t){var i=this,t=i.getSelectType(t),n=t&&t[0].options;if(n&&0<n.length)return _.filter(n,function(t,e){if(t.color=i.presetColorsArr[e],t.selectColor=i.selectColorsArr[e],10<n.length)for(var o=10;o<n.length;o++)n[o].color=i.presetColorsArr[i.presetColorsArr.length-1],n[o].selectColor=i.selectColorsArr[i.selectColorsArr.length-1];return!t.not_usable})},updateOptionsByDescribe:function(t){var e=this;e.optionsInputs=e.getOptionsByType(t.apiname);for(var o=0;o<e.optionsInputs.length;o++){for(var i=e.optionsInputs[o],n=!1,r=0;r<t.options.length;r++){var s=t.options[r];if(i.value==s.value){i.color=s.color,i.selectColor=s.selectColor,n=!0;break}}n||(i.color="#FF5730",i.selectColor="#BF3514")}},getSelectType:function(e){var t=this.optionsArr;if(0<t.length)return _.filter(t,function(t){return t.api_name===e})},initMapModeSetting:function(){this.renderMapModeSetting()},initializeColorsList:function(t,e){var o,i=this,n=t&&t.options||i.optionsInputs;if(e=e||!0,o=i.presetColorsArr[i.presetColorsArr.length-1],$(".colorsconfig-wrapper").html(""),10<n.length){for(var r=0;r<10;r++)i.renderColorsOption(n[r].color||i.presetColorsArr[r],e,r,n[r]);for(var s=10;s<n.length;s++)i.renderColorsOption(n[s].color||o,e,s,n[s])}else for(var l=0;l<n.length;l++)i.renderColorsOption(n[l].color||i.presetColorsArr[l],e,l,n[l])},renderConfig:function(){var o=this,t=o.isDisabled?"#C1C5CE":"#0C6CFF";this.comps.member_select=FxUI.create({wrapper:".j-map-config",template:'<fx-button style="float: right; padding: 10px; color:'+t+';"type="text" :disabled=disabled @click="clearConifgHandle">'.concat($t("清空配置"),"</fx-button>"),data:function(){return{disabled:o.isDisabled}},methods:{clearConifgHandle:function(){var t=null,e=$t("确定清空此配置?"),t=CRM.util.confirm($t(e),$t("提示"),function(){t.hide(),o.saveColorsObj="",o.isDisabled=!0,o.colorOptionsObj="",o.setMapModeSetting(),$(".j-map-config").html(""),$(".j-map-select").html(""),$(".colorsconfig-wrapper").html(""),o.renderSelect(o.optionsArr,""),o.renderConfig(),o.renderColorsOption("",!1,"","")})}}})},renderSelect:function(t,e){var o=this;this.comps.member_select=FxUI.create({wrapper:".j-map-select",template:'<fx-select\n\t\t\t\t\t\t\t\tstyle="float: left"\n\t\t\t\t\t\t\t\tref="el1"\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请选择\')"\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t@change="changeHandle"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{options:t,value:e,disabled:o.isDisabled}},methods:{changeHandle:function(t){o.isDisabled=!1,$(".j-map-config").html(""),o.renderConfig(),o.selectOption=t,o.is_active=o.fieldsObject[t]&&o.fieldsObject[t].is_active,""==o.saveColorsObj?o.saveColorsObj={}:o.saveColorsObj,o.getConfigByChange(),o.colorOptionsObj&&o.colorOptionsObj.apiname==t?o.updateOptionsByDescribe(o.colorOptionsObj):o.changeObj&&o.changeObj.apiname==t?o.optionsInputs=o.changeObj.options:o.optionsInputs=o.getOptionsByType(t),o.is_active?o.initializeColorsList():o.renderColorsOption("",!1,"",""),o.saveColorsObj.apiname=t,o.saveColorsObj.label=o.fieldsObject[t].label,o.saveColorsObj.options=o.optionsInputs}}})},renderColorsOption:function(t,e,o,i){var n=this;n.comps.colorOptions_select=window.FxUI.create({wrapper:".colorsconfig-wrapper",template:'<div class="colors-item">\n\t\t\t\t\t\t\t\t<fx-col :span="20">\n\t\t\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\t\t\t:placeholder="$t(\'选项值\')"\n\t\t\t\t\t\t\t\t\t\tv-model="tagInput"\n\t\t\t\t\t\t\t\t\t\twidth="240px"\n\t\t\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t\t\t:disabled=true\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<template slot="append" v-if="showColorFlag"><fx-color-picker :curIndex="curIndex" v-model="color"  \n\t\t\t\t\t\t\t\t\t\t:hide-footer=true :predefine="predefineColors" :hide-color-panel=true @change="onChange(curIndex,color)">\n\t\t\t\t\t\t\t\t\t\t</fx-color-picker></template>\n\t\t\t\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t\t\t</fx-col>\n\t\t\t\t\t\t </div>',data:function(){return{color:t||"#FF5730",tagInput:i&&i.label?i.label:"",curIndex:o,showColorFlag:e||!1,predefineColors:["#FF5730","#30C776","#189DFF","#8054DE","#FF8000","#16B4AB","#0C6CFF","#FF506B","#94CE55","#737C8C"]}},methods:{onChange:function(t,e){n.selectOption||(n.saveColorsObj=n.colorOptionsObj);var o=n.presetColorsArr.indexOf(e);n.optionsInputs[t].color=e,n.optionsInputs[t].selectColor=n.selectColorsArr[o],n.saveColorsObj.options=n.optionsInputs}}})},save:function(){var t=this,e=null,o=$t("当前字段已被禁用，保存后将被清空");!t.is_active&&t.fieldsObject[t.colorOptionsObj&&t.colorOptionsObj.apiname]?e=CRM.util.confirm($t(o),$t("提示"),function(){e.hide(),t.saveColorsObj="",t.setMapModeSetting(),$(".j-map-select").html(""),$(".j-map-config").html(""),$(".colorsconfig-wrapper").html(""),t.renderSelect(t.optionsArr,""),t.isDisabled=!0,t.renderConfig(),t.renderColorsOption("",!1,"","")}):t.setMapModeSetting()},setMapModeSetting:function(){var t=this;t.selectOption||(t.colorOptionsObj.options=t.optionsInputs,t.saveColorsObj=t.colorOptionsObj),t.is_active||(t.saveColorsObj=""),i.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"map_mode_setting_type",value:""==t.saveColorsObj?t.saveColorsObj:JSON.stringify(t.saveColorsObj)},success:function(t){0==t.Result.StatusCode?i.remind(1,$t("设置成功")):i.remind(3,t.Result.FailureMessage||$t("设置失败，请联系客服"))},error:function(){i.remind(3,$t("设置失败，请联系客服"))}},{errorAlertModel:1})},renderMapModeSetting:function(){this.el.innerHTML=n()},_toggleTip:function(t){$(t.currentTarget).closest(".update-tip").toggleClass("update-tip-hover")},show:function(){this.$el.show()},hide:function(){this.$el.hide()},showError:function(){CRM.util.showErrmsg($(".el-input--suffix",this.$el),$t("当前字段已被禁用，保存后将被清空"))},hideError:function(){CRM.util.hideErrmsg($(".el-input--suffix",this.$el))},destroy:function(){}})});
define("crm-setting/customerrule/nonhighseasrule/nonhighseasrule",["./tpl-html"],function(e,t,i){var l=e("./tpl-html");CRM.util;i.exports=Backbone.View.extend({events:{"click .rule-nav-span":"switchRule"},initialize:function(e){this.$el.html(l(e)),this.initRecycleRule(),this.initRemindRule()},show:function(){this.$el.show()},hide:function(){this.$el.hide()},switchRule:function(e){e=$(e.currentTarget);e.hasClass("active")||(this.$(".rule-table-nav .rule-nav-span").removeClass("active"),e.addClass("active"),this.$(".rule-box").hide(),"remind"==e.data("type")?this.$(".rule-recover-deptment").hide():this.$(".rule-recover-deptment").show(),this.$(".rule-"+e.data("type")).show())},initRecycleRule:function(){var t=this;e.async("./recyclerule/recyclerule",function(e){t.recycleRule=new e({el:t.$(".rule-recover")})})},initRemindRule:function(){var t=this;e.async("./remindrule/remindrule",function(e){t.remindRule=new e({el:t.$(".rule-remind")})})},destroy:function(){this.recycleRule&&this.recycleRule.destroy&&this.recycleRule.destroy(),this.remindRule&&this.remindRule.destroy&&this.remindRule.destroy(),this.deptRuleWarp&&this.deptRuleWarp.destroy&&this.deptRuleWarp.destroy()}})});
define("crm-setting/customerrule/nonhighseasrule/recyclerule/recyclerule",["crm-widget/table/table","crm-widget/dialog/dialog","crm-modules/common/highseas/highseas","./saveprompt-html"],function(e,t,i){var a=e("crm-widget/table/table"),n=e("crm-widget/dialog/dialog"),r=e("crm-modules/common/highseas/highseas"),l=r.common,c=r.rulesFieldStatic,s=CRM.util,o=e("./saveprompt-html");i.exports=Backbone.View.extend({events:{"click .j-add-recover":"addRecoverHandle"},initialize:function(){this.departmentRule="main",this.haveRule=!1,this.deptRuleOptionsDetail={main:$t("crm.recycling.rule.depement.mainDetail"),mainAndSup:$t("crm.recycling.rule.depement.mainAndSupDetail")},this.deptRuleOptionsDescribe={main:$t("crm.recycling.rule.depement.mainDescribe"),mainAndSup:$t("crm.recycling.rule.depement.mainAndSupDescribe")},this.initTable()},initTable:function(){var l=this,r=this.getCommonParse();this._ruleDt?this._ruleDt.resize():(this._ruleDt=new a({$el:l.$el,url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/get_account_recycling_rule",requestType:"FHHApi",autoHeight:!0,showPage:!1,noAllowedWrap:!1,operate:{pos:"T",btns:[{className:"j-add-recover",text:$t("新建")}]},columns:[{data:"ruleName",title:$t("规则名称"),width:200,render:function(e,t,i){return e||"--"}},{data:"departmentIds",title:$t("crm.适用部门"),width:200,render:function(e,t,i){return i.departmentIds.map(function(e){return FS.contacts.getCircleById(e)&&FS.contacts.getCircleById(e).name||"--"}).join(",")}},{data:"recyclingRuleList",title:$t("收回规则"),width:"auto",render:function(e,t,i){var a=[],n="";return 1<e.length&&(e=e.sort(function(e,t){return e.priority-t.priority})),_.each(e,function(e){a.push(r.parseFieldRecycling(l.addStaticField([]),e))}),a.length<1?$t("无收回规则"):(a=[a.shift()],_.each(a,function(i,e){var a="";n+='<div class="item-con-sec"><label class="sec-label">'+$t("优先级")+(e+1)+"：</label>",_.each(i.datas,function(e,t){t==i.datas.length-1?a+='"'+e.FieldName+'" '+e.Compare+' "'+e.FieldValue+'"':a+='"'+e.FieldName+'" '+e.Compare+' "'+e.FieldValue+'" '+$t("且")+" "}),i.datas.length?n+='<div class="sec-con">'+a+$t("；")+i.scopeText+"</div></div>":n+='<div class="sec-con">'+i.scopeText+"</div></div>"}),n)}},{title:$t("操作"),data:"",width:200,render:function(){return'<a href="javascript:;" class="edite-recover">'+$t("编辑")+'</a><a href="javascript:;" class="del-recover">'+$t("删除")+'</a><a href="javascript:;" class="copy-recover">'+$t("复制并新建")+"</a>"}}],formatData:function(e){var t;return e.areaList&&e.areaList.length&&CRM.util.setAreaCache(e.areaList),e.recyclingRuleList&&e.recyclingRuleList[0]?(t=e.recyclingRuleList[0].department_rule||"main",l.haveRule=!0,l.updateDepartmentRule(t)):l.haveRule=!1,0==(null==e||null==(t=e.recyclingRuleList)?void 0:t.length)&&l.updateDepartmentRule("mainAndSup"),{totalCount:e.Page&&e.Page.TotalCount||0,data:e.recyclingRuleList}}}),this._ruleDt.on("trclick",function(e,t,i,a){var n=0<e.recyclingRuleList.length?e.recyclingRuleList[0].HighSeasID:"",r=[],n=(e.departmentIds.forEach(function(e){(e=FS.contacts.getCircleById(e))&&r.push({id:e.id,name:e.name,type:"g"})}),_.extend(e,{DataType:2,highSeasList:n,recyclingRuleList:e.recyclingRuleList,filterList:l.getFilterList(this.curData.data),RuleName:e.ruleName||"",OldDataIDList:e.departmentIds,DataIDList:r,department_rule:l.departmentRule}));i.hasClass("del-recover")?l.delRecoverHandle(e):i.hasClass("edite-recover")?l.editeRecoverHandle(n):i.hasClass("copy-recover")&&l.copyRecoverHandle(n)}))},updateDepartmentRule:function(e){this.departmentRule=e,this.initDepartmentRule()},initDepartmentRule:function(){var r=this;this.deptRuleWarp&&this.deptRuleWarp.destroy&&this.deptRuleWarp.destroy(),this.deptRuleWarp=null,this.deptRuleWarp=FxUI.create({wrapper:$(".rule-recover-deptment")[0],template:'\n              <div class="nonhigh-secrch-rule-deptment" v-if="haveRule">\n                  <fx-alert\n                      :closable="false"\n                      type="warning"\n                      close-text="'.concat($t("crm.recycling.rule.depement.toswitch"),'>"\n                      >\n                      <template slot="title">\n                        <div class="rule-deptment-content">\n                          <span>{{deptRuleText}}</span>\n                          <fx-tooltip :visible-arrow="false" :open-delay="500" effect="light" :content="hoverTipText"\n                            placement="bottom">\n                            <span class="fx-icon-info crm-ui-title"></span>\n                          </fx-tooltip>\n                          <span style="color: #0c6cff;cursor: pointer;margin-left: 4px" @click="showChangeDeptmentRule">').concat($t("crm.recycling.rule.depement.toswitch"),"></span>\n                        </div>\n                      </template>\n                  </fx-alert>  \n              </div>\n              "),data:function(){return{deptRuleText:r.deptRuleOptionsDetail[r.departmentRule],haveRule:r.haveRule,hoverTipText:r.deptRuleOptionsDescribe[r.departmentRule]}},methods:{showChangeDeptmentRule:function(e){var t=FxUI.create({template:'<fx-dialog\n                          :visible.sync="dialogVisible"\n                          :title="$t(\'crm.recycling.rule.depement.dialog\')"\n                          max-height="500px"\n                          :append-to-body="true"\n                          @closed="handler"\n\t                        custom-class="change-deptment-rule-dialog"\n                        >\n                        <div ref="body">\n                          <div class=\'change-deptment-rule-top\'>'.concat($t("crm.recycling.rule.depement.title"),"</div>\n                          <div class='change-deptment-rule-top-desc'>").concat($t("crm.recycling.rule.depement.title.desc"),'</div>\n                          <fx-radio-group is-vertical v-model="radio1" @change="onChange1">\n                            <fx-radio :label="3" size="mini">').concat($t("crm.recycling.rule.depement.main"),"</fx-radio>\n                          </fx-radio-group>\n                          <div class='change-deptment-rule-main-dept'>").concat($t("crm.recycling.rule.depement.mainDescribe"),'</div>\n                          <fx-radio-group is-vertical v-model="radio2" @change="onChange2">\n                            <fx-radio :label="6" size="mini">').concat($t("crm.recycling.rule.depement.mainAndSup"),"</fx-radio>\n                          </fx-radio-group>\n                          <div class='change-deptment-rule-main-higher-dept'>").concat($t("crm.recycling.rule.depement.mainAndSupDescribe"),'</div>\n                        </div>\n                        <div slot="footer" >\n                            <fx-button type="primary" @click="saveRecyclingRule" size="small">{{$t(\'确定\')}}</fx-button>\n                            <fx-button @click="dialogVisible = false" size="small">{{$t(\'取消\')}}</fx-button>\n                        </div>\n                      </fx-dialog>'),components:{},data:function(){return{dialogVisible:!0,radio1:"main"==r.departmentRule?3:null,radio2:"mainAndSup"==r.departmentRule?6:null}},methods:{onChange1:function(){3==this.radio1&&(this.radio2=null)},onChange2:function(){6==this.radio2&&(this.radio1=null)},handler:function(){t.$destroy(),t=void 0},saveRecyclingRule:function(){var a,n=this;3==this.radio1&&(a="main"),6==this.radio2&&(a="mainAndSup"),s.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/get_account_recycling_rule",data:{_isfilter:!1},success:function(e){var t,i;0==e.Result.StatusCode?(i="main",(i=null!=e&&null!=(t=e.Value)&&t.recyclingRuleList&&e.Value.recyclingRuleList[0]?e.Value.recyclingRuleList[0].department_rule||"main":i)!=a?s.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/save_department_rule",data:{department_rule:a},success:function(e){0==e.Result.StatusCode?(r.updateDepartmentRule(a),s.remind("1",$t("保存成功")),n.dialogVisible=!1,r._ruleDt.setParam({},!0)):s.alert(e.Result.FailureMessage)}}):(r.updateDepartmentRule(a),s.remind("1",$t("保存成功")),n.dialogVisible=!1,r._ruleDt.setParam({},!0))):s.alert(e.Result.FailureMessage)}})}}})}}})},addRecoverHandle:function(e){var t=this._ruleDt.curData.data;this._doAction("addRule",{DataType:2,filterList:this.getFilterList(t),department_rule:this.departmentRule})},copyRecoverHandle:function(e){e.RuleName=e.RuleName+$t("副本"),e.DataIDList=[],e.department_rule=this.departmentRule;this._doAction("addRule",e)},editeRecoverHandle:function(e){this._doAction("editRule",e)},_doAction:function(t,i){var a=this;e.async("crm-modules/components/customerrule/customerrule",function(e){a.highseas||(a.highseas=new e,a.highseas.on("refresh",function(e,t){0==e.conflictRuleList.length?(t.isEdit?s.remind(1,$t("编辑成功")):s.remind(1,$t("新建成功")),a._ruleDt.setParam({},!0)):a.showSaveRecoverDialog(e,t)})),a.highseas[t]&&a.highseas[t](i)})},delRecoverHandle:function(e){var t=this,i=s.confirm($t("确认删除这条数据"),$t("提示"),function(){i.hide(),t.delPost(e)})},delPost:function(e){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/delete_account_recycling_rule",data:{groupId:e.groupId},success:function(e){0==e.Result.StatusCode?(s.remind(1,$t("删除成功")),t._ruleDt.setParam({},!0)):s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},getCommonParse:function(){return this._parse||(this._parse=new l),this._parse},addStaticField:function(e){return e=e.concat(c)},getFilterList:function(e){var t=[];return _.each(e,function(e){t=t.concat(e.departmentIds.map(function(e){return+e}))}),t},showSaveRecoverDialog:function(e,t){var a=this,i=(a.curSaveRecoverDialog&&a.curSaveRecoverDialog.destroy(),n.extend({attrs:{title:$t("提示"),width:500,content:o(e),showScroll:!1,showBtns:!0,className:"crm-s-customerrule"},events:{"click .b-g-btn-cancel":"hide","click .climits-employee":"checkNavHandle","click .b-g-btn":"submit"},checkNavHandle:function(e){var e=$(e.currentTarget);e.hasClass("active")||(this.$(".climits-employee").removeClass("active"),e.addClass("active"),e=e.data("employee")+"circle",this.$(".climits-circle").hide(),this.$("."+e).show())},submit:function(){var e=this.$(".climits-circle .mn-selected"),i=[],e=(_.each(e,function(e,t){i.push({employeeId:$(e).data("employeeid"),dataId:$(e).data("circleid")})}),_.extend({},t.fieldData,{employeeRuleList:i}));a.saveRecover(e,function(){a.curSaveRecoverDialog.trigger("refresh")},!1)},hide:function(){return i.superclass.hide.call(this)}}));a.curSaveRecoverDialog=new i,a.curSaveRecoverDialog.render(),a.curSaveRecoverDialog.on("refresh",function(){a._ruleDt.setParam({},!0)}),a.curSaveRecoverDialog.show()},saveRecover:function(e,t,i){var a=this;delete e.RecyclingRuleList,e.department_rule=a.departmentRule,s.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/save_account_recycling_rule",data:e,success:function(e){0==e.Result.StatusCode?(i&&e.Value.conflictRuleList&&0<e.Value.conflictRuleList.length?a.showSaveRecoverDialog(e.Value):s.remind("1",$t("保存成功")),t&&t(e.Value),a.curSaveRecoverDialog&&a.curSaveRecoverDialog.destroy()):s.alert(e.Result.FailureMessage)}})},destroy:function(){this._ruleDt&&this._ruleDt.destroy&&this._ruleDt.destroy(),this.curSaveRecoverDialog&&this.curSaveRecoverDialog.destroy&&this.curSaveRecoverDialog.destroy()}})});
define("crm-setting/customerrule/nonhighseasrule/recyclerule/saveprompt-html", [ "crm-modules/common/util" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var util = require("crm-modules/common/util");
            __p += ' <div class="recover-circle-box"> <h3>' + ((__t = $t("crm.公海多部门提醒")) == null ? "" : __t) + '</h3> <div class="climits-title"> <span style="padding:0 178px 0 10px;">' + ((__t = $t("员工")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("规则")) == null ? "" : __t) + '</span> </div> <div class="climits-box fn-clear"> <div class="fn-left box-left crm-scroll"> ';
            _.each(conflictRuleList, function(item, index) {
                __p += " ";
                if (index == 0) {
                    __p += ' <div class="climits-employee active" title="' + ((__t = util.getEmployeeById(item.employeeId) ? util.getEmployeeById(item.employeeId).name : "") == null ? "" : __t) + '" data-employee="' + ((__t = item.employeeId) == null ? "" : __t) + '"> ' + ((__t = util.getEmployeeById(item.employeeId) ? util.getEmployeeById(item.employeeId).name : "--") == null ? "" : __t) + " </div> ";
                } else {
                    __p += ' <div class="climits-employee" title="' + ((__t = util.getEmployeeById(item.employeeId) ? util.getEmployeeById(item.employeeId).name : "--") == null ? "" : __t) + '" data-employee="' + ((__t = item.employeeId) == null ? "" : __t) + '"> ' + ((__t = util.getEmployeeById(item.employeeId) ? util.getEmployeeById(item.employeeId).name : "--") == null ? "" : __t) + " </div> ";
                }
                __p += " ";
            });
            __p += ' </div> <div class="fn-left box-right crm-scroll"> ';
            _.each(conflictRuleList, function(item, index) {
                __p += " ";
                if (index == 0) {
                    __p += ' <div class="climits-circle ' + ((__t = item.employeeId) == null ? "" : __t) + 'circle mn-radio-box"> ';
                    _.each(item.conflictRuleList, function(ite, ind) {
                        __p += ' <div class="circle-radio"> <span class="mn-radio-item ';
                        if (ite.selectedRule) {
                            __p += "mn-selected";
                        }
                        __p += '" data-circleid="' + ((__t = ite.dataId) == null ? "" : __t) + '" data-employeeid="' + ((__t = item.employeeId) == null ? "" : __t) + '"></span> <span title="' + ((__t = util.getCircleById(ite.dataId) && util.getCircleById(ite.dataId).name || "--") == null ? "" : __t) + "" + ((__t = $t("规则")) == null ? "" : __t) + '" style="padding-left: 10px;">' + ((__t = util.getCircleById(ite.dataId) && util.getCircleById(ite.dataId).name || "--") == null ? "" : __t) + "" + ((__t = $t("规则")) == null ? "" : __t) + "</span> </div> ";
                    });
                    __p += " </div> ";
                } else {
                    __p += ' <div class="climits-circle b-g-hide ' + ((__t = item.employeeId) == null ? "" : __t) + 'circle mn-radio-box"> ';
                    _.each(item.conflictRuleList, function(ite, ind) {
                        __p += ' <div class="circle-radio"> <span class="mn-radio-item ';
                        if (ite.selectedRule) {
                            __p += "mn-selected";
                        }
                        __p += '" data-circleid="' + ((__t = ite.dataId) == null ? "" : __t) + '" data-employeeid="' + ((__t = item.employeeId) == null ? "" : __t) + '"></span> <span title="' + ((__t = util.getCircleById(ite.dataId) && util.getCircleById(ite.dataId).name || "--") == null ? "" : __t) + "" + ((__t = $t("规则")) == null ? "" : __t) + '" style="padding-left: 10px;">' + ((__t = util.getCircleById(ite.dataId) && util.getCircleById(ite.dataId).name || "--") == null ? "" : __t) + "" + ((__t = $t("规则")) == null ? "" : __t) + "</span> </div> ";
                    });
                    __p += " </div> ";
                }
                __p += " ";
            });
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/nonhighseasrule/remindrule/remindrule",["crm-widget/table/table"],function(l,t,e){var i=l("crm-widget/table/table"),n=CRM.util;e.exports=Backbone.View.extend({events:{"click .j-add":"addHandle"},initialize:function(){this.initTable()},initTable:function(){var r=this;this._ruleDt?this._ruleDt.resize():(this._ruleDt=new i({$el:r.$el,url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/get_account_remind_rule",requestType:"FHHApi",autoHeight:!0,showPage:!1,noAllowedWrap:!1,operate:{pos:"T",btns:[{className:"j-add",text:$t("新建")}]},columns:[{data:"departmentIds",title:$t("crm.适用部门"),width:200,render:function(t,e,i){return i.departmentIds.map(function(t){return FS.contacts.getCircleById(t)&&FS.contacts.getCircleById(t).name||"--"}).join(",")}},{data:"remindRuleList",title:$t("提醒规则"),width:"auto",render:function(t,e,i){var n=[];return _.each(t,function(t){var e=2==t.rule_type?t.deal_days:t.follow_up_days,t=[$t("不收回"),$t("未成交"),$t("未跟进")][t.rule_type-1];n.push(e+$t("天")+t+$t("提醒负责人"))}),n=n.join($t("；"))}},{title:$t("操作"),data:"",width:200,render:function(){return'<a href="javascript:;" class="table-action-btn" data-action="edit" >'.concat($t("编辑"),'</a>\n                                <a href="javascript:;" class="table-action-btn" data-action="delete">').concat($t("删除"),"</a>")}}],formatData:function(t){return{totalCount:t.remindRuleList&&t.remindRuleList.length||0,data:t.remindRuleList}}}),this._ruleDt.on("trclick",function(t,e,i,n){var a=i.data("action");r["".concat(a,"Handle")]&&r["".concat(a,"Handle")](i,t)}))},addHandle:function(){this.initRuleDialog("add")},editHandle:function(t,e){this.initRuleDialog("edit",e)},deleteHandle:function(t,e){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/delete_account_remind_rule",data:{groupId:e.groupId},success:function(t){0==t.Result.StatusCode&&(CRM.util.remind(1,$t("删除成功")),i._ruleDt.setParam({},!0))}})},initRuleDialog:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=this,e={edit:$t("编辑"),add:$t("新建")},a=$t("{{action}}非公海客户提醒规则",{action:e[t]}),e=n._ruleDt.getCurData(),r=[];_.each(e,function(t){r=r.concat(t.departmentIds)}),l.async("vcrm/sdk",function(t){t.getComponent("poolFormdialog").then(function(t){var t=t.default,e=[{label:$t("部门"),field:"departmentIds",is_require:!0,renderComp:"Department",excludeItems:r},{label:$t("提醒规则"),field:"remindRuleList",is_require:!0,renderComp:"RemindRule1"}];CRM.util.isGrayScale("CRM_POOL_SKIPHOLIDAYS")&&e.push({label:"",field:"skip_holidays",fields:["skip_holidays"],renderComp:"CheckBox"}),n.ruleDialog=new t({apiname:"HighSeasObj",columns:e,value:i,title:a}),n.ruleDialog.$on("save",function(t){n.doSave(t).then(function(){CRM.util.remind(1,$t("操作成功")),n._ruleDt.setParam({},!0)})})})})},toSubmitData:function(t){var e={remindRule:{}};return e.groupId=t.groupId,e.remindRule=t,e},doSave:function(t){return t=this.toSubmitData(t),new Promise(function(e){n.FHHApi({url:"/EM1HNCRM/API/v1/object/account_recycling_rule/service/save_account_remind_rule",data:t,success:function(t){0==t.Result.StatusCode?(n.remind("1",$t("保存成功")),e(t.Value)):n.alert(t.Result.FailureMessage)},error:function(){n.alert($t("系统异常"))}})})},destroy:function(){this._ruleDt&&this._ruleDt.destroy&&this._ruleDt.destroy(),this.ruleDialog&&this.ruleDialog.destroy&&this.ruleDialog.destroy()}})});
define("crm-setting/customerrule/nonhighseasrule/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro" style="padding-right: 110px;"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ol> <li>" + ((__t = $t("1不属于任何公海的客户称作非公海客户。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.部门设置收回规则说明")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3可对部门设置提醒信息。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("4客户下产生新的销售记录商机成交等内容时视为跟进。")) == null ? "" : __t) + '</li> </ol> <div class="share-group-box" data-title="rulerecover"></div> </div> <div class="rule-table-nav"> <span class="rule-nav-span active left" data-type="recover">' + ((__t = $t("收回规则")) == null ? "" : __t) + '</span> <span class="rule-nav-span" data-type="remind">' + ((__t = $t("提醒规则")) == null ? "" : __t) + '</span> </div> <div class="rule-recover-deptment"></div> <div class="rule-box rule-recover"> <div class="crm-loading"></div> </div> <div class="rule-box rule-remind b-g-hide"> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/template/customerbehavior-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="follow-behavior"> <div class="crm-intro-container"> <div class="crm-intro"> <h3>' + ((__t = $t("跟进行为")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("crm.跟进行为更改")) == null ? "" : __t) + "</li> <!-- <li>" + ((__t = $t("系统默认的跟进行为有客户的")) == null ? "" : __t) + '"' + ((__t = $t("分配")) == null ? "" : __t) + " 、" + ((__t = $t("领取")) == null ? "" : __t) + "" + ((__t = $t("客户")) == null ? "" : __t) + "、" + ((__t = $t("更换负责人")) == null ? "" : __t) + '"</li> --> </ol> </div> </div> <div class="behavior-btn-box"> <a href="javascript:;" class="fx-btn fx-btn-primary setting-btn">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <a href="javascript:;" class="fx-btn fx-btn-primary add-btn">' + ((__t = $t("添加")) == null ? "" : __t) + '</a> </div> <div class="behavior-table-box"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/template/customercheck-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-warn-bar ' + ((__t = isCheckOpen ? "" : "crm-hide") == null ? "" : __t) + '">' + ((__t = $t("为了更灵活方便的进行客户报备")) == null ? "" : __t) + "," + ((__t = $t("CRM现已提供")) == null ? "" : __t) + '<a href="#crmmanage/=/module-approval">' + ((__t = $t("审批流程管理")) == null ? "" : __t) + "</a>，" + ((__t = $t("crm.建议设置审批流程")) == null ? "" : __t) + "(" + ((__t = $t("如果开启客户报备功能则不能使用客户的审批流程管理")) == null ? "" : __t) + ')</div> <div class="crm-warn-bar ' + ((__t = isCheckOpen ? "crm-hide" : "") == null ? "" : __t) + '">' + ((__t = $t("crm.报备功能被取代提醒")) == null ? "" : __t) + '</div> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ol> <li>" + ((__t = $t("1如需使用客户报备功能开启报备开关设置报备审核人。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.开启报备后说明")) == null ? "" : __t) + '</li> </ol> <div class="share-group-box" data-title="rulecheck"></div> </div> <div class="crm-column"> <div class="column-item"> <span class="column-lb">' + ((__t = $t("报备开关")) == null ? "" : __t) + '</span> <div class="column-con"> <div class="mn-radio-box ' + ((__t = isCheckOpen ? "mn-selected" : "mn-disabled") == null ? "" : __t) + '"> <p> <span class="mn-radio-item ' + ((__t = isCheckOpen ? "mn-selected" : "disabled-selected") == null ? "" : __t) + '" data-value="1" data-title="' + ((__t = $t("客户报备")) == null ? "" : __t) + '"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> <span class="mn-radio-item ' + ((__t = !isCheckOpen ? "mn-selected" : "") == null ? "" : __t) + '" data-value="0" data-title="' + ((__t = $t("客户报备")) == null ? "" : __t) + '"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </p> </div> </div> </div> <div class="column-item choose-checker ' + ((__t = !isCheckOpen ? "b-g-hide" : "") == null ? "" : __t) + '"> <span class="column-lb">' + ((__t = $t("报备审核人")) == null ? "" : __t) + '</span> <div class="column-con"> <div class="check-employees name-box"> ';
            _.each(checker, function(item) {
                __p += " ";
                if (item) {
                    __p += ' <div class="name-item" data-id="' + ((__t = item.id) == null ? "" : __t) + '"> <img src="' + ((__t = item.profileImage) == null ? "" : __t) + '" /> <span class="name-text">' + ((__t = item.name) == null ? "" : __t) + '</span> <span class="close-name">×</span> </div> ';
                }
                __p += " ";
            });
            __p += ' </div> </div> <span class="add-btn add-checker">+' + ((__t = $t("新增审核人")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("客户和公海管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab customer-rule-tab"> <a class="item page1" href="#crm/setting/customerrule/=/page1">' + ((__t = $t("公海管理")) == null ? "" : __t) + '</a> <a class="item page2" href="#crm/setting/customerrule/=/page2">' + ((__t = $t("非公海客户规则")) == null ? "" : __t) + "</a> ";
            if (isCheckOpen) {
                __p += ' <a class="item page3" href="#crm/setting/customerrule/=/page3">' + ((__t = $t("客户报备")) == null ? "" : __t) + "</a> ";
            }
            __p += " ";
            if (is_new_rownership) {
                __p += ' <a class="item page8" href="#crm/setting/customerrule/=/page8">' + ((__t = $t("客户保有量")) == null ? "" : __t) + "</a> ";
            } else {
                __p += ' <a class="item page4" href="#crm/setting/customerrule/=/page4">' + ((__t = $t("客户保有量上限")) == null ? "" : __t) + "</a> ";
            }
            __p += ' <a class="item page5" href="#crm/setting/customerrule/=/page5">' + ((__t = $t("客户规则设置")) == null ? "" : __t) + "</a> ";
            if (crmVersion != "kis_edition") {
                __p += ' <a class="item page6" href="#crm/setting/customerrule/=/page6">' + ((__t = $t("客户跟进规则")) == null ? "" : __t) + '</a> <a class="item page7" href="#crm/setting/customerrule/=/page7">' + ((__t = $t("客户成交规则")) == null ? "" : __t) + "</a> ";
            }
            __p += " ";
            if (hierarchyRights) {
                __p += ' <a class="item page9" href="#crm/setting/customerrule/=/page9">' + ((__t = $t("客户层级配置")) == null ? "" : __t) + "</a> ";
            }
            __p += ' <a class="item page10" href="#crm/setting/customerrule/=/page10">' + ((__t = $t("客户联系人设置")) == null ? "" : __t) + "</a> ";
            if (contactPersonnelRelationRights) {
                __p += ' <a class="item page12" href="#crm/setting/customerrule/=/page12">' + ((__t = $t("人脉关系雷达配置")) == null ? "" : __t) + "</a> ";
            }
            __p += ' <a class="item page11" href="#crm/setting/customerrule/=/page11">' + ((__t = $t("地图模式设置")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item highseas-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item recover-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item check-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item limit-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item basic-box mn-checkbox-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item behavior-box " style="display:none;"> <div class="crm-loading"></div> </div> <div class="item makeDeal-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item ownership-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item hierarchy-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item contactrelations-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item contactpersonnelrelation-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item mapmodesetting-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/customerrule/template/makeDealBehavior-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="trade-behavior"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("成交规则主要影响客户的【成交状态】与【最后一次成交时间】")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("不可同时使用")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("自定义成交业务逻辑")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("手动更改成交状态")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("预置成交规则时更新成交状态会失败")) == null ? "" : __t) + '</li> </ol> </div> <h3 class="trade-behavior-title">' + ((__t = $t("成交规则")) == null ? "" : __t) + '</h3> <div class="trade-behavior-tabs mn-radio-box" data-key="obj.userDefineSetting"> <div class="radio-item"> <span class="mn-radio-item ' + ((__t = !obj.userDefineSetting ? "mn-selected" : "") == null ? "" : __t) + '" data-value="0"></span> <span class="radio-lb">' + ((__t = $t("预置成交规则")) == null ? "" : __t) + '</span> <span class="radio-tag ' + ((__t = !obj.userDefineSetting ? "tag-on" : "") == null ? "" : __t) + '">' + ((__t = $t("crm.accountobj.dealrule.inuse")) == null ? "" : __t) + '</span> </div> <div class="radio-item"> <span class="mn-radio-item ' + ((__t = obj.userDefineSetting ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span> <span class="radio-lb">' + ((__t = $t("自定义成交规则")) == null ? "" : __t) + '</span> <span class="radio-tag ' + ((__t = obj.userDefineSetting ? "tag-on" : "") == null ? "" : __t) + '">' + ((__t = $t("crm.accountobj.dealrule.inuse")) == null ? "" : __t) + '</span> </div> </div> <div class="trade-behavior-panel ' + ((__t = !obj.userDefineSetting ? "panel-on" : "") == null ? "" : __t) + '" data-type="0"> <div class="trade-behavior-inpanel"> <p class="line-mb-mid">' + ((__t = $t("当销售团队成员对客户进行下列操作时客户状态会变为已成交")) == null ? "" : __t) + "/" + ((__t = $t("多次成交最后成交时间会自动更新进而影响客户回收时间。")) == null ? "" : __t) + '</p> <p class="line-mb-mid">' + ((__t = $t("作废或恢复对象数据时影响客户成交状态与最后一次成交时间")) == null ? "" : __t) + '</p> <div class="trade-behavior-wrap"> ';
            _.each(obj.CustomerDealSetting, function(items) {
                __p += " ";
                _.each(items.actionList, function(item) {
                    __p += " ";
                    if (item.is_visible) {
                        __p += ' <div class="item"> <div class="crm-g-radio ' + ((__t = item.is_enable ? "state-active" : "") == null ? "" : __t) + '" data-value="' + ((__t = item.is_enable) == null ? "" : __t) + '" data-name="' + ((__t = item.action_code) == null ? "" : __t) + '" data-obj="' + ((__t = item.source_object_api_name) == null ? "" : __t) + '" data-caption="' + ((__t = item.action_label) == null ? "" : __t) + '"></div> <span class="text">' + ((__t = item.action_label) == null ? "" : __t) + "</span> </div> ";
                    }
                    __p += " ";
                });
                __p += " ";
            });
            __p += " </div> </div> ";
            if (obj.isGray) {
                __p += ' <div class="trade-behavior-cbwrapper"> <div class="trade-behavior-checkbox mn-checkbox-box"> <span class="mn-checkbox-item ' + ((__t = obj.allow_after_action_change == "1" ? "mn-selected" : "") == null ? "" : __t) + '" data-key="allow_after_action_change"></span> <span class="check-lb">' + ((__t = $t("允许流程后动作更新成交状态")) == null ? "" : __t) + '</span> </div> <p class="trade-behavior-tip">' + ((__t = $t("流程变更成交状态为预置选项可能会与预置成交规则发生冲突")) == null ? "" : __t) + "</p> </div> ";
            }
            __p += ' </div> <div class="trade-behavior-panel ' + ((__t = obj.userDefineSetting ? "panel-on" : "") == null ? "" : __t) + '" data-type="1"> <div class="trade-behavior-inpanel"> <p class="line-mb-mid">' + ((__t = $t("保存后，【成交状态】与【最后一次成交时间】字段将支持新建更新导入、流程后动作变更。")) == null ? "" : __t) + '</p> <p class="line-mb-mid">' + ((__t = $t("最后一次成交时间会影响到客户的自动收回")) == null ? "" : __t) + '</p> </div> </div> <div class="trade-behavior-btns"><div class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div></div> </div>";
        }
        return __p;
    };
});
define("crm-setting/customerrule/template/mapmodesetting-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="mapmode-setting"> <div class="update-tip update-tip-hover"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="title">' + ((__t = $t("注意")) == null ? "" : __t) + '</div> <div class="text">' + ((__t = $t("8.0.0及更高版本（包含8.0.0）后本页面的配置仅对手机端生效；电脑网页端需在客户对象管理-列表页布局-地图视图中进行配置气泡颜色。")) == null ? "" : __t) + " <a href='#crmmanage/=/module-myobject/api_name-AccountObj/child_type-layout/sub_child_type-list'>" + ((__t = $t("点击去配置网页端")) == null ? "" : __t) + '</a></div> </div> </div> <div class="crm-intro"> <h3>' + ((__t = $t("应用场景说明:")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("针对不同客户，设置差异化的气泡颜色，以便于在地图模式查看客户分布时，更加直观的评估趋势。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("配置后，作用于客户列表的地图模式、移动端客户列表的地图模式、移动端外勤选客户的地图模式。")) == null ? "" : __t) + '</li> </ol> </div> <h3 class="mapmode-setting-title">' + ((__t = $t("选择影响气泡颜色的字段")) == null ? "" : __t) + '</h3> <div class="map-mode-select"> <span class="j-map-select" style="overflow: hidden;"></span> <span class="j-map-config" style="overflow: hidden;"></span> </div> <h3 class="mapmode-setting-title">' + ((__t = $t("为选项值配置颜色")) == null ? "" : __t) + '</h3> <div class="content"> <div class="colorsconfig-wrapper"></div> </div> <div class="mapmode-setting-btns"><div class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div></div> </div>";
        }
        return __p;
    };
});