function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,a=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,r)),a}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/objmap/action/action",["crm-modules/common/util","crm-modules/action/field/field","./view/view","./view/editview","./view/editnextview","./view/nextview","./fetch","crm-modules/action/common/common"],function(e,t,r){var u=e("crm-modules/common/util"),a=e("crm-modules/action/field/field"),n=e("./view/view"),i=e("./view/editview"),s=e("./view/editnextview"),o=e("./view/nextview"),l=e("./fetch"),e=e("crm-modules/action/common/common").extend({add:function(){var i=this;a.add({title:$t("新建映射规则"),ownerType:"map_btn",show_type:"full",className:"crm-a-objmap",data:{location:$t("按钮会出现在数据源对象的详情页"),role:["00000000000000000000000000000006"],btn_api_name:"btn_"+u.getUUId(5)+"__c",map_api_name:"map_"+u.getUUId(5)+"__c"},View:n,NextView:o,holdNextView:!0,submit:function(t,r){var e=r.nextView,a=e.model.get("preData")||e.options.preData;e.collect(function(e){e=_.extend({describe_api_name:a.map_object[0].source_api_name,rule_list:i._formatData(a.map_object,a.map_api_name,a.map_name)},{button:e.button,roles:e.roles});u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/createRule",data:e,success:function(e){0===e.Result.StatusCode?(i.trigger("refresh","add",e),r.destroy()):(u.alert(e.Result.FailureMessage),i.trigger("refresh","add",e))}},{errorAlertModel:1,submitSelector:t&&t.target&&$(t.target)})})}})},_formatData:function(e,t,r){var a=[];return _.each(e,function(e){_.isEmpty(e.master_api_name)?a.push(_.extend(e,{rule_name:r,rule_api_name:t})):a.push(_.extend(e,{master_rule_api_name:t}))}),a},edit:function(e){var o=this;this.getMappingDetail(e,function(n){a.edit(_.extend({title:$t("编辑映射规则"),ownerType:"map_btn",show_type:"full",className:"crm-a-objmap",data:{location:$t("按钮会出现在数据源对象的详情页"),map_object:n.rule_list||n.ruleList,map_name:e.rule_name,map_api_name:e.rule_api_name,btn_name:n.button&&n.button.label,btn_api_name:n.button&&n.button.api_name,description:n.button&&n.button.description,btn_filter:n.button&&n.button.wheres,role:n.roles},View:i,NextView:o.shouldShowNextView(e)?s:"",holdNextView:!0,submit:function(e,t){var r=t.nextView,a=t.view,i=r?r.model.get("preData")||r.options.preData:a.collect();a&&!a.validate()?a.scrollToError():r?r.collect(function(e){e=_.extend({describe_api_name:i.map_object[0].source_api_name,rule_list:o._formatData(i.map_object,i.map_api_name,i.map_name)},{button:e.button,roles:e.roles});u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/updateRule",data:e,success:function(e){0===e.Result.StatusCode?(o.trigger("refresh","edit",e),t.destroy()):(u.alert(e.Result.FailureMessage),o.trigger("refresh","edit",e))}},{errorAlertModel:1})}):(a={describe_api_name:i.map_object[0].source_api_name,rule_list:o._formatData(i.map_object,i.map_api_name,i.map_name),button:n.button,roles:n.roles},u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/updateRule",data:a,success:function(e){0===e.Result.StatusCode?(o.trigger("refresh","edit",e),t.destroy()):(u.alert(e.Result.FailureMessage),o.trigger("refresh","edit",e))}},{errorAlertModel:1}))}},e))})},shouldShowNextView:function(e){return"custom"==e.define_type||"rule_quoteobj2salesorderobj__c"==e.rule_api_name},enable:function(e,t){var r=this;u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/enableRule",data:{rule_api_name:e.rule_api_name,describe_api_name:e.source_api_name},success:function(e){0===e.Result.StatusCode?e.Value.isSuccess&&r.trigger("refresh","enable"):u.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},disable:function(e,t){var r=this;u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/disableRule",data:{rule_api_name:e.rule_api_name,describe_api_name:e.source_api_name},success:function(e){0===e.Result.StatusCode?e.Value.isSuccess&&r.trigger("refresh","disable"):u.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},delete:function(e,t){var r=this;u.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/deleteRule",data:{rule_api_name:e.rule_api_name,describe_api_name:e.source_api_name},success:function(e){0===e.Result.StatusCode?e.Value.isSuccess&&r.trigger("refresh","delete"):u.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},getMappingDetail:function(e,r){var a=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/findByApiName",data:{rule_api_name:e.rule_api_name,rule_action_name:e.rule_action_name},success:function(t){0===t.Result.StatusCode?(t.Value.rule_list||t.Value.ruleList)&&a.filterWhenMasterCreateMD(t.Value.rule_list||t.Value.ruleList).then(function(e){r&&r(_objectSpread(_objectSpread({},t.Value),{},{rule_list:e}))}):u.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},filterWhenMasterCreateMD:function(n){function o(e){return(e||[]).map(function(e){var t=_.findWhere(e.fields,{type:"master_detail"});return _objectSpread(_objectSpread({},e),{},{isCreateWhenMasterCreate:null==t?void 0:t.is_create_when_master_create})})}var e=_.find(n,function(e){return _.isEmpty(e.master_api_name)});return new Promise(function(i){$.when(l.getAllMDFieldsByApiName(e.source_api_name),l.getAllMDFieldsByApiName(e.target_api_name)).done(function(e,t){var r,a;"success"==e[1]&&"success"==t[1]&&(e=e[0],t=t[0],0==e.Result.StatusCode)&&0==t.Result.StatusCode?(r=o(e.Value.detailDescribeList),a=o(t.Value.detailDescribeList),i(n.filter(function(e){var t;return!!_.isEmpty(e.master_api_name)||(null==(t=_.findWhere(r,{api_name:e.source_api_name}))?void 0:t.isCreateWhenMasterCreate)&&(null==(t=_.findWhere(a,{api_name:e.target_api_name}))?void 0:t.isCreateWhenMasterCreate)}))):i(n)})})}});r.exports=e});
define("crm-setting/objmap/action/components/apinameinput/apinameinput",["crm-modules/action/field/field"],function(t,e,i){var n=t("crm-modules/action/field/field").components.input;return n.extend({validate:function(t){t=t||this.getIptValue();/^[a-zA-Z](_?(?!_)\w)*__c$/.test(t)?(n.prototype.validate.call(this,t),this.hideError()):this.showError()},getValue:function(){return this.getIptValue()}})});
define("crm-setting/objmap/action/components/detailmap/detailmap",["../../config/nconfig","./template/edit-html","./fieldmodel.js","./fieldcollection","./detailmapline","./template/list-tpl-html","./template/second-list-html","./mapcollection","./mapmodel","crm-modules/common/util"],function(e,t,i){var a=e("../../config/nconfig"),n=e("./template/edit-html"),l=e("./fieldmodel.js"),o=e("./fieldcollection"),s=e("./detailmapline"),p=e("./template/list-tpl-html"),d=e("./template/second-list-html"),c=e("./mapcollection"),r=e("./mapmodel"),m=e("crm-modules/common/util"),e=Backbone.View.extend({opts:{data:{sourceObj:{},targetObj:{},sourceFields:[],targetFields:[],ruleList:[],rule_api_name:"",master_api_name:"",define_type:"",lookUpData:{},rule_name:"",isEdit:!1}},i:0,events:{"click .j-add":"addFieldMapLine","click .second-items-wrapper .right-wrapper ":"showSecondRListHandle","click .inp-wrapper":"showListHandle","click .second-items-wrapper .j-check":"checkSecondOptsList","click .j-check":"checkFieldList","input input":"inputHandle","click .j-icon":"removeMapLine","click .j-lookup":"checkLookUpIconHandle"},initialize:function(e){this.options=$.extend(!0,{},this.opts,e),this.setElement(this.options.el),this.detailmapLine={},this.collection={},this.collection.maps=new c,this.collection.sourceField=new o,this.collection.targetField=new o,this.parseModel(this.options),this.Edit=this.options.data.isEdit},render:function(){var e=this.options.data;this.$el.html(n({fromName:e.sourceObj.name,toName:e.targetObj.name})),this.checkLookUp(),this.Edit||this.subShowRecordType(),this.Edit||this.addFieldMapLine(),this.Edit&&this.setData()},checkLookUp:function(){var e=this.options.data.lookUpData;e.hasLookUp&&this.addFieldMapLine("lookup",e.map_data.targetLookUpObj)},subShowRecordType:function(){var e,t,i;this.options.data.master_api_name&&(e=this.addFieldMapLine("record_type"),t=this.collection.sourceField.findWhere({type:"record_type"}),i=this.collection.targetField.findWhere({type:"record_type"}),this.detailmapLine[e].setVal(t,{noSetLike:!0}),this.detailmapLine[e].setVal(i,{noSetLike:!1}),t.set("checked",!0),i.set("checked",!0))},checkLookUpIconHandle:function(e){var e=$(e.currentTarget),t=e.siblings(".right-wrapper"),i=e.siblings(".left-wrapper").find("input");e.hasClass("mn-selected")?(t.find(".checked-list .close").eq(0).trigger("click"),i.attr("data-value",""),t.append('<div class="disabled"></div>').find("input").removeClass("error-ipt"),e.siblings(".error-box").html("")):(i.attr("data-value","_id"),t.find(".disabled").remove())},inputHandle:function(){var e=this,t=arguments;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout(function(){e._inputHandle.apply(e,t)},200)},_inputHandle:function(e){var e=$(e.currentTarget),t=e.closest(".inp-wrapper"),e=e.val();t.trigger("click",e)},setData:function(){var n=this,e=this.options.data.ruleList.field_mapping;_.findWhere(e,{source_field_api_name:"record_type"})||this.subShowRecordType(),_.each(e,function(e){var t,i,a;"_id"!=e.source_field_api_name&&(t=n.collection.sourceField.findWhere({api_name:e.source_field_api_name}),i=n.collection.targetField.findWhere({api_name:e.target_field_api_name}),t)&&(a=n.addFieldMapLine(t.get("type")),t&&n.detailmapLine[a].setVal(t,{noSetLike:!0}),i)&&n.detailmapLine[a].setVal(i,{noSetLike:!0,secondData:e.option_mapping})})},getData:function(){var e=this,t=[];if(this.collection.maps.each(function(e){e.get("mapLine").getCollection()&&t.push(e.get("mapLine").getCollection())}),!_.isEmpty(t))return $.extend(!0,{},_.pick(e.options.data,"rule_name","rule_api_name","master_api_name","define_type"),{source_api_name:e.options.data.sourceObj.value,target_api_name:e.options.data.targetObj.value,target_display_name:e.options.data.targetObj.name,field_mapping:t,tenant_id:e.options.data.ruleList&&e.options.data.ruleList.tenant_id,status:e.options.data.ruleList&&e.options.data.ruleList.status||0})},checkSecondOptsList:function(e){var t=$(e.currentTarget),i=$(e.target),a=t.closest(".new-item"),n=a.find(".inp-wrapper input").eq(0).attr("data-value"),l=t.closest(".list"),o=[],s=(i.hasClass("j-select-option")||(i=i.closest(".j-select-option")),this.getMapLine(t).mapLine),p=this.getModel(s.getVal().target_field_api_name,"right");i.find(".mn-checkbox-item").toggleClass("mn-selected",!i.find(".mn-checkbox-item").hasClass("mn-selected")),(-1==i.data("value")?i.siblings("li .mn-checkbox-item"):i.siblings('li[data-value="-1"]').find(".mn-checkbox-item")).removeClass("mn-selected"),"select_many"==p.get("type")&&(t=t.find("li"),_.each(t,function(e){$(e).find(".mn-checkbox-item").hasClass("mn-selected")&&o.push($(e).data("value"))})),_.contains(["select_one","record_type"],p.get("type"))&&(o=[i.attr("data-value")]),p&&s.setSecondVal(p,n,o.length?o:[],a),"select_many"!==p.get("type")&&l.hide(),e.stopImmediatePropagation()},checkFieldList:function(e,t){var i=$(e.target),a=i.data("wrapper"),n=i.closest(".list"),l=n.siblings(".checked-list"),l=(l.find("li")&&l.find("li .close").trigger("click"),this.getMapLine(i)),i=this.getModel(i.data("value"),a);i&&(l.mapLineModel.set("left-wrapper"==i.get("wrapperType")?"left":"right",i),l.mapLine.setVal(i,{noSetLike:t})),n.hide(),e.stopPropagation()},addFieldMapLine:function(e,t){var i=this,a=this.$el.find(".edit-page .context");return a.append('<div data-name="detail'+this.i+'" class="detail detail'+this.i+'"></div>'),this.detailmapLine[this.i]=new s({el:a.find(".detail"+this.i),isEdit:i.Edit,type:e||"custom",lookupTargetObj:t,md_type:i.options.data.master_api_name?1:0}),this.detailmapLine[this.i].on("changeModel",function(e,t){e=i.getModel(e,t);e&&e.set("checked",!1)}),this.detailmapLine[this.i].render(),this.collection.maps.add(new r({mapLine:this.detailmapLine[this.i],mapLineName:"detail"+this.i,type:e||"custom"})),this.i++,this.i-1},showSecondRListHandle:function(e,t){var i=$(e.currentTarget),a=i.find(".list"),n=this.getMapLine(i).mapLine,n=this.collection.targetField.findWhere({api_name:n.getVal().target_field_api_name}),l=(this.trigger("hideList"),t?_.filter(n.get("enumItems"),function(e){return-1!==e.label.indexOf(t)}):n.get("enumItems")),i=i.find(".checked-list li"),o={};i.length&&_.each(i,function(e){o[$(e).data("value")]=$(e).data("name")}),l.length&&a.html(d(_.extend(n.toJSON(),{enumItems:l,checkObj:o}))).show(),e.stopImmediatePropagation()},showListHandle:function(e,t){var i,a,n,l,o,s;$(e.currentTarget).find(".disabled").length||(a=(i=$(e.currentTarget)).find(".list"),l=(o=(n=this.getMapLine(i)).mapLine.getVal()).type,s=this.getModel(o.source_field_api_name,"left"),o=this.getModel(o.target_field_api_name,"right"),s=s||o,this.trigger("hideList"),e.stopPropagation(),i.closest(".second-items-wrapper").length)||(o="lookup"==n.type?this.getCollectionByDom(i).getLookUpModelsByType(l,t,this.options.data.sourceObj.value):"object_reference"===l||"master_detail"===l||"object_reference_many"===l?this.getCollectionByDom(i).getLookUpModelsByType(l,t,s.get("target_api_name")):this.getCollectionByDom(i).getModelsByType(l,t),(e=_.map(o,function(e){return e.toJSON()})).length&&a.html(p({items:e})).show())},getCollectionByDom:function(e){e=$(e).closest(".inp-wrapper").hasClass("left-wrapper")?"sourceField":"targetField";return this.collection[e]},getModel:function(e,t,i){t=t&&(-1<t.indexOf("left")?"sourceField":"targetField"),i=$(i).closest(".inp-wrapper").hasClass("left-wrapper");return this.collection[t||(i?"sourceField":"targetField")].findWhere({api_name:e})},parseModel:function(e){var t=this,i=e.data.sourceFields,e=e.data.targetFields;_.each(i,function(e){t.collection.sourceField.add(new l({api_name:e.api_name,define_type:e.define_type,label:e.label,not_usable:!_.isUndefined(e.is_active)&&!e.is_active||!1,type:e.type,typeText:a.field.fieldInfo[e.type],enumItems:e.options,upload_limit:e.file_amount_limit,wrapperType:"left-wrapper",otherWrapperType:"right-wrapper",target_api_name:e.target_api_name}))}),_.each(e,function(e){t.collection.targetField.add(new l({api_name:e.api_name,define_type:e.define_type,label:e.label,not_usable:!_.isUndefined(e.is_active)&&!e.is_active||!1,type:e.type,typeText:a.field.fieldInfo[e.type],enumItems:t.addEmptyOptionByType(e.type,e.options),upload_limit:e.file_amount_limit,wrapperType:"right-wrapper",otherWrapperType:"left-wrapper",target_api_name:e.target_api_name}))})},addEmptyOptionByType:function(e,t){return _.contains(["select_one","select_many"],e)?t.concat([{label:$t("空"),value:"-1"}]):t},removeMapLine:function(e){var i,a,n,l,o=this;1==this.$(".detail").length?m.remind(3,$t("至少保留一条字段映射")):(e=$(e.currentTarget).siblings(".inp-wrapper").find("input"),i=this.getMapLine(e),a=e.eq(0).data("value"),n=e.eq(1).data("value"),a||n?l=m.confirm($t("删除映射规则后两个对象的字段将不会联动确认删除吗"),$t("提示"),function(){var e=o.getModel(a,"left"),t=o.getModel(n,"right");e&&e.set("checked",!1),t&&t.set("checked",!1),i.mapLine.destroy&&i.mapLine.destroy(),o.collection.maps.remove(i.mapLineModel),l.hide()}):(i.mapLine.destroy&&i.mapLine.destroy(),o.collection.maps.remove(i.mapLineModel)))},getMapLine:function(e){e=$(e).closest(".detail").data("name"),e=this.collection.maps.findWhere({mapLineName:e});return{mapLine:e&&e.get("mapLine"),mapLineModel:e,type:e.get("type")}},destroy:function(){var t=this;t.collection.maps.each(function(e){e.get("mapLine")&&e.get("mapLine").destroy&&e.get("mapLine").destroy(),t.collection.maps.remove(e)}),this.$el.remove(),this.$el=null}});i.exports=e});
define("crm-setting/objmap/action/components/detailmap/detailmapline",["./template/dmapline-tpl-html","./template/second-tpl-html","./template/lookup-tpl-html","../../config/nconfig"],function(e,t,i){var a=e("./template/dmapline-tpl-html"),l=e("./template/second-tpl-html"),n=e("./template/lookup-tpl-html"),p=e("../../config/nconfig"),e=Backbone.View.extend({opts:{el:"",isEdit:!1,type:"common",md_type:0},initialize:function(e){this.opts=$.extend(!0,{},this.opts,e),this.setElement(e.el)},render:function(){"lookup"==this.opts.type?this.$el.html(n({lookupTargetObj:this.opts.lookupTargetObj,toName:"",fromName:"",lookupSourceVal:""})):this.$el.html(a(this.opts))},events:{"click .checked-list .close":"removeCheckedItem"},setVal:function(i,e){var a=this,t=i.toJSON(),l=this.$(".inp-wrapper."+t.wrapperType).eq(0),n=l.siblings(".image-file-tip"),s=l.find("input").eq(0);this.removeError(s),this.addCheckedDom(l,_.extend({name:t.label,value:t.api_name},t)),l.find(".innertype").html(t.typeText),s.attr({placeholder:"","data-type":t.type,"data-value":t.api_name,upload_limit:t.upload_limit}).val(""),this.changeNotUseTip({isSecond:!1,showTip:t.not_usable,typeWrapper:t.wrapperType,visibleDom:l.siblings(".visible-tip-box-wrapper")}),this.changeImageFileTip(n,t),_.contains(p.secondType,t.type)&&("left-wrapper"===t.wrapperType?this.renderSelectTypeWrapper(this.$(".second-items-wrapper"),t):(e.noSetLike||this.renderLikeOptions(i),e.secondData&&_.each(e.secondData,function(e){var t=$('input[data-value="'+e.source_option+'"]',a.$el);t.length&&a.setSecondVal(i,e.source_option,e.target_option.split("|"),t.closest(".new-item"))}))),i.set("checked",!0)},changeImageFileTip:function(e,t){var i=this.getVal();+i.limitL&&+i.limitR&&+i.limitL>+i.limitR?(t="image"==t.type?$t("目标图片字段最大个数小于源图片字段最大个数只能映射前部分对应数量的图片")+";"+$t("无水印图片可映射到配置了有水印的图片字段中")+";":$t("目标附件字段最大个数小于源附件字段最大个数，只能映射前部分对应数量的附件"),$(e).html(t).removeClass("hide")):"image"==i.type?(t=$t("无水印图片可映射到配置了有水印的图片字段中"),$(e).html(t).removeClass("hide")):$(e).html("").addClass("hide")},changeNotUseTip:function(e){var t=e.visibleDom||this.$(".visible-tip-box-wrapper"),i=-1<e.typeWrapper.indexOf("right")?1:0,a=0==i?1:0,l=t.find(".visible-tip-box"),n=e.isSecond?$t("该选项已禁用或删除，对象映射时将不能生效"):$t("该字段已禁用或删除，对象映射时将不能生效");e.showTip?(l.eq(i).html(n).addClass("visible-tip"),t.removeClass("hide")):(l.eq(i).html("").removeClass("visible-tip"),l.eq(a).html()||t.addClass("hide"))},addCheckedDom:function(e,t){var i=_.escape(t.name),a=_.escape(t.value),e=$(e),l="<li class='innertxt' data-value='"+a+"' data-name='"+i+"'><span class='txt'>"+i+"</span><span class='close' >×</span></li>";1!=this.opts.md_type||"record_type"!=t.type||t.isSecond||(l="<li class='innertxt' data-value='"+a+"' data-name='"+i+"'><span class='txt'>"+i+"</span></li>"),"select_many"==t.type&&-1!=t.value?(e.find('.checked-list li[data-value="'+a+'"]').length||e.find(".checked-list").append(l),e.find('.checked-list li[data-value="-1"]').remove()):e.find(".checked-list").html(l)},setSecondVal:function(e,t,i,a){var l=this,n=e.toJSON(),s=$(a).find(".right-wrapper").eq(0),e=s.find("input").eq(0),p=(this.removeError(e),n.enumItems);s.find(".checked-list").html(""),_.each(i,function(e){e=p.length&&_.findWhere(p,{value:e.toString()});e&&(l.addCheckedDom(s,_.extend({name:e.label,value:e.value,type:n.type,isSecond:!0},e)),l.changeNotUseTip({isSecond:!0,showTip:e.not_usable,typeWrapper:s.attr("class"),visibleDom:$(a).find(".visible-tip-box-wrapper")}))}),e.attr({placeholder:"","data-type":n.type,"data-value":n.api_name}).val("")},getVal:function(){var e=this.$(".inp-wrapper input").eq(0),t=e&&e.attr("data-value"),i=this.$(".inp-wrapper input").eq(1),a=i&&i.attr("data-value");return{type:e.attr("data-type")||i.attr("data-type"),limitL:e.attr("upload_limit"),limitR:i.attr("upload_limit"),source_field_api_name:t,target_field_api_name:a,option_mapping:this.getSecondVal()}},getSecondVal:function(){var e=this.$(".second-items-wrapper").find(".inp-wrapper input"),i=[],a={},l=[];return _.each(e,function(e,t){t%2==0?(a.source_option=$(e).attr("data-value"),a.source_label=$(e).siblings(".checked-list").find("li").html().trim()):(t=$(e).siblings(".checked-list").find("li"),l=[],_.each(t,function(e){l.push($(e).attr("data-value"))}),a.target_option=l.join("|"),i.push(a),a={})}),i},getCollection:function(){var i=this,e=this.$(".inp-wrapper input");if("lookup"!=this.options.type||e.eq(0).attr("data-value")||e.eq(1).attr("data-value"))return _.each(e,function(e,t){$(e).attr("data-value")||i.setError(e,t)}),_.pick(this.getVal(),["source_field_api_name","target_field_api_name","option_mapping"])},setError:function(e,t){var i=$(e).closest(".inp-wrapper").siblings(".error-box"),t='<div class="'+("error"+(t%2?1:2))+'">'+$t("内容不完整")+"</div>";$(e).addClass("error-ipt"),i.html("").html(t)},removeError:function(e){e=$(e).closest(".item").find("input");_.each(e,function(e){$(e).removeClass("error-ipt"),$(e).closest(".inp-wrapper").siblings(".error-box").html(""),$(e).closest(".inp-wrapper").siblings(".image-file-tip").html("")})},renderLikeOptions:function(a){var l=this,e=this.getSecondVal(),n=a.toJSON().enumItems;_.each(e,function(t){var e,i=_.find(n,function(e){return e.label.trim()===t.source_label.trim()});i&&(e=$('input[data-value="'+t.source_option+'"]',l.$el)).length&&l.setSecondVal(a,t.source_option,_.isArray(i.value)?i.value:[i.value],e.closest(".new-item"))})},renderSelectTypeWrapper:function(e,t){$(e).html(l({_:_,list:{enumItems:t.enumItems,type:t.type,value:t.api_name}})),5<t.enumItems.length&&$(e).addClass("showScroll")},removeCheckedItem:function(e){var e=$(e.currentTarget),t=e.closest(".innertxt"),e=e.closest(".inp-wrapper"),i=e.siblings(".inp-wrapper"),a=e.hasClass("left-wrapper")?"left":"right",l=e.find(".innertype"),n=e.find("input"),s=e.closest(".second-items-wrapper").length,l=(this.removeError(n),s||this.trigger("changeModel",n.attr("data-value"),e.attr("class")),t.remove(),l.html("--"),!s&&_.contains(p.secondType,n.attr("data-type"))&&("left"==a&&(i.find(".checked-list .close").trigger("click"),this.$(".second-items-wrapper").html("").removeClass("showScroll")),t=this.$(".second-items-wrapper ."+a+"-wrapper .checked-list .close"),_.each(t,function(e){$(e).trigger("click")})),e.find(".checked-list li"));n.attr({placeholder:s?$t("请选择"):$t("请输入或请选择"),"data-type":"","data-value":l.length?$(l).attr("data-value"):"",upload_limit:""}),this.changeNotUseTip({isSecond:s,showTip:!1,typeWrapper:a,visibleDom:e.siblings(".visible-tip-box-wrapper")})},destroy:function(){this.$el.remove(),this.$el=null}});i.exports=e});
define("crm-setting/objmap/action/components/detailmap/fieldcollection",["./fieldmodel"],function(e,t,n){e=e("./fieldmodel");return Backbone.Collection.extend({Model:e,getModelsByType:function(t,n){var i,o;return t?this.filter(function(e){return i=e.toJSON(),o=n?-1!=i.label.indexOf(n):1,i.type==t&&0==i.checked&&o}):this.filter(function(e){return i=e.toJSON(),o=n?-1!=i.label.indexOf(n):1,0==i.checked&&o})},getLookUpModelsByType:function(t,n,i){var o,l,r;return t?this.filter(function(e){return o=e.toJSON(),l=o.target_api_name==i&&0==o.checked,r=n?-1!=e.label.indexOf(n):1,l&&o.type==t&&r}):this.filter(function(e){return o=e.toJSON(),l=o.target_api_name==i&&0==o.checked,r=n?-1!=e.label.indexOf(n):1,l&&r})}})});
define("crm-setting/objmap/action/components/detailmap/fieldmodel",[],function(e,a,t){var n=Backbone.Model.extend({defaults:{type:"",typeText:"",api_name:"",wrapperType:"",define_type:"",label:"",not_usable:!1,enumItems:[{value:"",label:"",not_usable:!1}],upload_limit:1,checked:!1,target_api_name:""}});t.exports=n});
define("crm-setting/objmap/action/components/detailmap/mapcollection",["./mapmodel"],function(e,o,n){e=e("./mapmodel"),e=Backbone.Collection.extend({Model:e});n.exports=e});
define("crm-setting/objmap/action/components/detailmap/mapmodel",[],function(e,n,o){var a=Backbone.Model.extend({mapLine:"",leftModel:"",rightModel:"",mapLineName:""});o.exports=a});
define("crm-setting/objmap/action/components/detailmap/template/dmapline-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="item new-item j-new-item"> <span class="icon j-icon ' + ((__t = type == "record_type" && md_type == 1 ? "hide" : "show") == null ? "" : __t) + '"></span> <span class="inp-wrapper left-wrapper"> <input class="b-g-ipt fm-ipt normal-ipt ipt " placeholder="' + ((__t = $t("请输入或选择字段")) == null ? "" : __t) + '" data-type="" data-value=""> <ul class="checked-list"></ul> <span class="list"></span> <div class="innertype">-- </div> </span> <span class="inp-wrapper right-wrapper"> <input class="b-g-ipt fm-ipt normal-ipt ipt" placeholder="' + ((__t = $t("请输入或选择字段")) == null ? "" : __t) + '" data-type="" data-value=""> <ul class="checked-list"></ul> <span class="list"></span> <div class="innertype">-- </div> </span> <div class="error-box"></div> <div class="second-items-wrapper"></div> <div class="visible-tip-box-wrapper clear-fix hide"> <div class="visible-tip-box"></div> <div class="visible-tip-box"></div> </div> <div class="image-file-tip hide"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/detailmap/template/edit-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="detail-map-title-wrapper j-detail-title"> <div class="icon"></div> <div class="detail-map-title">' + ((__t = $t("设置字段将")) == null ? "" : __t) + "" + ((__t = fromName) == null ? "" : __t) + " " + ((__t = $t("映射到")) == null ? "" : __t) + " " + ((__t = toName) == null ? "" : __t) + '</div> </div> <div class="edit-page"> <div class="con"> <div class="top clear"> ';
            var fromName_t = $t("{{fromName}}对象的字段", {
                fromName: fromName
            });
            var toName_t = $t("{{toName}}对象的字段", {
                toName: toName
            });
            __p += " <span>" + ((__t = fromName_t) == null ? "" : __t) + '</span><span class="tit2"></span><span class="tit3">' + ((__t = toName_t) == null ? "" : __t) + '</span> </div> <div class="context"> </div> </div> </div> <span class="j-add add">+' + ((__t = $t("继续添加")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/detailmap/template/list-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="f-g-options-wrapper theme-1" type="position:absolute;"> <ul class="g-select-options j-check crm-scroll" style="max-height:230px;"> ';
            for (var i = 0; i < items.length; i++) {
                __p += ' <li data-value="' + ((__t = items[i].api_name) == null ? "" : __t) + '" data-type="' + ((__t = items[i].type) == null ? "" : __t) + '" data-visible="' + ((__t = !items[i].not_usable) == null ? "" : __t) + '" data-wrapper="' + ((__t = items[i].wrapperType) == null ? "" : __t) + '">' + __e(items[i].label) + "</li> ";
            }
            __p += " </ul> </div>";
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/detailmap/template/lookup-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="item new-item j-new-item mn-checkbox-box"> <span class="icon lookup-icon mn-checkbox-item check-item j-lookup ' + ((__t = lookupTargetObj ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="inp-wrapper left-wrapper disable"><input class="b-g-ipt fm-ipt normal-ipt ipt _id" placeholder="' + ((__t = $t("本对象")) == null ? "" : __t) + '" data-type="object_reference" data-value="' + ((__t = lookupTargetObj ? "_id" : "") == null ? "" : __t) + '"> <ul class="checked-list"></ul></input> <div class="disabled"></div> </span> <span class="inp-wrapper right-wrapper lookup"> <input class="b-g-ipt fm-ipt normal-ipt ipt" placeholder="' + ((__t = lookupTargetObj ? "" : $t("请输入或选择字段")) == null ? "" : __t) + '" data-type="' + ((__t = lookupTargetObj ? lookupTargetObj.type : "") == null ? "" : __t) + '" data-value="' + ((__t = lookupTargetObj ? lookupTargetObj.api_name : "") == null ? "" : __t) + '"> <ul class="checked-list"> ';
            if (lookupTargetObj) {
                __p += ' <li class="innertxt">' + __e(lookupTargetObj.label) + ' <span class="close">×</span> </li> ';
            }
            __p += ' </input> </ul> <span class="list"></span> ';
            if (lookupTargetObj) {
                __p += ' <div class="innertype">' + ((__t = $t("查找关联")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="innertype">-- </div> <div class="disabled"></div> ';
            }
            __p += ' </span> <div class=\'error-box\'></div> <div class="second-items-wrapper"></div> <div class="visible-tip-box-wrapper clear-fix hide"> <div class="visible-tip-box "></div> <div class="visible-tip-box"></div> </div> ';
            var toName_fromName_t = $t("由于{{toName}}中存在查找关联字段关联了{{fromName}}选择是否将本对象映射到目标的查找关联字段", {
                toName: toName,
                fromName: fromName
            });
            __p += ' <div class="lookup-tip">' + ((__t = toName_fromName_t) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/detailmap/template/second-list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="f-g-options-wrapper theme-1" type="position:absolute;" style="z-index:10000"> <ul class="g-select-options j-check mn-checkbox-box" style="max-height:230px;" data-value="' + ((__t = api_name) == null ? "" : __t) + '" data-type="' + ((__t = type) == null ? "" : __t) + '"> ';
            for (var i = 0; i < enumItems.length; i++) {
                __p += ' <li class="j-select-option ' + ((__t = checkObj[enumItems[i].value] ? "selected" : "") == null ? "" : __t) + '" data-name="' + ((__t = enumItems[i].label) == null ? "" : __t) + '" data-value="' + ((__t = enumItems[i].value) == null ? "" : __t) + '" data-type="' + ((__t = type) == null ? "" : __t) + '" isDeleted="' + ((__t = enumItems[i].IsDeleted) == null ? "" : __t) + '" data-visible="' + ((__t = enumItems[i].not_usable) == null ? "" : __t) + '"><span class="' + ((__t = type == "select_many" ? "mn-checkbox-item check-item" : "ratio-check-icon") == null ? "" : __t) + " " + ((__t = checkObj[enumItems[i].value] && (type == "select_many" ? "mn-selected" : "ratio-checked")) == null ? "" : __t) + '"></span>' + __e(enumItems[i].label) + "</li> ";
            }
            __p += " </ul> </div>";
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/detailmap/template/second-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            for (var i = 0; i < list.enumItems.length; i++) {
                __p += ' <div class="item new-item j-new-item" data-type="' + ((__t = list.type) == null ? "" : __t) + '" data-value="' + ((__t = list.value) == null ? "" : __t) + '" > <!--<span class="icon j-icon"></span>--> <span class="inp-wrapper left-wrapper"> <input class="fm-ipt normal-ipt ipt" placeholder="" data-value="' + ((__t = list.enumItems[i].value) == null ? "" : __t) + '"> <ul class="checked-list"> <li class="innertxt">' + __e(list.enumItems[i].label) + '</li> </ul> <span class="list" style="display:none"></span> </span> <span class="inp-wrapper right-wrapper special"> <input class=" fm-ipt normal-ipt ipt b-g-ipt" placeholder="' + ((__t = $t("请选择")) == null ? "" : __t) + '" data-type="" > <ul class="checked-list"></ul> <span class="list" style="display:none"></span><span class="right-border"><i></i></span> </span> <div class="error-box"></div> <div class="visible-tip-box-wrapper clear-fix ' + ((__t = list.enumItems[i].not_usable ? "" : "hide") == null ? "" : __t) + '"> <div class="visible-tip-box ' + ((__t = list.enumItems[i].not_usable ? "visible-tip" : "") == null ? "" : __t) + '">' + ((__t = !_.isUndefined(list.enumItems[i].not_usable) && list.enumItems[i].not_usable ? $t("该选项已禁用或删除，对象映射时将不能生效") : "") == null ? "" : __t) + '</div> <div class="visible-tip-box"></div> </div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/map/collection",["./model"],function(e,o,n){e=e("./model");return Backbone.Collection.extend({Model:e})});
define("crm-setting/objmap/action/components/map/map",["../template/map-tpl-html","../mapline/mapline","../../fetch","../detailmap/detailmap","crm-modules/common/util","crm-modules/action/field/field","./model","./collection","../template/naviagtion-html"],function(e,t,i){var a=e("../template/map-tpl-html"),s=e("../mapline/mapline"),o=e("../../fetch"),l=e("../detailmap/detailmap"),p=e("crm-modules/common/util"),n=e("crm-modules/action/field/field").components.Base,r=e("./model"),c=e("./collection"),u=e("../template/naviagtion-html"),e=n.extend({i:0,initialize:function(e){n.prototype.initialize.call(this,arguments),this.widgets={},this.widgets.sub={},this.widgets.detail=[],this.widgets.main={},this.ajaxs={},this.collection=new c},events:{"click .j-add-submap":"_addSubMapLine","click .j-del-submap":"_delSubMapHandle","click .j-detail-title":"_showDetailMap"},render:function(){var t=this;this.renderNavigation(),this.$el.html(a()),this.initMainLine(function(){t.listenTo(t.collection,{resetSubModelsOptions:t.resetSubModelsOptions}),setTimeout(function(){var e;t.model.get("isEdit")&&(e=(e=t.model.get("data"))&&e.map_object,t.setData(e))},2e3)}),this.$el.closest(".crm-c-dialog").on("click",$.proxy(this.removeList,this))},removeList:function(e){this.$(".list").hide()},btnEnable:function(e){$(".crm-action-field-full .full-header .crm-btn[data-action="+e+"]").removeAttr("disabled")},setData:function(e){var t=this,i=_.find(e,function(e){return _.isEmpty(e.master_api_name)}),a=t.collection.findWhere({type:"main"});t.widgets.main.mainLine.setValue({sourceData:{value:t.model.get("source_api_name"),name:t.model.get("source_display_name")},targetData:{value:t.model.get("target_api_name"),name:t.model.get("target_display_name")}}),a.set("map_object",i),2<=e.length?setTimeout(function(){t.setSubMapLine(e)},2e3):t.btnEnable("next")},setSubMapLine:function(e){var i=this,a=_.filter(e,function(e){return e.master_api_name}),n=i.$(".j-add-submap");setTimeout(function(){_.each(a,function(e){n.trigger("click");var t="subLine"+(i.i-1),t=i.collection.findWhere({mapLineName:t}),e=((t&&t.get("mapLine")).setValue({sourceData:{value:e.source_api_name},targetData:{value:e.target_api_name}}),_.findWhere(a,{source_api_name:e.source_api_name,target_api_name:e.target_api_name}));t.set("map_object",e)})},2e3)},resetSubModelsOptions:function(){var a,n,e=this.collection.where({type:"sub"}),s=this.collection.findWhere({type:"main"}),o=[],r=[];_.each(e,function(e){e.get("target_api_name")&&o.push(e.get("target_api_name")),e.get("source_api_name")&&r.push(e.get("source_api_name"))}),_.each(e,function(e){var t=e.get("target_api_name"),i=e.get("source_api_name"),t=(a=_.without(o,t),n=_.without(r,i),_.filter(s.get("subTargetOptions"),function(e){return!_.contains(a,e.value)})),i=_.filter(s.get("subSourceOptions"),function(e){return!_.contains(n,e.value)});e.set("targetOptions",t),e.set("sourceOptions",i),e.get("mapLine").widgets&&e.get("mapLine").widgets.rigSelect.resetOptions(t,!0),e.get("mapLine").widgets&&e.get("mapLine").widgets.lefSelect.resetOptions(i,!0)})},renderMainDetailMap:function(e,t,i){"main"===e.get("type")&&(e.get("hasMD")?this._showAddSubLineTip():(this._hideAddSubLineTip(),this._hideSubMapTip())),this._renderDetail(e)},_showDetailMap:function(e){e=$(e.currentTarget);e.toggleClass("actived"),e.siblings().toggleClass("hide")},_addSubMapLine:function(){$(".sub-line-wrapper",this.$el).removeClass("hide"),this._initSubMapLine()},_delSubMapHandle:function(e){var e=$(e.currentTarget).data("key"),t=this.collection.findWhere({mapLineName:e}),i=t.get("mapLine");i&&i.destroy&&i.destroy(),this.$("."+e+"-wrapper").remove(),t.trigger("remove.subDetailMap",t),this.collection.remove(t),this.collection.trigger("resetSubModelsOptions")},_initSubMapLine:function(){var e=this.createSubMapLine();this.createSubModel(e),this.i++},createSubModel:function(e){e=new r({type:"sub",isEdit:this.options.model.get("isEdit"),mapLine:e,mapLineName:e.options.name});return this.listenToSubModel(e),this.collection.add(e),e},listenToSubModel:function(e){this.listenTo(e,"change:target_api_name",this.resetOtherSubTargetObj),this.listenTo(e,"remove.subDetailMap",this.removeSubDetailMap)},resetOtherSubTargetObj:function(t,e){var i,a=this.collection.findWhere({type:"main"}),n=this.collection.where({type:"sub"}),s=[];_.each(n,function(e){e.get("target_api_name")&&s.push(e.get("target_api_name"))}),n=_.filter(n,function(e){return e.get("value")!=t.get("value")}),_.each(n,function(e){var t=e.get("target_api_name"),t=(i=_.without(s,t),_.filter(a.get("subTargetOptions"),function(e){return!_.contains(i,e.value)}));e.set("targetFieldOptions",t),e.get("mapLine").widgets&&e.get("mapLine").widgets.rigSelect.resetOptions(t,!0)})},caculateSubTargetOptions:function(){var t,e=this.collection.where({type:"sub"}),i=this.collection.findWhere({type:"main"});return e.length?(t=[],_.each(e,function(e){e.get("target_api_name")&&t.push(e.get("target_api_name"))}),_.filter(i.get("subTargetOptions"),function(e){return!_.contains(t,e.value)})):i.get("subTargetOptions")||[]},createSubMapLine:function(){var a=this,e="subLine"+a.i,t=this.collection.findWhere({type:"main"});if(!a.widgets.sub[e])return a.widgets.sub[e]=new s({isMainLine:!1,className:"crm-c-sub-mapline "+e+"-wrapper",zIndex:1e3,sourceOptions:t.get("subSourceOptions"),targetOptions:a.caculateSubTargetOptions(),type:"sub",name:e,defaultVal:""}),a.widgets.sub[e].on("showDetailMap",function(e){var i=a.collection.findWhere({mapLineName:this.options.name}),t=a.collection.findWhere({type:"main"});i.set({source_api_name:e.sourceObj.value,target_api_name:e.targetObj.value,master_api_name:t&&t.get("sourceObj").value,sourceObj:e.sourceObj,targetObj:e.targetObj,value:e.sourceObj.value+e.targetObj.value,mapLine:this,mapLineName:this.options.name}),i._getAllFieldsByApiName(i,"sub",function(e,t){a._renderDetail(i),$(".sub-detail-wrapper",a.$el).removeClass("hide"),a.btnEnable("next")})}),a.widgets.sub[e].on("reset",function(e){var t=a.collection.findWhere({mapLineName:this.options.name});t&&t.trigger("remove.subDetailMap",t)}),a.createSubLineWrapper(a.widgets.sub[e]),a.widgets.sub[e]},createSubLineWrapper:function(e){this.$(".sub-line-wrapper").append(e.$el),e.$el.append('<div class="del-sub-map j-del-submap " data-key="subLine'+this.i+'"></div>')},removeSubDetailMap:function(e){var t;e.get("detailMap")&&((t=e.get("detailMap")).destroy&&t.destroy(),t.off(),t.$el=null,e.set("detailMap",""),delete this.widgets.detail[e.get("sourceObj").value+e.get("targetObj").value])},createMainModel:function(e){e=new r({type:"main",isEdit:this.options.model.get("isEdit"),mapLine:e,mapLineName:e.options.name});return this.listenToMainModel(e),this.collection.add(e),e},initMainLine:function(i){var n=this;o.getSourceObjList(n.options.model,function(t){o.getTargetObjList(n.options.model,function(e){n.widgets.main.mainLine=new s({el:$(".main-wrapper .main-line",n.$el),isMainLine:!0,sourceOptions:t,targetOptions:e,type:"main",zIndex:1e3,name:"mainLine",isEdit:n.model.get("isEdit")}),n.createMainModel(n.widgets.main.mainLine),n.widgets.main.mainLine.on("showDetailMap",function(t){o.checkBtnOrMapRuleNumber(n.options.model.get("isEdit"),t,function(e){var a;e&&((a=n.collection.findWhere({type:"main"})).set({source_api_name:t.sourceObj.value,target_api_name:t.targetObj.value,sourceObj:t.sourceObj,targetObj:t.targetObj,value:t.sourceObj.value+t.targetObj.value,isEdit:n.options.model.get("isEdit"),mapLine:n.widgets.main.mainLine,mapLineName:n.widgets.main.mainLine.options.name}),a.checkMD(a,"main",function(e,t,i){n._hideError(),e?(a.set({subSourceOptions:t,subTargetOptions:i}),a.set("hasMD",!0)):a.set("hasMD",!1),n.renderMainDetailMap(a,a.collection)}))})}),i&&i(),n.widgets.main.mainLine.on("reset",function(e){var t=n.collection.findWhere({type:"main"});t&&t.trigger("remove.allDetailMap",t)}),n.widgets.main.mainLine.on("change.lefSelect",function(e,t,i){var a=n.collection.findWhere({type:"main"});a&&a.set({source_api_name:e,target_api_name:"",sourceObj:i,targetObj:""})}),n.widgets.main.mainLine.on("change.rigSelect",function(e,t,i){var a=n.collection.findWhere({type:"main"});a&&a.set({target_api_name:e,targetObj:i.targetObj})})})})},listenToMainModel:function(e){this.listenTo(e,"remove.allDetailMap",this.removeMainDetailMap)},removeMainDetailMap:function(e,t){e=e.collection.models;_.each(e,function(e){var t=e.get("detailMap");t&&(t.destroy&&t.destroy(),t.off(),t.$el=null,e.set("detailMap",""))}),this._hideSubMapTip(),this._hideAddSubLineTip()},getLookUpData:function(t){var e,i=t.get("targetFieldOptions"),i=_.filter(i,function(e){return"object_reference"===e.type&&t.get("sourceObj").value===e.target_api_name});if(i.length)return e=(e=t.get("map_object")&&_.findWhere(t.get("map_object").field_mapping,{source_field_api_name:"_id"}))&&_.findWhere(t.get("targetFieldOptions"),{api_name:e.target_field_api_name}),{hasLookUp:i.length,map_data:{_id:_.findWhere(t.get("sourceFieldOptions"),{api_name:"_id"}),targetLookUpObj:e}}},_renderDetail:function(e){var t=this,i=e.get("sourceObj"),a=e.get("targetObj"),n=e.get("sourceFieldOptions"),s=e.get("targetFieldOptions"),o=i.value+a.value,r=e.get("map_object")||{};e.get("detailMap")&&e.get("detailMap").$el.remove(),t._createDetailWrapper(e),t.widgets.detail[o]=new l({el:$("."+o,t.$el),data:{sourceObj:i,targetObj:a,sourceFields:n,targetFields:s,ruleList:r,rule_api_name:_.isEmpty(r)?"map_"+p.getUUId(5)+"__c":r.rule_api_name,master_api_name:e.get("master_api_name"),define_type:_.isEmpty(r)?"custom":r.define_type,lookUpData:t.getLookUpData(e)||{},rule_name:i.name+$t("转换为")+("SalesOrderProductObj"==a.value?$t("订单明细"):a.name),isEdit:t.model.get("isEdit")}}),t.widgets.detail[o].render(),t.widgets.detail[o].on("hideList",t.removeList),e.set("detailMap",t.widgets.detail[o])},_createDetailWrapper:function(e){var t=this._createDOM(e);("main"===e.get("type")?this.$(".main-detail-wrapper .detail-line-wrapper"):this.$(".sub-detail-wrapper .detail-line-wrapper")).append(t)},getValue:function(){var t=[];return _.each(this.collection.models,function(e){e.get("detailMap")&&t.push(e.get("detailMap").getData())}),t},_createDOM:function(e){var t=e.get("sourceObj").value+e.get("targetObj").value;return'<div class="detail-map-wrapper '+t+'" data-apiname="'+t+'" data-type="'+e.get("type")+'"></div>'},_showAddSubLineTip:function(){var e=this.collection.findWhere({type:"main"});$(".sub-wrapper").removeClass("hide"),$(".hassub-tip",this.$el).html(e.get("sourceObj").name+" "+$t("和")+" "+e.get("targetObj").name+" "+$t("都包含了从对象,可以添加从对象的映射关系")),$(".add-map",this.$el).removeClass("hide")},_hideAddSubLineTip:function(e){$(".sub-wrapper").addClass("hide"),$(".hassub-tip",this.$el).html(""),$(".add-map",this.$el).addClass("hide")},_hideError:function(){this.hideError(null)},_hideSubMapTip:function(){$(".hassub-tip",this.$el).html(""),$(".add-map",this.$el).addClass("hide");var e=$(".sub-line-wrapper .j-del-submap",this.$el);_.each(e,function(e){$(e).trigger("click",!0)}),$(".sub-line-wrapper",this.$el).html("")},renderNavigation:function(){$(".crm-a-objmap .btn-group").before(u({hasNextShep:this.model.get("NextView")})),$(".crm-a-objmap .crm-btn-primary").on("showstep",$.proxy(this._showStepHandle)),$('.crm-a-objmap .crm-btn-primary[data-action="pre"]').on("click.showstep",$.proxy(this._showStepHandle,this))},_showStepHandle:function(e){e=$(e.currentTarget).data("action");this.$(".fm-error").length||this.$(".error1").length||this.$(".error2").length||($(".crm-a-objmap .navigation-wrapper .step").removeClass("actived"),$(".crm-a-objmap .navigation-wrapper .step."+e).addClass("actived"))},destroy:function(){_.each(this.widgets,function(e){e&&e.destory&&e.destory()}),_.each(this.widgets.detail,function(e){e&&e.destory&&e.destory()}),this.$el.remove(),this.$el=null}});i.exports=e});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,i=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)),i}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/objmap/action/components/map/model",["../../fetch"],function(e,t,r){var l=e("../../fetch"),e=Backbone.Model.extend({defaults:{source_api_name:"",target_api_name:"",master_api_name:"",sourceObj:{},targetObj:{},sourceFieldOptions:[],targetFieldOptions:[],type:"main",value:"sourceApiName2targetApiName",mapLine:"",mapLineName:"",detailMap:"",subSourceOptions:[],subTargetOptions:[]},_getAllFieldsByApiName:function(r,i,n){var o=this,a=r.get("sourceObj").value,s=r.get("targetObj").value;$.when(l.getAllFieldsByApiName(a),l.getAllFieldsByApiName(s)).done(function(e,t){"success"==e[1]&&"success"==t[1]&&(0==e[0].Result.StatusCode&&0==t[0].Result.StatusCode?(o.set("sourceFieldOptions",l.filterFields(r.get("isEdit"),a,e[0].Value.objectDescribe.fields,i,"source")),o.set("targetFieldOptions",l.filterFields(r.get("isEdit"),s,t[0].Value.objectDescribe.fields,i,"target")),n&&n(e[0].Value,t[0].Value)):CRM.util.alert(e[0].Result.FailureMessage||t[0].Result.FailureMessage))})},_getAllMDFieldsByApiName:function(e,t,r){var i=e.get("sourceObj").value,e=e.get("targetObj").value;$.when(l.getAllMDFieldsByApiName(i),l.getAllMDFieldsByApiName(e)).done(function(e,t){"success"==e[1]&&"success"==t[1]&&(0==e[0].Result.StatusCode&&0==t[0].Result.StatusCode?r&&r(e[0].Value.detailDescribeList,t[0].Value.detailDescribeList):CRM.util.alert(e[0].Result.FailureMessage))})},filterWhenMasterCreateMD:function(e){return null==e||null==(e=e.map(function(e){return _objectSpread(_objectSpread({},e),{},{isCreateWhenMasterCreate:null==(e=_.findWhere(e.fields,{type:"master_detail"}))?void 0:e.is_create_when_master_create})}))?void 0:e.filter(function(e){return e.isCreateWhenMasterCreate})},checkMD:function(e,t,r){var i=this;i._getAllFieldsByApiName(e,"main",function(){i._getAllMDFieldsByApiName(e,"main",function(e,t){e=l._formatData(!1,i.filterWhenMasterCreateMD(e),"display_name","api_name","source"),t=l._formatData(!1,i.filterWhenMasterCreateMD(t),"display_name","api_name","target");e.length&&t.length?r&&r(!0,e,t):r&&r(!1)})})}});r.exports=e});
define("crm-setting/objmap/action/components/mapline/mapline",["crm-widget/select/select","./template/line-tpl-html","crm-modules/common/util"],function(e,t,i){var s=e("crm-widget/select/select"),n=e("./template/line-tpl-html"),l=e("crm-modules/common/util"),e=Backbone.View.extend({opts:{zIndex:200,className:"",isMainLine:!0,sourceOptions:[],targetOptions:[],defaultVal:{},type:"",isEdit:!1},initialize:function(e){this.options=$.extend(!0,{},this.opts,e),this.widgets={},this.$el.html(n()),this.$el.addClass(this.options.className),this.$el.addClass("crm-c-mapline clearfix"),this._addDefaultOps(),this.render()},render:function(){var i=this;this.widgets.lefSelect=new s({$wrap:$(".left-wrapper",this.$el),width:"100%",zIndex:+i.options.zIndex+10,multiple:"single",stopPropagation:!0,options:i.options.sourceOptions||[],defaultVal:0,appendBody:!1,disabled:i.options.isEdit}),this.widgets.lefSelect.on("change",function(e,t){i.widgets.rigSelect.getValue()&&(i.trigger("reset",!1),i.trigger("change.lefSelect",e,i.widgets.rigSelect.getValue(),t),i.widgets.rigSelect.setValue(0))}),this.widgets.rigSelect=new s({$wrap:$(".right-wrapper",this.$el),width:this.options.isMainLine?370:330,zIndex:+i.options.zIndex+10,multiple:"single",options:i.options.targetOptions||[],stopPropagation:!0,defaultVal:0,appendBody:!1,disabled:i.options.isEdit}),this.widgets.rigSelect.on("change",function(e,t){i.widgets.lefSelect.getValue()?e&&i.widgets.lefSelect.getValue()&&("main"==i.options.type&&i.widgets.lefSelect.getValue()==e?(l.alert($t("目的对象和源对象不能相同")),$t("请重新选择"),i.widgets.rigSelect.setValue(0),this.beforeRightVal&&i.trigger("reset",this.beforeRightVal)):(this.beforeRightVal&&i.trigger("reset",this.beforeRightVal),i.trigger("change.rigSelect",i.widgets.rigSelect.getValue(),this.beforeRightVal,i.getValue()),i.getValue().sourceObj&&i.trigger("showDetailMap",i.getValue()),this.beforeRightVal=e)):(l.alert($t("请先选择源对象")),i.widgets.rigSelect.setValue(0))})},_addDefaultOps:function(){var e=this;_.findWhere(e.options.sourceOptions,{value:0,name:$t("请选择")})||e.options.sourceOptions&&e.options.sourceOptions.unshift({name:$t("请选择"),value:0}),_.findWhere(e.options.targetOptions,{value:0,name:$t("请选择")})||e.options.targetOptions&&e.options.targetOptions.unshift({name:$t("请选择"),value:0})},setTargetOptions:function(t){var e=_.filter(this.options.targetOptions,function(e){return e.value!=t});this.widgets.rigSelect.resetOptions(e,!0)},_filterLeftVal:function(t){this.widgets.rigSelect.resetOptions(_.filter(this.options.targetOptions,function(e){return e.value!=t})),this.widgets.rigSelect.setValue()},getValue:function(){var e={};return e.sourceObj=_.findWhere(this.options.sourceOptions,{value:this.widgets.lefSelect.getValue()}),e.targetObj=_.findWhere(this.options.targetOptions,{value:this.widgets.rigSelect.getValue()}),e.type="main"==this.options.type?"main":"sub",e},setValue:function(e){this.widgets.lefSelect.setValue(e.sourceData.value,1),this.widgets.rigSelect.setValue(e.targetData.value,1)},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()})}});i.exports=e});
define("crm-setting/objmap/action/components/mapline/template/line-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="half left-wrapper"> <div class="left-select"></div> </div> <div class="mapto-icon"><span class="mapto-text">' + ((__t = $t("映射到")) == null ? "" : __t) + '</span></div> <div class="half right-wrapper"> <div class="right-select"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/template/map-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="map-wrapper"> <div class="main-wrapper"> <div class="title-wrapper"> <div class="title"><em class="must-icon">*</em>' + ((__t = $t("paas.crm.objmap.sourceobject", null, "源对象")) == null ? "" : __t) + '</div> <div class="to">' + ((__t = $t("映射到")) == null ? "" : __t) + '</div> <div class="title">' + ((__t = $t("目标对象")) == null ? "" : __t) + '</div> </div> <div class="main-line"></div> </div> <div class="main-detail-wrapper"> <!-- <div class="detail-title">设置字段的映射关系</div> --> <div class="detail-title-select"></div> <div class="detail-line-wrapper"> <!-- <div class="detail-map"></div> --> </div> </div> <div class="sub-wrapper hide"> <div class="sub-title detail-title"> ' + ((__t = $t("从对象的映射")) == null ? "" : __t) + ' <span data-title="' + ((__t = $t("paas.convertrule.disallow.noIndependent.childCreation", null, "不支持选择主从不一起新建的从对象")) == null ? "" : __t) + '" data-pos="top" class="fx-icon-question crm-ui-title"></span> </div> <div class="hassub-tip"></div> <div class="sub-line-wrapper hide"> <div class="title-wrapper "> <div class="title"><em class="must-icon">*</em>' + ((__t = $t("paas.crm.objmap.sourceobject", null, "源对象")) == null ? "" : __t) + '</div> <div class="to">' + ((__t = $t("映射到")) == null ? "" : __t) + '</div> <div class="title">' + ((__t = $t("目标对象")) == null ? "" : __t) + '</div> </div> </div> <div class="j-add-submap add-map hide">+' + ((__t = $t("添加从对象映射关系")) == null ? "" : __t) + '</div> </div> <div class="sub-detail-wrapper hide"> <!-- <div class="detail-title">设置字段的映射关系</div> --> <div class="detail-title-select"></div> <div class="detail-line-wrapper"> <!-- <div class="detail-map"></div> --> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/objmap/action/components/template/naviagtion-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="navigation-wrapper"> <span class="step actived pre" style="font-size:16px;" > <span class="step-icon">1</span> <span>' + ((__t = $t("配置映射规则")) == null ? "" : __t) + "</span> </span> ";
            if (hasNextShep) {
                __p += ' <span class="circle-wrapper"> <span class="circle"></span> <span class="circle"></span> </span> <span class="step next" style="font-size:16px;"> <span class="step-icon">2</span> <span>' + ((__t = $t("设置对应按钮")) == null ? "" : __t) + "</span> </span> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/objmap/action/config/_actionObj",[],function(t,e,o){o.exports={sourceApiNames:["LeadsObj","AccountObj","ContactObj","OpportunityObj","SalesOrderObj","SalesOrderProductObj","ReturnedGoodsInvoiceObj","ReturnedGoodsInvoiceProductObj","VisitingObj","RefundObj","InvoiceApplicationObj","ContractObj","ProductObj","MarketingEventObj","PartnerObj","CompetitorObj","CompetitiveProductsObj","CompetitiveLinesObj","ProcurementAccountObj","ProcurementLogObj","WechatFriendsRecordObj","ContactEvaluationObj","DistributorAuthorizationAgreementObj","AuthorizationAgreementLinesObj"]}});
define("crm-setting/objmap/action/config/_fieldConfig",[],function(e,t,a){var r={text:$t("单行文本"),long_text:$t("多行文本"),select_one:$t("单选"),select_many:$t("多选"),number:$t("数字"),currency:$t("金额"),date:$t("日期"),time:$t("时间"),date_time:$t("日期时间"),phone_number:$t("手机"),email:$t("邮箱"),true_or_false:$t("布尔值"),percentile:$t("百分数"),url:$t("网址"),object_reference:$t("查找关联"),master_detail:$t("主从关系"),employee:$t("crm.人员"),location:$t("定位"),record_type:$t("业务类型"),department:$t("crm.部门"),country:$t("国家"),province:$t("省"),city:$t("市"),district:$t("区"),town:$t("乡镇"),village:$t("village"),address:$t("详细地址"),image:$t("图片"),file_attachment:$t("附件")};a.exports={fieldInfo:r,groups:{area:["area_country","area_province","area_city","area_district","area_town","area_village","area_detail_address","area_location"],date_time_range:["end_time_field","start_time_field"]},blackType:["formula","auto_number","signature","quote","embedded_object_list","multi_level_select_one","count","out_department"],fieldChange:{BizQueryObj:{Scope:"long_text"}},whiteApi:["owner","name","out_owner","mc_currency","public_data_type"],whiteType:["record_type","image","file_attachment"]}});
define("crm-setting/objmap/action/config/_fieldwhite",[],function(e,t,_){_.exports={PaymentPlanObj:["plan_payment_method","plan_payment_amount","order_id","remark","mc_exchange_rate","account_id","plan_payment_time","remind_time","partner_id"],AccountObj:["owner","account_no","record_type","account_no","account_type","account_level","account_source","order_id","tel","fax","remark","url","email","name","area_location","partner_id","uniform_social_credit_code","enterprise_id","biz_reg_name","enterprise_wechat_user_id","industry_level1","industry_level2","enterprise_type","legal_representative","registered_capital","registration_status","business_scope","external_user_id","erp_code","parent_account_id","data_own_organization","account_class_category","bl_expiry_date","applicant_name"],OpportunityObj:["account_id","order_id","sales_process_name","name","lost_reason","expected_deal_closed_date","expected_deal_amount","remark","product_line","owner","sales_stg_changed_time","probability","partner_id"],ContactObj:["owner","account_id","order_id","name","company","department","job_title","primary_contact","owned_partner_id","email","add","date_of_birth","gender","introducer","remark","mobile1","tel1","enterprise_wechat_user_id","source","partner_id","external_user_id","kc","customer_relationship_hierarchy","change_adaptability","customer_relationship_objectives","impact","influence_level","contact_competitor_strength","contact_our_strength","change_adaptability"],LeadsObj:["name","order_id","company","source","marketing_event_id","leads_pool_id","remark","department","tel","job_title","mobile","url","email","address","picture_path","partner_id","close_reason","enterprise_wechat_user_id","enterprise_id","biz_reg_name","promotion_channel","industry_level1","industry_level2","wechat_id","external_user_id","owner","data_own_organization"],MarketingEventObj:["name","account_id","order_id","begin_time","end_time","event_type","location","expected_cost","actual_cost","expected_income","actual_income","marketing_plan","execution_desc","summary","effect","desc","partner_id"],ContractObj:["order_id","account_id","contract_title","started_time","expired_time","contract_amount","owner","partner_id"],SalesOrderObj:["record_type","order_id","product_amount","account_id","opportunity_id","new_opportunity_id","order_time","discount","order_amount","delivery_comment","confirmed_receive_date","ship_to_id","ship_to_tel","ship_to_add","delivery_date","receipt_type","commision_info","remark","confirmed_delivery_date","resource","price_book_id","quote_id","promotion_id","confirmed_receive_date","owner","partner_id","shipping_warehouse_id","mc_currency","mc_exchange_rate","sale_contract_id","dynamic_amount","policy_total","policy_discount","price_policy_id","price_policy_rule_ids","policy_dynamic_amount"],PaymentObj:["account_id","order_id","payment_time","payment_id","payment_amount","payment_term","remark","notification_time","owner","amount","purpose","pay_type","enter_into_account","fund_account_id","partner_id","received_payment_id"],OrderPaymentObj:["account_id","order_id","used_date","payment_amount","payment_plan_id","attachment","remark","partner_id"],RefundObj:["account_id","order_id","refunded_time","refunded_amount","refunded_method","remark","owner","partner_id"],InvoiceApplicationObj:["invoice_type","partner_id","account_id","order_id","invoice_date","name","invoice_applied_amount","invoice_type","remark","owner","invoice_no","contact_add","recipient","contact_tel","invoice_no","title_type","invoice_title","tax_id","account_bank","account_bank_no","invoice_add","invoice_tel","owner","partner_id"],InvoiceApplicationLinesObj:["name","product_id","invoiced_quantity","order_id","order_product_id","sales_price","invoiced_amount","tax_rate","invoice_date"],PriceBookObj:["name","order_id","is_standard","record_type","remark","owner","price_book_product_id","start_date","end_date","account_range"],PriceBookProductObj:["pricebook_id","product_id","discount","record_type","teamMemberRole","order_id","owner","pricebook_sellingprice"],QuoteLinesObj:["product_id","price_book_product_id","price","discount","sales_price","quantity","total_amount","quote_id","owner","quote_lines_unit","order_id","quote_lines_specs","extra_discount","selling_price","total_discount","total_discount_amount","sales_amount","sub_product_id","prod_pkg_key","parent_prod_pkg_key","bom_id","root_prod_pkg_key","price_book_id","parent_prod_package_id","attribute","nonstandard_attribute","attribute_json","attribute_price_book_id","mc_currency","mc_exchange_rate","actual_unit","node_type","temp_node_bom_id","temp_node_group_id","node_no","policy_dynamic_amount","policy_price","policy_subtotal","policy_discount","dynamic_amount","gift_amortize_price","gift_amortize_subtotal","amortize_amount","amortize_subtotal","group_key","node_price","node_discount","share_rate","node_subtotal","base_unit_count","stat_unit_count","conversion_ratio","other_unit","other_unit_quantity","bom_core_id","bom_version","bom_type","related_core_id","new_bom_path","is_giveaway","gift_type","price_policy_id","price_policy_rule_ids","group_key","parent_gift_key","policy_dynamic_amount","amortize_amount","gift_amortize_price","gift_amortize_subtotal","service_start_time","price_per_set","pricing_cycle","settlement_mode","whole_period_sale","settlement_cycle","settlement_amount_per_period","settlement_period","pricing_period","pricing_rate","settlement_rate","pricing_mode","service_end_time","price_tiered_record","discount","sales_price","total_amount","non_standard_pro_description","detail_type"],QuoteObj:["account_id","opportunity_id","quote_time","price_book_id","quote_discount","quote_amount","owner","order_id","new_opportunity_id","name","mc_currency","mc_exchange_rate","partner_id","price_book_amount","dynamic_amount","policy_total","policy_discount","price_policy_id","price_policy_rule_ids","policy_dynamic_amount"],SalesOrderProductObj:["record_type","quantity","product_price","product_id","discount","price_book_product_id","unit","sales_price","subtotal","is_giveaway","promotion_id","delivered_count","delivery_amount","remark","subtotal","sub_product_id","prod_pkg_key","parent_prod_pkg_key","bom_id","root_prod_pkg_key","price_book_id","parent_prod_package_id","attribute","nonstandard_attribute","attribute_json","attribute_price_book_id","mc_currency","mc_exchange_rate","actual_unit","sale_contract_id","sale_contract_line_id","price_book_discount","price_book_price","price_book_subtotal","node_type","temp_node_bom_id","temp_node_group_id","node_no","node_price","node_discount","share_rate","node_subtotal","base_unit_count","stat_unit_count","conversion_ratio","other_unit","other_unit_quantity","bom_core_id","bom_version","bom_type","related_core_id","new_bom_path","price_policy_id","price_policy_rule_ids","group_key","parent_gift_key","policy_dynamic_amount","amortize_amount","gift_amortize_price","gift_amortize_subtotal","quote_line_id","service_start_time","price_per_set","pricing_cycle","settlement_mode","whole_period_sale","settlement_cycle","settlement_amount_per_period","settlement_period","pricing_period","pricing_rate","settlement_rate","pricing_mode","service_end_time","price_tiered_record","price_book_price","price_book_discount","price_book_subtotal","non_standard_pro_description","detail_type"],VisitingObj:[],RebateIncomeDetailObj:["customer_id","income_type","amount","start_time","end_time","transaction_time","remark","owner"],RebateOutcomeDetailObj:["amount","transaction_time","payment_id","owner"],PrepayDetailObj:["name","customer_id","amount","transaction_time","income_type","outcome_type","online_charge_no","refund_id","remark","owner"],RebateUseRuleObj:["name","start_time","end_time","status","min_order_amount","used_max_amount","used_max_precent","remark","owner"],PromotionObj:["name","start_time","end_time","status","images","type","owner"],PromotionProductObj:["product_id","quota","promotion_id"],PromotionRuleObj:["order_discount","price_discount","order_money","promotion_id","fixed_price","derate_money","purchase_num","gift_product_num","gift_product_id","order_derate_money","gift_type"],AdvertisementObj:["ad_pictures","jump_type","status","sort","owner"],StatementObj:["ad_pictures","jump_type","status","sort","owner"],ExchangeReturnNoteProductObj:["remark"],CasesObj:["account_id","contact_id","subject","tel","owner","cases_status","record_type","resources","priority","due_to_time","catalog","description","partner_id","service_request_id","spare_parts_maintenance_task_detail_id","field_glcp__c","product_id","sn_id","field_glsb__c","device_id","partner_id"],ServiceFaultRecordObj:["name","cases_id","fault_solution_id","fault_phenomenon_id","out_data_own_organization","product_id","fault_solution_display_name","device_id","fault_phenomenon_display_name","fault_position_display_name","fault_position_id","fault_cause_id","comment","fault_cause_display_name","out_data_own_department","lock_status","owner","life_status","record_type","data_own_department","owner_department","out_owner","last_modified_time","create_time","last_modified_by","created_by","data_own_organization"],CasesDeviceObj:["device_id"],PartnerObj:["name","owner","credit_rating","partner_category","email","fax","tel","uniform_social_credit_code","url","area_location","partner_id","remark","applicant_name","business_scope","past_success","co_description"],NewOpportunityObj:["record_type","account_id","pricebook_id","price_book_id","close_date","amount","partner_id","out_owner","marketing_event_id","mc_currency","mc_exchange_rate","opp_discount","email"],NewOpportunityLinesObj:["record_type","product_id","new_opporutnity_lines_specs","quantity","price","discount","new_opporutnity_lines_unit","price_book_product_id","sales_price","price_book_id","mc_currency","mc_exchange_rate","total_amount","service_start_time","price_per_set","pricing_cycle","settlement_mode","whole_period_sale","settlement_cycle","settlement_amount_per_period","settlement_period","pricing_period","pricing_rate","settlement_rate","pricing_mode","service_end_time","price_tiered_record","price_book_price","price_book_discount","price_book_subtotal","non_standard_pro_description","detail_type"],ProductObj:["name","category","price","unit","product_line","product_status","barcode","product_code","remark","is_giveaway"],SPUObj:["name","owner","picture","record_type"],SpecificationObj:["name","owner","record_type"],SpecificationValueObj:["name","owner","record_type"],ReturnNoticeNoteObj:["return_date","account_id","return_warehouse_id","owner","return_amount","name"],ReturnNoticeNoteProductObj:["product_id","quantity","return_price","subtotal"],ExchangeReturnNoteObj:["name","refund_amount","sales_order_id","account_id","remark","partner_id"],FundReturnBackObj:["exchange_return_note_id","sales_order_id","account_id","refunded_amount","partner_id"],AccountFinInfoObj:["name","account_bank","account_bank_no","account_id","invoice_add","remark","tax_id","tel","title_type"],AccountAddrObj:["name","account_id","add_type","contact_id","contact_way","regional_location","remark","zipcode"],NewOpportunityContactsObj:["contact_id","role"],EnterpriseInfoObj:["name","biz_reg_name","enterprise_id","tel","email","url","fax","enterprise_type","uniform_social_credit_code","legal_representative","registered_capital","address","registration_status","business_scope"],AccountsReceivableNoteObj:["account_id","record_type","name","calculate_tax_method","due_date","is_overdue","note_date","remarks","opening_balance"],AccountsReceivableDetailObj:["sku_id","tax_price","tax_amount","tax_rate","price_total_amount","price_tax_amount","orderproduct_id","order_id","remark","ar_quantity","delivery_note_id","delivery_note_product_id","goods_received_note_id","goods_received_note_product_id"],SalesInvoiceObj:["record_type","account_id"],SalesInvoiceDetailObj:["sku_id"],AccountMainDataObj:["name","main_account_no","erp_code","erp_id","account_type","enterprise_type","legal_representative","registered_capital","registration_status","business_scope","tel","email","fax","url","remark","uniform_social_credit_code","biz_reg_name","partner_id","industry_level1","industry_level2"],TPMActivityObj:["name","end_date","begin_date","description","store_range","department_range","is_agreement_required","owner_department","code","activity_status"],TPMActivityDetailObj:["name","amount_standard","activity_item_id","type","code","remark","activity_cost_standard","activity_id","owner_department","record_type","cost_standard","display_form_id"],TPMActivityStoreObj:["name","activity_id","owner_department","owner","store_id","record_type"],ShoppingCartSummaryObj:["customer_id"],ShoppingCartObj:["quantity","is_giveaway","price","product_id","price_book_id","sales_price","price_book_product_id","discount","sales_amount","remark","promotion_id","unit","warehouse_id","_id","record_type"],DistributorAuthorizationAgreementObj:["name","start_date","end_date","remark","record_type","account_id"],AuthorizationAgreementLinesObj:["guide_price"],SaleContractObj:["account_id","price_book_id","quote_id","partner_id","contract_time"],SaleContractLineObj:["record_type","product_id","product_price","quantity","discount","sales_price","subtotal","bom_id","root_prod_pkg_key","parent_prod_pkg_key","prod_pkg_key","price_book_id","price_book_product_id","contract_id","attribute","nonstandard_attribute","attribute_json","attribute_price_book_id","quote_line_id","node_type","temp_node_bom_id","temp_node_group_id","node_no","actual_unit","base_unit_count","stat_unit_count","conversion_ratio","other_unit","other_unit_quantity","bom_core_id","bom_version","bom_type","related_core_id","new_bom_path","service_start_time","price_per_set","pricing_cycle","settlement_mode","whole_period_sale","settlement_cycle","settlement_amount_per_period","settlement_period","pricing_period","pricing_rate","settlement_rate","pricing_mode","service_end_time","price_tiered_record","price_book_price","price_book_discount","price_book_subtotal","non_standard_pro_description","detail_type"],ProjectObj:["name","owner","is_delay","out_owner","account_id","created_by","create_time","is_template","biz_status","record_type","lock_status","life_status","budget_amount","plan_end_date","actual_end_date","project_describe","plan_start_date","owner_department","last_modified_by","last_modified_time","actual_start_date","new_opportunity_id","parent_project_id","percentage_complete","project_template_id","data_own_department","project_actual_cost","remain_budget_amount","planed_working_hours","project_application_id","actual_working_hours_percentage"],ServiceKnowledgeObj:["title","summary","rich_text","category"],CompetitorObj:["name","url","uniform_social_credit_code","size","competitiveness","advantage","weaknesses","response_strategy","sales_analysis","market_situation_analysis","contact_information","document","remarks","identification"],CompetitiveProductsObj:["name","competitor_id","price","product_no","product_spec","barcode","unit","product_category_id","picture_path","remark"],CompetitiveLinesObj:["account_id","newopportunity_id","competitor_id","advantage","weaknesses","quotation_amount","is_win","remark","remarks"],ProcurementAccountObj:["email","legal_entity","url","logo","establish_date","address","reg_capital","tags","phone"],ServiceRequestObj:["priority","source_channel","contact_id","account_id","contact_tel","product_id","appointment_processing_time","country","province","city","district","address","location","subject","problem_desc","name","service_request_status","subject","contact_tel","assign_type","incoming_tel","assign_status","assign_time","finish_time","source_email","deal_user","out_data_own_department","account_level","remark","contact_id","service_address","contact_email","out_data_own_organization","customer_service_session_id","owner","is_auto_commit","lock_status","owner_department","life_status","record_type","data_own_department","create_time","created_by","out_owner","last_modified_time","last_modified_by","data_own_organization","service_progress"],ServiceRequestLinesObj:["service_request_id","device_id","data_own_organization","life_status","device_name","record_type","data_own_department","owner_department","out_owner","last_modified_time","create_time","last_modified_by","created_by"],WechatWorkExternalUserObj:["name","remark_name","enterprise_name","enterprise_full_name","avatar","sex","phone","position","type","external_user_id","wx_union_id","remark","wechat_work_create_time","add_source","add_qr_code_id","add_qr_code_name","owner_department","out_owner","data_own_department","record_type","owner"],ProcurementInfoObj:["caller_contact","caller_enterprise_name","title","content","winner_amount","caller_status","winner_status","caller_budget","bid_sub_type","bid_type","project_number","province","biding_end_time","tender_end_time","caller_type","caller_method","bid_id","project_name","publish_time","city","source_url","partner_url","caller_enterpris","winner_enterprise","winner_contact","agent_enterprise","agent_contact","tender_enterprise","products","jury","caller_bid_status"],ProcurementEnterpriseObj:["name","credit_code","caller_type","bid_type"],ProcurementContactObj:["name","contact_type","mobile1","mobile2","mobile3"],ProcurementLogObj:["name","procurement_info_id","account_id","partner_id","enterprise_info_id","competitor_id","log_type","leads_id"],WechatEmployeeObj:["name","mobile","telephone","owner"],ConsultFormObj:["name","description","tel","email","company_name"],ProjectTaskObj:["name","project_id","stage_id","task_describe","priority","biz_status","percentage_complete","plan_start_date","plan_end_date","owner","payment_plan_id","is_payment_task","is_milestone"],WechatFriendsRecordObj:["remark_tel","remark_name","enterprise_full_name","owner","external_user_id","enterprise_wechat_user_id","custom_id","remark_enterprise","marketing_event_id"],ProjectStageObj:["name","project_id","stage_describe","percentage_complete","plan_start_date","plan_end_date","owner"],AccessoryExchangeDetailObj:["employee_warehouse_id","note","batch_id","batch_sn","product_id","spare_part_type","exchange_amount","stock_type","accessory_exchange_id","spare_part_quantity","serial_number_id","employee_warehouse_detail_id"],AccessoryExchangeObj:["out_warehouse","comments","method","in_warehouse"],BatchObj:["begin_warning_date","warning_days_in_advance","remark","total_batch_stock","manufacture_date","product_id","expiry_date"],BatchStockObj:["begin_warning_date","warning_days_in_advance","batch_id","remark","combined_unit_quantity","manufacture_date","product_id","batch_available_stock","batch_real_stock","expiry_date","stock_id","unit","warehouse_id"],DeliveryNoteObj:["receive_remark","attachments","remark","ship_to_add","express_order_id","sales_order_id","images","consignee","express_org","total_delivery_money","receive_date","consignee_phone_number","delivery_date","account_id","delivery_warehouse_id","interaction_mode","delivery_mode","status","order_id","record_type","partner_id"],DeliveryNoteProductObj:["batch_sn","product_image","batch_stock_id","statistical_unit_quantity","delivery_num","specs","has_delivered_num","product_id","sales_order_id","sales_order_product_id","batch_real_stock","auxiliary_delivery_quantity","unit","delivery_warehouse_id","serial_number_id","delivery_note_id","whether_multi_unit_enabled","receive_remark","batch_id","remark","avg_price","real_receive_num_bast_unit","order_product_amount","delivery_money","actual_unit","stock_id","real_receive_num","return_back_quantity","sales_price","real_stock","record_type","data_own_department"],DeviceComponentsObj:["device_part_count","accessory_path","batch_id","partmatch_status","device_part_id","remark","bom_record_type","parent_product_id","accessory_material_id","part_sn","device_id","opensn","sn_id"],DeviceObj:["country","device_agency_id","city","device_customer_id","register_address","device_name","province","install_time","picture_path","device_status","address","expiration_time","production_time","device_product_id","display_name","buy_time","field_S3a6K__c","district","device_code","running_duration","serial_number_id","location","owner","record_type","last_maintain_time","partner_id"],EmployeeWarehouseAdjustmentNoteObj:["employee_warehouse_id","reason","batch_id","batch_sn","employee_warehouse_detail_quantity","adjustment_type","inbound_quantity","product_id","outbound_quantity","spare_part_type","stock_type","serial_number_id","employee_warehouse_detail_id"],EmployeeWarehouseDetailObj:["employee_warehouse_id","cases_id","batch_id","batch_sn","usage_status","freeze_quantity","product_id","total_quantity","spare_part_type","quantity","stock_type","unit","product_spec","occupancy_status","serial_number_id","category"],EmployeeWarehouseInOutRecordObj:["employee_warehouse_id","change_quantity","receive_product_id","batch_id","spare_parts_consumption_id","out_stock_type","original_freeze_quantity","change_freeze_quantity","balance_freeze_quantity","refund_product_id","product_id","original_quantity","accessory_exchange_detail_id","employee_warehouse_adjustment_note_id","goods_received_note_id","balance_quantity","out_bound_delivery_note_id","accessory_exchange_id","product_spec","in_stock_type","serial_number_id","category","employee_warehouse_detail_id"],EmployeeWarehouseObj:["remain_amount","warehouse_status"],GoodsReceivedNoteObj:["spare_parts_delivery_id","purchase_order_id","spare_parts_consumption_id","remark","goods_received_date","requisition_note_id","goods_received_type","stock_check_note_id","original_note_id","refund_material_bill_id","original_note","acceptance_mode","return_note_id","original_note_api_name","supplier_id","warehouse_id","return_note_product_id","partner_id"],GoodsReceivedNoteProductObj:["batch_sn","purchase_order_product_id","statistical_unit_quantity","specs","manufacture_date","product_id","goods_received_note_id","original_note","unit","original_note_api_name","return_note_product_id","serial_number_id","purchase_order_product_amount","whether_multi_unit_enabled","warning_days_in_advance","batch_id","remark","goods_received_amount","auxiliary_received_quantity","actual_unit","expiry_date","acceptance_warehouse_id","original_note_id","return_back_quantity","has_received_amount","owner","record_type"],IndividualStockTransactionsObj:["employee_warehouse_id","change_quantity","data_own_organization","batch_id","specs","product_id","original_note_detail_api_name","related_operation","original_note_detail_id","in_out_type","initial_quantity","original_note_id","original_note","balance_quantity","original_note_detail","sn_id","original_note_api_name","category","employee_warehouse_detail_id"],OBOMChangeRecordObj:["obom_record_number_id","data_own_organization","device_id","spare_parts_consumption_id","old_component_id","new_component_id","new_component_sn_id","old_component_quantity","new_component_batch_id","new_component_quantity","old_component_sn_id","old_component_batch_id","object_describe_id"],OutboundDeliveryNoteObj:["spare_parts_delivery_id","delivery_note_id","outbound_mode","spare_parts_consumption_id","remark","outbound_date","requisition_note_id","stock_check_note_id","original_note_id","purchase_return_note_id","original_note","receive_material_bill_id","original_note_api_name","outbound_type","warehouse_id","owner","partner_id"],OutboundDeliveryNoteProductObj:["whether_multi_unit_enabled","batch_id","batch_sn","batch_stock_id","remark","statistical_unit_quantity","specs","product_id","outbound_warehouse_id","outbound_amount","actual_unit","batch_real_stock","original_note_id","stock_id","original_note","auxiliary_outbound_quantity","unit","original_note_api_name","outbound_delivery_note_id","serial_number_id","available_stock","owner"],PurchaseOrderObj:["discount","remark","attachment","discount_money","expect_received_date","goods_received_status","product_money","total_money","purchase_date","supplier_id","owner","partner_id"],PurchaseOrderProductObj:["whether_multi_unit_enabled","subtotal_money","purchase_order_id","remark","tax_rate","goods_received_amount","statistical_unit_quantity","specs","price","product_id","goods_received_money","tax_excluded_money","product_discount","actual_unit","unit","purchase_amount","purchase_price","tax_money"],PurchaseReturnNoteObj:["reason","remark","refund_amount","goods_received_note_id","supplier_id","warehouse_id"],PurchaseReturnNoteProductObj:["goods_received_note_product_id","batch_sn","remark","specs","price","product_id","sn","quantity","batch_real_stock","batch","sub_sum","stock_id","purchase_return_note_id","unit","batch_stock","available_stock"],ReceiveMaterialBillObj:["spare_parts_delivery_id","employee_warehouse_id","note","cases_id","receive_type","confirm_operator","original_note_id","original_note","confirm_receive_material_time","receive_status","original_note_api_name","field_MtxvK__c","apply_amount","warehouse_id","owner","record_type","lock_status","life_status","created_by","create_time","out_owner","data_own_department","device_id"],ReceiveMaterialBillProductObj:["employee_warehouse_id","batch_id","new_product_id","spare_batch_stock_id","receive_type","init_available_stock","field_OIz18__c","product_batch_sn","batch_real_stock","stock_id","confirm_receive_material_time","receive_material_bill_id","sn_id","warehouse_id","apply_amount","owner","note","available_stock","record_type","lock_status","life_status","receive_status","created_by","create_time","out_owner","data_own_department"],RefundMaterialBillObj:["employee_warehouse_id","note","refund_warehouse_flag","refund_status","conofirm_operator","original_note_id","confirm_time","original_note","original_note_api_name","warehouse_id","apply_amount","owner","confirm_operator","refund_material_type","record_type","lock_status","life_status","created_by","create_time","out_owner","data_own_department"],RefundMaterialBillProductObj:["employee_warehouse_id","field_fkFha__c","batch_id","init_spare_part_quantity","product_id","refund_amount","spare_part_type","product_batch_sn","refund_material_bill_id","sn_id","employee_warehouse_detail_id","warehouse_id","cases_accessory_use_info_id","receive_material_bill_product_id","owner","note","refund_material_type","record_type","lock_status","life_status","refund_status","created_by","create_time","out_owner","data_own_department"],RequisitionNoteObj:["remark","requisition_date","transfer_out_warehouse_id","transfer_in_warehouse_id","inbound_confirmed","requisition_mode","owner","partner_id"],RequisitionNoteProductObj:["whether_multi_unit_enabled","batch_id","batch_sn","batch_stock_id","remark","requisition_product_amount","requisition_note_id","statistical_unit_quantity","specs","product_id","auxiliary_requisition_quantity","actual_unit","batch_real_stock","stock_id","unit","serial_number_id","available_stock","owner"],ReturnedGoodsInvoiceObj:["account_id","order_id","returned_goods_inv_amount","returned_goods_time","returned_goods_reason","remark","owner","name","return_warehouse_id","partner_id","current_level","field_11hAj__c","submit_time","is_user_define_work_flow","work_flow_id","return_mode","exchange_out_warehouse_id","status"],ReturnedGoodsInvoiceProductObj:["delivery_note_id","whether_multi_unit_enabled","order_product_id","batch_id","batch_sn","returned_product_price","delivery_note_product_id","specs","product_id","returned_goods_inv_id","quantity","auxiliary_quantity","actual_unit","unit","subtotal","serial_number_id","order_id","redelivery_sign"],SerialNumberObj:["remark","manufacture_date","product_id","is_using","whether_used","stock_id","unit","grade","warehouse_id"],SparePartsApplicationDetailObj:["altering_reason","remark","spec","applying_quantity","effective","product_id","returned_quantity","estimated_arrival_date","spare_parts_application_id","recieved_quantity","delivered_quantity","unit","category"],SparePartsApplicationObj:["employee_warehouse_id","reason","recieving_warehouse_id","remark","device_sn_id","expected_arriving_date","case_id","application_status","recieving_mode","consignee","address","device_id","consignee_phone_number","account_id"],SparePartsConsumptionDetailObj:["obom_record_number_id","old_batch_id","old_batch_sn","spare_parts_consumption_id","new_sn_id","usage_mode","new_remark","replace_device_components","consumed_quantity","old_spare_part_id","old_sn_id","new_batch_id","new_batch_sn","device_id","new_batch_stock_id","recycled_quantity","new_spare_part_id","spare_part_stock_id","employee_warehouse_quantity","spare_part_available_stock","employee_warehouse_detail_id","old_remark"],SparePartsConsumptionObj:["employee_warehouse_id","spare_part_warehouse_id","remark","case_id","recycled_warehouse_id","device_id","spare_part_source","name","owner"],SparePartsDeliveryDetailObj:["spare_parts_delivery_id","spare_parts_batch_stock_id","batch_sn","application_quantity","remark","received_quantity","spec","spare_parts_batch_id","product_id","spare_parts_sn_id","spare_parts_available_stock","spare_parts_application_id","delivered_quantity","spare_parts_application_detail_id","unit","spare_parts_stock_id","delivery_warehouse_id","current_delivery_quantity"],SparePartsDeliveryObj:["recipient","delivery_status","employee_warehouse_id","attachments","recieving_warehouse_id","delivery_remark","recipient_tel","receiving_remark","images","express_order_number","receiving_mode","express_org","recipient_address","delivery_date","object_describe_id"],SparePartsMaintenancePlanObj:["good_stock_quantity","available_defect_stock_quantity","required_good_quantity","product_status","remark","spec","field_855K1__c","product_id","defect_stock_quantity","planned_maintenance_quantity","related_task_status","lifecycle_application_quantity","completed_maintenance_quantity","plan_origin","category","task_assigned","plan_status"],SparePartsMaintenanceTaskDetailObj:["name","product_id","sn_id","device_id","maintenance_status","product_status","remark","case_id","maintenance_start_time","planned_quantity","maintenance_actual_completion_time","engineer","spare_parts_maintenance_task_id","spare_parts_maintenance_plan_id","category","maintenance_assumed_completion_time","object_describe_id"],SparePartsMaintenanceTaskObj:["task_status","task_assumed_completion_time","remark","task_start_time","refurbished_ratio","repaired_quantity","scrapped_quantity","total_task_quantity","outstanding_quantity","task_actual_completion_time","completion_ratio","object_describe_id"],SparePartsReturnDetailObj:["employee_warehouse_id","rejected_quantity","spare_parts_batch_stock_id","batch_sn","delivery_note_signed_quantity","remark","received_quantity","spec","spare_parts_return_id","spare_parts_batch","product_id","spare_parts_sn","quantity","individual_available_quantity","spare_parts_application_id","spare_parts_application_detail_id","spare_parts_source","unit","spare_parts_stock_id","stock_out_warehouse_id","stock_available_quantity","employee_warehouse_detail_id"],SparePartsReturnObj:["reason","stock_in_warehouse_id","images","attachments","remark","stock_in_confirmed","mode","object_describe_id"],StockCheckNoteObj:["remark","check_type","goods_received_note_generated","stock_check_date","outbound_note_generated","warehouse_id","partner_id"],StockCheckNoteProductObj:["whether_multi_unit_enabled","batch_id","batch_sn","system_amount","batch_stock_id","remark","statistical_unit_quantity","specs","product_id","auxiliary_check_quantity","actual_unit","batch_real_stock","stock_check_note_id","profit_amount","stock_id","unit","serial_number_id","check_amount"],StockDetailsObj:["relate_operate","batch_id","stock_change_amount","specs","product_id","inbound_type","goods_received_note_id","stock_id","stock_details_date","unit","current_stock","outbound_delivery_note_id","serial_number_id","remain_stock","batch_current_stock","outbound_type","warehouse_id"],StockObj:["field_yGILv__c","auxiliary_real_stock","batch_sn","product_status","combined_unit_quantity","safety_stock","specs","auxiliary_unit","product_id","product_safety_stock","product_max_stock","defective_quantity","good_quantity","field_02To9__c","blocked_stock","unit","max_stock","auxiliary_unit_info","real_stock","category","available_stock","warehouse_id"],SupplierObj:["upper_tenant_id","country","is_enable","city","tax_rate","bank","province","tel","email","bank_account","area","address","level","url","district","industrial_and_commercial_registration_number","industry_level2","industry_level1","location","supplier_id","tax_identification_number","object_describe_id","name","partner_id"],WarehouseObj:["country","warehouse_type","city","is_enable","employee_range","remark","number","dept_range","province","area","address","account_range","is_default","being_occupied","district","location"],CostAdjustmentNoteObj:["date","lock_rule","reason","remark","lock_user","partner_id","total_adjusted_amount","life_status_before_invalid","owner_department","out_resources","owner","lock_status","life_status","relevant_team","record_type","data_own_department","warehouse_id"],CostAdjustmentNoteProductObj:["lock_rule","remark","lock_user","cost_adjustment_note_id","adjusted_cost_price","specs","life_status_before_invalid","product_id","owner_department","owner","lock_status","subtotal_adjusted_amount","life_status","stock_id","relevant_team","record_type","unit","data_own_department","current_cost_price","real_stock"],InventoryFreezingDetailObj:["date","inventory_freezing_id","lock_rule","operating_type","remark","lock_user","freezing_note_id","life_status_before_invalid","owner_department","owner","lock_status","freezing_note","life_status","stock_id","record_type","relevant_team","data_own_department","freezing_note_api_name","frozen_quantity","warehouse_id"],InventoryFreezingObj:["date","lock_rule","operating_type","remark","lock_user","total_frozen_quantity","partner_id","original_freezing_note","life_status_before_invalid","original_freezing_note_api_name","owner_department","out_resources","owner","lock_status","life_status","stock_id","relevant_team","record_type","data_own_department","original_freezing_note_id","total_pending_unfreezing_quantity","total_unfrozen_quantity","warehouse_id"],InventoryUnfreezingDetailObj:["unfreezing_note_id","date","inventory_freezing_id","lock_rule","operating_type","unfreezing_note_api_name","remark","lock_user","life_status_before_invalid","owner_department","unfrozen_quantity","owner","lock_status","life_status","unfreezing_note","stock_id","record_type","relevant_team","data_own_department","warehouse_id"],InventoryDetailsObj:["lock_rule","batch_sn","field_516pg__c","guarantee_period","batch_stock_id","cargo_owner_type","ware_position_id","inventory_organization","specs","auxiliary_unit","manufacture_date","life_status_before_invalid","product_id","owner_department","field_bb4t9__c","lock_status","plan_tracking_number","relevant_team","quantity_auxiliary","bom_edition","unit","data_own_department","quantity_main","preserver_name","serial_number_id","cargo_owner_code","fauxpropid","batch_id","lock_user","inventory_status","cargo_owner_name","preserver_code","owner","life_status","expiry_date","position_details","stock_id","record_type","guarantee_unit","preserver_type","real_stock","warehouse_id"],TPMActivityUnifiedCaseObj:["name","activity_code","end_date","Dealer_Fee_Ratio__c","Brach_Office_Ratio__c","Headquarters_Ratio__c","Closing_Criteria__c","VerificationForm__c","Event_Initiator__c","Assessment_Criteria__c","activity_amount","write_off_cash","close_status","store_range","occupy_amount","activity_actual_amount","available_amount"],TPMActivityProductRangeObj:["name","Allocation_Ratio__c","Product_code__c","Main_House_Type__c","Area__c","Delivery_Set__c","Set_Proportion__c","Ancillary_Price__c","Ancillary_Product__c","price","product_id","activity_id"],CustomerRetentionStrategyObj:["workshop_visits","number_of_exhibition_forums","number_of_high_level_visits","expansion_strategy","planning_cycle","number_of_business_activities","account_id","company_visits","out_owner","owner","lock_status"],ContactEvaluationObj:["decision_support","change_adaptability","last_week_relation_result","customer_relationship_objectives","impact","competitor_bias","competitive_attitude","contact_id","kc","information_transfer","competitors_id","recognition_of_our_company","account_id","guidance","relation_result","customer_engagement_level","out_owner","owner","impact","competitor_bias","recognition_of_our_company","change_adaptability"],MCR_ExpansionPlanObj:["our_participant","activity_type","theme","account_id","client_side_participant","planned_time","customer_retention_strategy_id","planning_attachment","plan_description","status","out_owner","owner"],VocalizeObj:["proposer_id","description","source","relevant_attachments","processing_scheme","type","theme","account_id","status","out_owner","owner"],ProjectApplicationObj:["name","owner","out_owner","created_by","account_id","lock_status","life_status","create_time","contract_id","instructions","application_type","related_document","sales_order_id","project_manager","new_opportunity_id","expect_start_date","data_own_department","owner_department","last_modified_by","last_modified_time"],ProjectInspectionApplicationObj:["application_type","delivery_file","receipt_file","inspection_pass_time","cause_description","project_id","project_task_id","representative","status"],ProjectHandoverObj:["handover_instructions","handover_status","project_id","receive_time","handover_receiver","handover_attachment","handover_receipt"],ProjectRiskObj:["risk_desc","risk_grade","influence_degree","risk_attachment","coping_strategy","risk_property","time_window","project_id","handling_suggestion","risk_probability","project_stage_id","risk_type","risk_status","action_plan","close_reason","risk_name"],ProjectWeeklyObj:["end_date","plan_project_stage_id","current_project_stage_id","predict_next_workweek","last_weekly_id","project_id","project_manager","start_date","workweek","next_week_plan","account_id","week_progress","stage_percentage_complete"],ProjectBudgetObj:["budget_total_amount","project_id","new_opportunity_id"],ProjectDeliverableObj:["sale_contract_line_id","attachment","project_id","project_task_id","deliverable_status"],AssetBorrowingObj:["owner","record_type","name","date","purpose","total_quantities","returned_quantities","name","date","purpose","total_quantities","returned_quantities","returned_quantities","returned_quantities","life_status"],AssetBorrowingDetailObj:["owner","record_type","name","asset_borrowing_id","asset","product","customer","asset_location","quantity","lending_out_date","expected_return_date","actual_return_date","asset_borrowing_id","return_status","remark","life_status"],AssetExtensionObj:["owner","record_type","name","date","reason","applicant","applicant_tel","applicant_dept","remark","life_status"],AssetExtensionDetailObj:["owner","record_type","name","asset_extension_id","asset_borrowing_id","asset_borrowing_detail_id","asset","quantity","lending_out_date","expected_return_date","applied_extending_date","remark","life_status"],AssetReturnObj:["owner","record_type","name","date","applicant","applicant_tel","applicant_dept","remark","life_status"],AssetReturnDetailObj:["owner","record_type","name","asset_borrowing_id","asset_borrowing_detail_id","asset","quantity","lending_out_date","expected_return_date","actual_return_date","remark","life_status"],TimeSheetObj:["project_id","task_id"],ServiceRecordObj:["name","account_obj_id","contact_obj_id","call_id","kf_number","seat_id","origin","answer_status","leads_obj_id","service_request_id"],LoyaltyMemberObj:["name","partner_id","out_owner","data_own_department","birthday","gender","program_id","consumer_points","source","contact_id","tier_start_time","frozen_grading_points","grading_points","owner_department","tier_id","registration_time","evaluation_date","owner","avatar","record_type","account_id","frozen_points","phone","employee_id"],ActiveRecordObj:["interactive_types","interaction_records","interactive_content","recording_link","leads_id","new_opportunity_id","account_id"],SettlementDetailObj:["name","product_id","settlement_amount","remark"],ReceivedPaymentObj:["name","remark","arrival_date","amount_received","bank_statement_id","account_id","status"],MailObj:["name","date","subject","type","web_im_visitor_id","message_body","sender_email","original_link","message_body_text","sender","cases_id","remark","contact_id","leads_id","customer_service_session_id","summary","gpt_summary","account_id","consultant","time","lock_status","owner_department","owner","life_status","record_type","create_time","created_by","data_own_department","out_owner","last_modified_time","last_modified_by","data_own_organization","leads_id_list","contact_id_list","account_id_list","new_opportunity_id","opportunity_id_list","service_request_id","cc_address","to_address","bcc_address"]}});
define("crm-setting/objmap/action/config/_obj",[],function(e,t,o){o.exports={SourceApiName:["AccountObj","AccountMainDataObj","MailObj","PayChannelObj","EnterprisePayOrderObj","ServiceRecordObj","ConsultQuestionRecordObj","SalesOrderObj","SalesOrderProductObj","ContactObj","LeadsObj","OpportunityObj","ReturnedGoodsInvoiceObj","ReturnedGoodsInvoiceProductObj","SparePartsMaintenanceTaskDetailObj","SparePartsConsumptionObj","PaymentObj","OrderPaymentObj","PriceBookObj","PriceBookProductObj","QuoteLinesObj","PriceBookObj","PriceBookProductObj","QuoteObj","DeliveryNoteObj","DeliveryNoteProductObj","StockObj","RebateIncomeDetailObj","RebateOutcomeDetailObj","CustomerAccountObj","PrepayDetailObj","AccountTransactionFlowObj","EnterpriseRelationObj","PublicEmployeeObj","ErDepartmentObj","CasesObj","ServiceFaultRecordObj","PartnerObj","NewOpportunityObj","ProductObj","SPUObj","SpecificationObj","SpecificationValueObj","ReturnNoticeNoteObj","ReturnNoticeNoteProductObj","InvoiceApplicationObj","ContractObj","ContactEvaluationObj","RefundObj","EmployeeWarehouseDetailObj","ExchangeReturnNoteObj","DeviceObj","MarketingEventObj","EnterpriseInfoObj","PurchaseOrderObj","PurchaseOrderProductObj","ReceiveMaterialBillObj","ReceiveMaterialBillProductObj","RefundMaterialBillObj","RefundMaterialBillProductObj","AccountsReceivableNoteObj","AccountsReceivableDetailObj","SalesInvoiceObj","SalesInvoiceDetailObj","TPMActivityObj","TPMActivityAgreementObj","TPMActivityDetailObj","TPMActivityStoreObj","ShoppingCartSummaryObj","SaleContractObj","RequisitionNoteObj","RequisitionNoteProductObj","StockCheckNoteObj","StockCheckNoteProductObj","ExchangeReturnNoteObj","ExchangeReturnNoteProductObj","GoodsReceivedNoteObj","GoodsReceivedNoteProductObj","ProjectObj","CompetitorObj","ProcurementAccountObj","EmployeeWarehouseAdjustmentNoteObj","EmployeeWarehouseDetailObj","ServiceRequestObj","ServiceRequestLinesObj","ServiceKnowledgeObj","WechatWorkExternalUserObj","ProcurementInfoObj","ProcurementEnterpriseObj","ProcurementContactObj","ProcurementLogObj","WechatEmployeeObj","ConsultFormObj","ProjectTaskObj","WechatFriendsRecordObj","ProjectStageObj","DistributorAuthorizationAgreementObj","AuthorizationAgreementLinesObj","AccessoryExchangeDetailObj","AccessoryExchangeObj","BatchObj","BatchStockObj","DeliveryNoteObj","DeliveryNoteProductObj","DeviceComponentsObj","DeviceObj","EmployeeWarehouseAdjustmentNoteObj","EmployeeWarehouseDetailObj","EmployeeWarehouseInOutRecordObj","EmployeeWarehouseObj","GoodsReceivedNoteObj","GoodsReceivedNoteProductObj","IndividualStockTransactionsObj","OBOMChangeRecordObj","OutboundDeliveryNoteObj","OutboundDeliveryNoteProductObj","PurchaseOrderObj","PurchaseOrderProductObj","PurchaseReturnNoteObj","PurchaseReturnNoteProductObj","ReceiveMaterialBillObj","ReceiveMaterialBillProductObj","RefundMaterialBillObj","RefundMaterialBillProductObj","RequisitionNoteObj","RequisitionNoteProductObj","ReturnedGoodsInvoiceObj","ReturnedGoodsInvoiceProductObj","SerialNumberObj","SparePartsApplicationDetailObj","SparePartsApplicationObj","SparePartsConsumptionDetailObj","SparePartsConsumptionObj","SparePartsDeliveryDetailObj","SparePartsDeliveryObj","SparePartsMaintenancePlanObj","SparePartsMaintenanceTaskDetailObj","SparePartsMaintenanceTaskObj","SparePartsReturnDetailObj","SparePartsReturnObj","StockCheckNoteObj","StockCheckNoteProductObj","StockDetailsObj","StockObj","SupplierObj","WarehouseObj","InventoryFreezingObj","InventoryFreezingDetailObj","InventoryUnfreezingDetailObj","InventoryDetailsObj","CostAdjustmentNoteObj","CostAdjustmentNoteProductObj","TPMActivityUnifiedCaseObj","TPMActivityProductRangeObj","CustomerRetentionStrategyObj","ContactEvaluationObj","MCR_ExpansionPlanObj","VocalizeObj","ProjectApplicationObj","ProjectInspectionApplicationObj","ProjectHandoverObj","ProjectRiskObj","ProjectWeeklyObj","ProjectBudgetObj","ProjectDeliverableObj","AssetBorrowingObj","AssetBorrowingDetailObj","AssetExtensionObj","AssetExtensionDetailObj","AssetReturnObj","AssetReturnDetailObj","TimeSheetObj","LoyaltyMemberObj","ActiveRecordObj","PaymentPlanObj","ReceivedPaymentObj"],filterTargetApiName:["StockObj","StockDetailsObj","InventoryFreezingObj","InventoryFreezingDetailObj","InventoryUnfreezingDetailObj","InventoryDetailsObj","BatchStockObj","EmployeeWarehouseDetailObj","EmployeeWarehouseInOutRecordObj","IndividualStockTransactionsObj","SalesOrderProductObj","PersonnelObj","ProductObj","SPUObj","SpecificationValueObj"],EditApiName:["QuoteObj"]}});
define("crm-setting/objmap/action/config/_secondtype",[],function(e,n,t){t.exports=["select_one","select_many","record_type"]});
define("crm-setting/objmap/action/config/nconfig",["./_fieldConfig","./_secondtype","./_actionObj"],function(n,i,o){var e=n("./_fieldConfig"),c=n("./_secondtype"),n=n("./_actionObj");o.exports={field:e,secondType:c,actionObj:n}});
define("crm-setting/objmap/action/fetch",["crm-modules/common/util","./config/nconfig","../blackConfig"],function(e,t,i){var n,a=e("crm-modules/common/util"),o=e("./config/nconfig"),e=e("../blackConfig"),l=null==(n=e.presetObjectBlackList)?void 0:n.sourceObjectBlackList,s=null==(n=e.presetObjectBlackList)?void 0:n.targetObjectBlackList,p=e.presetFieldCommonApiNameBlackList,d=e.presetFieldBlackList,m=e.presetFieldWhiteList;i.exports={getAllFieldsByApiName:function(e){return a.FHHApi({url:"/EM1HNCRM/API/v1/object/"+e+"/controller/DescribeLayout",data:{apiname:e,include_detail_describe:"BizQueryObj"!=e,include_layout:!1,include_ref_describe:!1}},{errorAlertModel:1})},checkBtnOrMapRuleNumber:function(t,i,n){t?n&&n(!0):a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_mapping/service/checkCount",data:{source_api_name:i.sourceObj.value,target_api_name:i.targetObj.value},success:function(e){0===e.Result.StatusCode?!t&&1e6<=e.Value.customButtonCount?(a.alert(i.sourceObj.name+$t("源对象按钮已满10个,不能再添加")),n&&n(!1)):!t&&1e5<=e.Value.ruleCount?(a.alert(i.sourceObj.name+$t("映射到{{name}}的规则已经满5个",{name:i.targetObj.name})),n&&n(!1)):n&&n(!0):CRM.util.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},getAllMDFieldsByApiName:function(e){return a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList",data:{describeApiName:e,includeDetailList:!0,includeRefList:!1}},{errorAlertModel:1})},_formatData:function(e,t,i,n,a){var r=[];return e||(t=_.filter(t,function(e){return 1==e.is_active&&!0!==e.hide_button})),_.each(t,function(e){r.push({name:e[i],value:e[n],define_type:e.define_type||"",type:a})}),r},_getMainObjectsList:function(i,e){e&&CRM.get("crm-obj-mainlist")&&CRM.get("crm-obj-mainlist").length?i&&i(CRM.get("crm-obj-mainlist")):a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findBizDescribeList",data:{isDraft:!0,isIncludeFieldDescribe:!0,isIncludeSystemObj:!0,isIncludeUnActived:!0,packageName:"CRM",checkDetailObjectButton:!0},success:function(e){var t;0===e.Result.StatusCode?(t=(t=e.Value.objectDescribeList||[]).filter(function(e){return!e.original_describe_api_name}),CRM.set("crm-obj-mainlist",t),i&&i(t)):a.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},getSourceObjList:function(t,i){var n,a=this,r=[],s=t.get("isEdit"),c=l;a._getMainObjectsList(function(e){n=a._formatData(s,e,"display_name","api_name","source"),r=_.filter(n,function(e){return"package"!=e.define_type||!c||!_.contains(c,e.value)}),s&&"BizQueryObj"==t.get("source_api_name")&&r.push({name:$t("工商查询"),value:"BizQueryObj"}),s&&"HospitalObj"==t.get("source_api_name")&&r.push({name:$t("医院查询"),value:"HospitalObj"}),s&&"HospitalDepartmentObj"==t.get("source_api_name")&&r.push({name:$t("icm.crm.hospital_department-query-240927",null,"科室查询"),value:"HospitalDepartmentObj"}),i&&i(r)})},getTargetObjList:function(e,t){var i,n=this,a=e.get("isEdit"),r=s;n._getMainObjectsList(function(e){i=n._formatData(a,e,"display_name","api_name","target"),i=_.filter(i,function(e){return!r||!_.contains(r,e.value)}),t&&t(i)},!0)},_changeFields:function(e,t){var i=o.field.fieldChange,n=_.keys(i),a=$.extend(!0,{},e);return _.contains(n,t)?_.filter(a,function(e){return _.contains(_.keys(i[t]),e.api_name)?_.extend(e,{type:i[t][e.api_name]}):e}):e},filterFields:function(e,t,i,n,a){var r=[],s=_.where(i,{type:"group"}),c=this._getGroupsFieldsByFilter(s)||[],l=d,u=m;return _.each(i,function(e){if(!_.contains(o.field.blackType,e.type)&&!(l&&l.fields&&l.fields[t]&&_.contains(l.fields[t][a]||[],e.api_name)||l&&l.type&&l.type[t]&&_.contains(l.type[t][a]||[],e.type)||"group"==e.type)){if("component"!==e.used_in)return"system"==e.define_type||"package"==e.define_type?"sub"==n&&"owner"==e.api_name||p&&_.contains(p,e.api_name)?void 0:u&&u[t]?void(_.contains(u[t],e.api_name)&&r.push(e)):void r.push(e):void r.push(e);_.contains(_.keys(c),e.api_name)&&r.push($.extend({},e,{label:c[e.api_name]+"."+e.label}))}}),r&&this._changeFields(r,t)},_getGroupsFieldsByFilter:function(e){var n={};return _.filter(e,function(i){_.contains(_.keys(o.field.groups),i.group_type)&&i.fields&&_.each(i.fields,function(e,t){_.contains(o.field.groups[i.group_type],t)&&(n[e]=i.label)})}),n}}});
define("crm-setting/objmap/action/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("对象映射规则")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <p>1." + ((__t = $t("映射规则应用于线索转换")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("已设置映射规则的字段在线索转换时将自动带入映射对象")) == null ? "" : __t) + '</p> </div> <div class="con-table"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/objmap/action/view/editnextview",["crm-modules/action/field/field"],function(e,t,i){var a=e("crm-modules/action/field/field");return a.View.extend({initialize:function(){a.View.prototype.initialize.apply(this,arguments),this.createSDK()},createSDK:function(){var t,i=this,a=_.find(i.options.preData.map_object,function(e){return _.isEmpty(e.master_api_name)});seajs.use("paas-vui/sdk",function(e){e.getModule("object/button").then(function(e){t=e.init({object_api_name:a.source_api_name,is_fixed:!1,show_header:!1,api_name:i.options.preData.btn_api_name,button_type:"convert",data:{mapping_rule:{source_api_name:a.source_api_name,target_api_name:a.target_api_name,target_display_name:a.target_display_name}}}),i.view=t,i.$el.empty().append(t.$el)})})},collect:function(t){this.view.getData(function(e){t&&t(e)})},destroy:function(){this.view&&this.view.$destroy&&this.view.$destroy(),this.view&&this.view.destroy&&this.view.destroy(),a.View.prototype.destroy.call(this),$("body").off("click.mapobject"),$(".crm-a-objmap .crm-btn-primary").off("shownext")},render:function(){},events:{}})});
define("crm-setting/objmap/action/view/editview",["crm-modules/action/field/field","../components/map/map","../components/apinameinput/apinameinput"],function(t,e,i){var a=t("crm-modules/action/field/field"),n=_.map([["map_name",$t("映射名称"),2,!0,,30],["map_api_name","API Name",2,!0,1],["group",$t("映射的对象"),1],["map_object",""]],function(t){return{FieldName:t[0],FieldCaption:t[1],FieldType:t[2],IsNotNull:!!t[3],Right:t[4],max_length:t[5]}});return a.View.extend({initialize:function(){"system"==this.options.define_type&&(n[0].Right=1),this.options.fieldList=n,a.View.prototype.initialize.call(this),this.btnDisable("next")},btnDisable:function(t){$(".crm-action-field-full .full-header .crm-btn[data-action="+t+"]").attr("disabled","disabled")},mycomponents:{map_object:t("../components/map/map"),map_api_name:t("../components/apinameinput/apinameinput")},validate:function(){var t=this.forms.map_object.getValue();return!(!_.keys(this.forms.map_object.widgets.detail).length||(t&&!t[0]?(CRM.util.alert($t("请至少设置一条字段映射")),1):0<this.$(".detail-line-wrapper .error-ipt").length||this.$(".fm-error")[0]?(CRM.util.alert($t("请更正列表错误")),1):($('.crm-a-objmap .crm-btn-primary[data-action="next"]').trigger("showstep"),0)))}})});
define("crm-setting/objmap/action/view/nextview",["crm-modules/action/field/field"],function(e,t,i){var a=e("crm-modules/action/field/field");return a.View.extend({initialize:function(){a.View.prototype.initialize.apply(this,arguments),this.createSDK()},events:{},render:function(){},createSDK:function(){var t,i=this,a=_.find(i.options.preData.map_object,function(e){return _.isEmpty(e.master_api_name)});seajs.use("paas-vui/sdk",function(e){e.getModule("object/button").then(function(e){t=e.init({object_api_name:a.source_api_name,is_fixed:!1,show_header:!1,api_name:"",button_type:"convert",data:{mapping_rule:{source_api_name:a.source_api_name,target_api_name:a.target_api_name,target_display_name:a.target_display_name}}}),i.view=t,i.$el.empty().append(t.$el)})})},collect:function(t){this.view.getData(function(e){t&&t(e)})},destroy:function(){this.view&&this.view.$destroy&&this.view.$destroy(),this.view&&this.view.destroy&&this.view.destroy(),a.View.prototype.destroy.call(this),$("body").off("click.mapobject"),$(".crm-a-objmap .crm-btn-primary").off("shownext")}})});
define("crm-setting/objmap/action/view/view",["crm-modules/action/field/field","../components/map/map","../components/apinameinput/apinameinput"],function(t,e,i){var a=t("crm-modules/action/field/field"),n=_.map([["map_name",$t("映射名称"),2,!0,,30],["map_api_name","API Name",,!0,,30],["group",$t("映射的对象"),1],["map_object",""]],function(t){return{FieldName:t[0],FieldCaption:t[1],FieldType:t[2],IsNotNull:!!t[3],Right:t[4],max_length:t[5]}});return a.View.extend({initialize:function(){this.options.fieldList=n,a.View.prototype.initialize.call(this)},mycomponents:{map_object:t("../components/map/map"),map_api_name:t("../components/apinameinput/apinameinput")},validate:function(){var t=this.forms.map_object.getValue();return!(!_.keys(this.forms.map_object.widgets.detail).length||(t&&!t[0]?(CRM.util.alert($t("请至少设置一条字段映射")),1):0<this.$(".detail-line-wrapper .error-ipt").length||this.$(".fm-error")[0]?(CRM.util.alert($t("请更正列表错误")),1):($('.crm-a-objmap .crm-btn-primary[data-action="next"]').trigger("showstep"),0)))}})});
define("crm-setting/objmap/blackConfig",[],function(e,t,a){t.presetObjectBlackList={sourceObjectBlackList:["ForecastRuleObj","ForecastTaskObj","OperationsStrategyObj","SubAccountTreeRelationLogObj","AccountTreeRelationObj","BusinessRiskInformationObj","EnterpriseRiskObj","AccountRiskPortraitRecordObj","LeadsFlowRecordObj","LeadsTransferLogObj","BehaviorRecordObj","BehaviorIntegralDetailObj","HighSeasObj","LeadsPoolObj","ActivityQuestionObj","InteractionStrategyObj","InteractionStrategyDetailObj","ActivityMeetingSummaryObj","TermBankDetailObj","PartnerContactRelationshipObj","PartnerDepartmentObj","PartnerProvisionObj","PartnerAgreementObj","PartnerAgreementDetailObj","AgreementStatusRecordObj","GoalValueObj","GoalRuleObj","GoalRuleApplyCircleObj","EmployeeLoginUsageObj","EmployeeObjectUsageObj","EnterpriseRelationObj","ErDepartmentObj","PublicEmployeeObj","ErBindThirdAccountObj","AccountsPayableNoteObj","AccountsPayableDetailObj","FMCGSerialNumberObj","FMCGSerialNumberStatusObj","InspectionRecordObj","InspectionLogObj","CheckinsObj","ShelfReportAIDetailObj","VisitRouteObj","RouteCustomerObj","AreaManageObj","CoveredStoresObj","UserVisitObj","CheckinsImgObj","CheckinsImgDetailObj","CheckinsVerifyObj","CheckinsVerifyDetailObj","MileageStatisticsObj","MileageStatisticsDetailObj","StorePhotoWallInspectObj","StorePhotoInspectDetailObj","SuccessfulStoreRangeObj","ProjectStandardsObj","MustDistributeProductsObj","DisplayTypeStandardsObj","DisplayFormatDetailsObj","SummaryDisplayAchievementObj","DisplayDistrAchSummaryObj","DisplayProjectAchievementObj","DistributionProductsAchievedObj","ErpOrganizationObj","JournalObj","BlogObj","ScheduleObj","AnnounceObj","ServiceLogObj","TelesalesRecordObj","ApprovalFormObj","LeaveApplicationObj","LeaveApplicationItemObj","OvertimeApplicationObj","OvertimeApplicationItemObj","TravelReimbursementObj","TravelReimbursementItemObj","TravelApplicationObj","TravelApplicationItemObj","ReimbursementObj","ReimbursementItemObj","ApprovalInstanceObj","BpmInstance","StageInstanceObj","ApprovalTaskObj","BpmTask","StageTaskObj","ApproverOpinionObj","FlowTaskHandleTimeDetailObj","InspectionDataAppealRecordObj"],targetObjectBlackList:["ForecastRuleObj","ForecastTaskObj","OperationsStrategyObj","SubAccountTreeRelationLogObj","AccountTreeRelationObj","BusinessRiskInformationObj","EnterpriseRiskObj","AccountRiskPortraitRecordObj","LeadsFlowRecordObj","LeadsTransferLogObj","BehaviorRecordObj","BehaviorIntegralDetailObj","HighSeasObj","LeadsPoolObj","ActivityQuestionObj","InteractionStrategyObj","InteractionStrategyDetailObj","ActivityMeetingSummaryObj","TermBankDetailObj","PartnerContactRelationshipObj","PartnerDepartmentObj","PartnerProvisionObj","PartnerAgreementObj","PartnerAgreementDetailObj","AgreementStatusRecordObj","GoalValueObj","GoalRuleObj","GoalRuleApplyCircleObj","EmployeeLoginUsageObj","EmployeeObjectUsageObj","EnterpriseRelationObj","ErDepartmentObj","PublicEmployeeObj","ErBindThirdAccountObj","AccountsPayableNoteObj","AccountsPayableDetailObj","SalesStatementsObj","PointsRewardDetailObj","FMCGSerialNumberObj","FMCGSerialNumberStatusObj","InspectionRecordObj","InspectionLogObj","CheckinsObj","ShelfReportAIDetailObj","VisitRouteObj","RouteCustomerObj","AreaManageObj","CoveredStoresObj","UserVisitObj","CheckinsImgObj","CheckinsImgDetailObj","CheckinsVerifyObj","CheckinsVerifyDetailObj","MileageStatisticsObj","MileageStatisticsDetailObj","StorePhotoWallInspectObj","StorePhotoInspectDetailObj","SuccessfulStoreRangeObj","ProjectStandardsObj","MustDistributeProductsObj","DisplayTypeStandardsObj","DisplayFormatDetailsObj","SummaryDisplayAchievementObj","DisplayDistrAchSummaryObj","DisplayProjectAchievementObj","DistributionProductsAchievedObj","ErpOrganizationObj","JournalObj","BlogObj","ScheduleObj","AnnounceObj","ServiceLogObj","TelesalesRecordObj","ApprovalFormObj","LeaveApplicationObj","LeaveApplicationItemObj","OvertimeApplicationObj","OvertimeApplicationItemObj","TravelReimbursementObj","TravelReimbursementItemObj","TravelApplicationObj","TravelApplicationItemObj","ReimbursementObj","ReimbursementItemObj","ApprovalInstanceObj","BpmInstance","StageInstanceObj","ApprovalTaskObj","BpmTask","StageTaskObj","ApproverOpinionObj","FlowTaskHandleTimeDetailObj","InspectionDataAppealRecordObj","StockObj","StockDetailsObj","InventoryFreezingObj","InventoryFreezingDetailObj","InventoryUnfreezingDetailObj","InventoryDetailsObj","BatchStockObj","EmployeeWarehouseDetailObj","EmployeeWarehouseInOutRecordObj","IndividualStockTransactionsObj","SalesOrderProductObj","PersonnelObj","ProductObj","SPUObj","SpecificationValueObj","AccountsReceivableNoteObj","MatchNoteObj"]},t.presetFieldBlackList={fields:{SalesOrderObj:{target:["confirmed_delivery_date","confirmed_receive_date","delivery_comment","resource","promotion_id"]},OpportunityObj:{source:["sales_process_name","sales_stg_changed_time","after_sale_stage_order","oppo_stage_id","biz_status","is_start_after_sale","last_followed_time","status","after_sale_stage_status","after_sale_stage_name","origin_source","is_bind_after_sale","leads_id","probability_amount","oppo_after_stage_id","before_sale_stage_order","before_sale_stage_name"],target:["sales_stg_changed_time","probability","after_sale_stage_order","oppo_stage_id","sales_process_name","biz_status","is_start_after_sale","last_followed_time","status","after_sale_stage_status","after_sale_stage_name","origin_source","is_bind_after_sale","leads_id","probability_amount","oppo_after_stage_id","before_sale_stage_order","before_sale_stage_name"]},ReturnedGoodsInvoiceObj:{source:["ShouldReturnMoney"],target:["ShouldReturnMoney"]},ContactObj:{source:["mobile","tel","date_of_birth","year_of_birth","month_of_birth","day_of_birth"],target:["mobile","tel","date_of_birth","year_of_birth","month_of_birth","day_of_birth"]},LeadsObj:{target:["close_reason","completed_result","back_reason","returned_time","biz_status","life_status","owner_department","assigner_id","assigned_time","is_overtime","is_duplicated","is_relevance_wechat","out_resources","last_follower","last_follow_time","transform_time","transform_period","remaining_time","changed_to_mql_period"]},RefundObj:{target:["account_id"]},VisitingObj:{target:["account_id"]},WechatEmployeeObj:{source:["record_type","out_owner"],target:["record_type","out_owner"]},AccountObj:{source:["biz_status","deal_status","expire_time","last_followed_time","last_follower","last_deal_closed_time","returned_time","claimed_time","extend_days","remaining_time","owner_modified_time","completion_rate","completed_field_quantity","account_path","enable_risk_portrait","is_blacklist","blacklist_desc","credit_limit_upper","remind_days","risk_scores_model_name","self_service_scores_rule_name","formula_credit_limit","is_risk_monitor","third_party_risk_scores","credit_rule_name","credit_limit_lower","origin_source","industry_ext","is_remind_recycling","acc_expired_time","signing_status"],target:["phone_number_attribution_city","phone_number_attribution_province","phone_number_attribution_district","phone_number_attribution_country","phone_number_attribution_address","phone_number_attribution_location","transfer_count","biz_reg_name","industry_ext","high_seas_name","high_seas_id","biz_status","deal_status","expire_time","last_followed_time","last_follower","last_deal_closed_time","returned_time","claimed_time","extend_days","remaining_time","owner_modified_time","completion_rate","completed_field_quantity","account_path","enable_risk_portrait","is_blacklist","blacklist_desc","credit_limit_upper","remind_days","risk_scores_model_name","self_service_scores_rule_name","formula_credit_limit","is_risk_monitor","third_party_risk_scores","credit_rule_name","credit_limit_lower","origin_source","is_remind_recycling","acc_expired_time","signing_status"]},NewOpportunityObj:{source:["sales_process_id","sales_stage","leads_id"],target:["sales_process_id","sales_stage","leads_id"]},PartnerObj:{source:["acc_expired_time","signing_status"],target:["acc_expired_time","signing_status"]},ProjectObj:{target:["planed_work_cost","actual_working_hours","biz_status","expense_claim_amount","remain_working_hours","actual_work_cost","percentage_complete"]},ProjectStageObj:{target:["actual_working_hours","actual_work_cost","planed_work_cost"]}},type:{ContractObj:{target:["image","file_attachment"]},RefundObj:{target:["image","file_attachment"]},InvoiceApplicationObj:{target:["image"]},VisitingObj:{target:["image","file_attachment"]},MarketingEventObj:{target:["image","file_attachment"]}}},t.presetFieldCommonApiNameBlackList=["_id","tenant_id","lock_status","extend_obj_data_id","package","object_describe_id","object_describe_api_name","version","lock_user","lock_rule","life_status_before_invalid","is_deleted","data_auth_code","change_type","out_data_auth_code","order_by","data_auth_id","out_data_auth_id","origin_source","sys_modified_time"]});
define("crm-setting/objmap/objmap",["./template/tpl-html","crm-widget/table/table","./action/action","./action/config/nconfig"],function(e,t,a){var s=CRM.util,i=e("./template/tpl-html"),l=e("crm-widget/table/table"),n=e("./action/action"),e=e("./action/config/nconfig"),o=Backbone.View.extend({events:{"click .j-add":"_addMapHandle"},initialize:function(e){this.setElement(e.wrapper),this.$el.html(i({})),this.widgets={},this.createEl()},createEl:function(){this.$el.html(['<div class="crm-module-con crm-scroll"></div>','<div class="bottom-span"></div>'].join(""))},render:function(){var i=this,n="";i.widgets.tab&&i.widgets.tab.destroy(),i.widgets.tab=new l({$el:$(".crm-module-con",i.$el),url:"/EM1HNCRM/API/v1/object/object_mapping/service/findRuleList",postData:{status:0,rule_name:""},title:$t("对象映射规则"),showPage:!1,requestType:"FHHApi",searchTerm:{pos:"C",type:"status",showManage:!1,showCustom:!1,options:[{id:-1,name:$t("全部")},{id:0,isdef:!0,name:$t("启用")},{id:1,name:$t("禁用")}]},operate:{btns:[{text:$t("新建"),className:"j-add"}]},search:{placeHolder:$t("搜索规则名称"),type:"Keyword",highFieldName:"rule_name"},columns:[{data:"rule_name",title:$t("规则名称"),width:300},{data:"rule_action_name",title:$t("触发动作"),width:300},{data:"source_display_name",title:$t("信息源"),width:200},{data:"target_display_name",title:$t("映射到"),width:200},{data:"status",title:$t("状态"),width:100,render:function(e,t,a){return 0===a.status?$t("启用"):$t("禁用")}},{data:"",lastFixed:!0,lastFixedIndex:1e3,title:$t("操作"),render:function(e,t,a){var i;return["HospitalObj","HospitalDepartmentObj"].includes(a.source_api_name)?'<span class="operate edite-recover">'+$t("编辑")+"</span>":(i="system"===a.define_type,'<span class="operate edite-recover">'+$t("编辑")+"</span>"+(1===a.status?'<span class="operate enable-recover">'+$t("启用")+"</span>"+(i?"":'<span class="operate delete-recover">'+$t("删除")+" </span>"):i?"":'<span class="operate disable-recover">'+$t("禁用")+"</span>"))}}],formatData:function(e){return e.ruleList=e.ruleList||e.rule_list||[],$(".bottom-span",i.$el).html($t("当前有{{length}}条映射规则",{length:e.ruleList.length})),{totalCount:e.Page&&e.Page.TotalCount||0,data:e.ruleList}},paramFormat:function(e){return{rule_name:e.Keyword||"",status:e.status}},initComplete:function(){CRM.api.show_guide({show_type:"tip",key:"objmap_page_guide",data:[{$target:i.$(".j-add"),pos:"right",appendBody:!0,text:$t("自定义数据映射规则业务转换一键搞定")}]})},error:function(e){s.alert(e)}}),i.widgets.tab.on("trclick",function(e,t,a){a.hasClass("edite-recover")?i._clickEditHandle(e,t,a):a.hasClass("enable-recover")?i._clickEnabledHandle(e,t,a):a.hasClass("disable-recover")?n=s.confirm($t("禁用后该映射规则将不可用")+$t("确认禁用吗"),$t("提示"),function(){s.remind(1,$t("操作成功")),n.hide(),i._clickDisabledHandle(e,t,a)}):a.hasClass("delete-recover")&&(0===e.status?s.alert($t("该映射规则未被禁用不能被删除")):n=s.confirm($t("确认删除此按钮吗？"),$t("提示"),function(){s.remind(1,$t("操作成功")),n.hide(),i._clickDeleteHandle(e,t,a)}))})},addRulNameSelect:function(){var a=this;a.widgets.filterSelect=a.widgets.tab.addSelect({$target:this.$(".dt-op-box"),pos:"before",label:$t("状态")+":",options:[{value:-1,name:$t("全部")},{value:0,name:$t("启用")},{value:1,name:$t("禁用")}],defaultValue:-1,zIndex:300}),a.widgets.filterSelect.on("change",function(e,t){a.widgets.tab.setParam({status:t.value},!0)})},_addMapHandle:function(){this._getOperate(function(e){e.add()})},_getOperate:function(e){var a=this,t=a.widgets.operate;t&&t.destroy&&(t.destroy(),t=null),(t=new n).on("refresh",function(e,t){a.widgets.tab&&a.widgets.tab.setParam({},!0)}),e&&e(t),a.widgets.operate=t},_clickEnabledHandle:function(t){this._getOperate(function(e){e.enable(t)})},_clickDisabledHandle:function(t){this._getOperate(function(e){e.disable(t)})},_clickDeleteHandle:function(t){this._getOperate(function(e){e.delete(t)})},_clickEditHandle:function(t,e,a){a.hasClass("edite-recover")&&(this._getOperate(function(e){e.edit(t)}),CRM.util.uploadLog("crmsetting","s-rule",{eventId:"editmap",eventType:"cl"}))},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this._guide&&this._guide.destroy(),this.$el.remove(),this.$el=null}});o.ActionConfig=e,a.exports=o});
define("crm-setting/objmap/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-module-con crm-scroll"></div> <div class="bottom-span"></div>';
        }
        return __p;
    };
});