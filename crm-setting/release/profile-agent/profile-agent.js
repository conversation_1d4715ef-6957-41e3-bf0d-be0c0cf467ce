function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var f,a={},t=Object.prototype,s=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",i=e.toStringTag||"@@toStringTag";function l(t,e,r,n){return Object.defineProperty(t,e,{value:r,enumerable:!n,configurable:!n,writable:!n})}try{l({},"")}catch(f){l=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var i,o,a,u,e=e&&e.prototype instanceof g?e:g,e=Object.create(e.prototype);return l(e,"_invoke",(i=t,o=r,a=new _(n||[]),u=1,function(t,e){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw e;return{value:f,done:!0}}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){r=function t(e,r){var n=r.method,i=e.i[n];if(i===f)return r.delegate=null,"throw"===n&&e.i.return&&(r.method="return",r.arg=f,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;n=c(i,e.i,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,p;i=n.arg;return i?i.done?(r[e.r]=i.value,r.next=e.n,"return"!==r.method&&(r.method="next",r.arg=f),r.delegate=null,p):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,p)}(r,a);if(r){if(r===p)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;r=c(i,o,a);if("normal"===r.type){if(u=a.done?4:2,r.arg===p)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(u=4,a.method="throw",a.arg=r.arg)}}),!0),e}function c(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var p={};function g(){}function o(){}function h(){}var e={},d=(l(e,n,function(){return this}),Object.getPrototypeOf),d=d&&d(d(P([]))),m=(d&&d!==t&&s.call(d,n)&&(e=d),h.prototype=g.prototype=Object.create(e));function v(t){["next","throw","return"].forEach(function(e){l(t,e,function(t){return this._invoke(e,t)})})}function y(a,u){var e;l(this,"_invoke",function(r,n){function t(){return new u(function(t,e){!function e(t,r,n,i){var o,t=c(a[t],a,r);if("throw"!==t.type)return(r=(o=t.arg).value)&&"object"==_typeof(r)&&s.call(r,"__await")?u.resolve(r.__await).then(function(t){e("next",t,n,i)},function(t){e("throw",t,n,i)}):u.resolve(r).then(function(t){o.value=t,n(o)},function(t){return e("throw",t,n,i)});i(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()},!0)}function S(t){this.tryEntries.push(t)}function w(t){var e=t[4]||{};e.type="normal",e.arg=f,t[4]=e}function _(t){this.tryEntries=[[-1]],t.forEach(S,this),this.reset(!0)}function P(e){if(null!=e){var r,t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return r=-1,(t=function t(){for(;++r<e.length;)if(s.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=f,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return l(m,"constructor",o.prototype=h),l(h,"constructor",o),o.displayName=l(h,i,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,h):(t.__proto__=h,l(t,i,"GeneratorFunction")),t.prototype=Object.create(m),t},a.awrap=function(t){return{__await:t}},v(y.prototype),l(y.prototype,r,function(){return this}),a.AsyncIterator=y,a.async=function(t,e,r,n,i){void 0===i&&(i=Promise);var o=new y(u(t,e,r,n),i);return a.isGeneratorFunction(e)?o:o.next().then(function(t){return t.done?t.value:o.next()})},v(m),l(m,i,"Generator"),l(m,n,function(){return this}),l(m,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,r=Object(t),n=[];for(e in r)n.unshift(e);return function t(){for(;n.length;)if((e=n.pop())in r)return t.value=e,t.done=!1,t;return t.done=!0,t}},a.values=P,_.prototype={constructor:_,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=f,this.done=!1,this.delegate=null,this.method="next",this.arg=f,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&s.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=f)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function t(t){o.type="throw",o.arg=e,r.next=t}for(var n=r.tryEntries.length-1;0<=n;--n){var i=this.tryEntries[n],o=i[4],a=this.prev,u=i[1],s=i[2];if(-1===i[0])return t("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=a){if(a<u)return this.method="next",this.arg=f,t(u),!0;if(a<s)return t(s),!1}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var i=n;break}}var o=(i=i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]?null:i)?i[4]:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i[2],p):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r[2]===t)return this.complete(r[4],r[3]),w(r),p}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r,n,i=this.tryEntries[e];if(i[0]===t)return"throw"===(r=i[4]).type&&(n=r.arg,w(i)),n}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={i:P(t),r:e,n:r},"next"===this.method&&(this.arg=f),p}},a}function asyncGeneratorStep(t,e,r,n,i,o,a){try{var u=t[o](a),s=u.value}catch(t){return void r(t)}u.done?e(s):Promise.resolve(s).then(n,i)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(e,r){var n=u.apply(t,a);function i(t){asyncGeneratorStep(n,e,r,i,o,"next",t)}function o(t){asyncGeneratorStep(n,e,r,i,o,"throw",t)}i(void 0)})}}define("crm-setting/profile-agent/profile-agent",[],function(t,e,r){r.exports=Backbone.View.extend({initialize:function(){},render:function(){var t=[{label:$t("sfa.aiCustomerProfileSetting.targetIndustries"),prop:"target_industries",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.targetIndustriesPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.targetIndustriesRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.prosperousIndustries"),prop:"prosperous_industries",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.prosperousIndustriesPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.prosperousIndustriesRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.targetProducts"),prop:"target_products",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.targetProductsPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.targetProductsRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.serviceModels"),prop:"service_models",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.serviceModelsPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.serviceModelsRequired")}]},{prop:"customer_profile_agent",component:"fx-switch",defaultValue:!1,isShowForm:!1}],e=[$t("sfa.aiCustomerProfileSetting.featureSettingTips1"),$t("sfa.aiCustomerProfileSetting.featureSettingTips2"),$t("sfa.aiCustomerProfileSetting.featureSettingTips3"),$t("sfa.aiCustomerProfileSetting.featureSettingTips4"),$t("sfa.aiCustomerProfileSetting.featureSettingTips5")],r={},n={},i=[];_.each(t,function(t){r[t.prop]=t.rules,n[t.prop]=t.defaultValue,i.push(t.prop)}),FxUI.create({wrapper:this.options.wrapper[0],template:'\n                    <div class="profile-configure-container">\n                        <div class="profile-configure-title">\n                            <span>{{ title }}</span>\n                        </div>\n                        <div class="profile-configure-content">\n                            <div class="profile-configure-item">\n                                <div class="content">\n                                    <div class="feature-setting-tip">\n                                        <span class="fx-icon-f-info icon"></span>\n                                        <div class="feature-setting-tip-content">\n                                            <p v-for="(item, index) in featureSettingTips" :key="index" class="feature-setting-tip-item">\n                                                <span class="index">{{ index + 1 }}.</span>\n                                                <span>{{ item }}</span>\n                                            </p>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            \n                            <div class="profile-configure-item open-status">\n                                <div class="profile-configure-item-header">\n                                    <span class="profile-configure-item-header-title">{{ title }}</span>\n                                    <div class="profile-configure-item-header-content">\n                                        <span class="status-tip">{{ openProfileAgentTip }}</span>\n                                        <fx-switch size="small" :disabled="openOrOpeningProfileAgentStatus" :value="openOrOpeningProfileAgentStatus" @change="openProfileAgent" />\n                                    </div>\n                                </div>\n                                <div class="content">\n                                    <p>{{ openStatusDesc }}</p>\n                                </div>\n                            </div>\n                            <div class="profile-configure-item" v-if="openProfileAgentStatus">\n                                <div class="profile-configure-item-header">\n                                    <span class="profile-configure-item-header-title">{{ featureSettingFormTitle }}</span>\n                                </div>\n                                <div class="content">\n                                    <fx-form size="mini" lineSize="micro" ref="featureSettingForm" :rules="featureSettingFormRules" :model="featureSettingFormData">\n                                            <fx-form-item v-for="item in featureSettingFormProps" :key="item.prop" :label="item.label" :prop="item.prop">\n                                                <fx-input v-model="featureSettingFormData[item.prop]" :placeholder="item.placeholder" />\n                                            </fx-form-item>\n                                            <fx-button type="primary" size="mini" @click="submitFeatureSettingForm" :loading="featureSettingFormLoading">{{ $t(\'保存\') }}</fx-button>\n                                        </fx-form>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                ',data:function(){return{title:$t("sfa.aiCustomerProfileSetting.title"),openStatusDesc:$t("sfa.aiCustomerProfileSetting.openStatusDesc"),featureSettingTips:e,featureSettingFormTitle:$t("sfa.aiCustomerProfileSetting.featureSettingFormTitle"),featureSettingFormData:n,featureSettingFormProps:t.filter(function(t){return"customer_profile_agent"!==t.prop}),featureSettingFormRules:r,featureSettingFormLoading:!1}},computed:{openOrOpeningProfileAgentStatus:function(){return"0"!==this.featureSettingFormData.customer_profile_agent},openProfileAgentStatus:function(){return"1"===this.featureSettingFormData.customer_profile_agent},openProfileAgentTip:function(){return"2"===this.featureSettingFormData.customer_profile_agent?$t("正在开启中，请耐心等待"):$t("crm.setting.tradeconfigure.warn_cannot_closed")}},created:function(){this.getFeatureSettingValue()},methods:{getFeatureSettingValue:function(){var r=this;CRM.util.showLoading_new(),CRM.util.getConfigValues(i).then(function(t){_.each(t,function(t){var e=t.key,t=t.value;void 0!==t&&(r.featureSettingFormData[e]=t)})},function(t){CRM.util.alert(t)}).always(function(){CRM.util.hideLoading_new()})},submitFeatureSettingForm:function(){var t,r=this;null!=(t=this.$refs.featureSettingForm)&&t.validate&&this.$refs.featureSettingForm.validate(function(t){var e;t&&(e=[],_.each(r.featureSettingFormProps,function(t){e.push({key:t.prop,value:r.featureSettingFormData[t.prop]})}),r.setConfigValues(e))})},openProfileAgent:function(e){var r=this;return _asyncToGenerator(_regeneratorRuntime().mark(function t(){return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!0===e)return t.next=3,r.setConfigValues([{key:"customer_profile_agent",value:"2"}]);t.next=4;break;case 3:r.getFeatureSettingValue();case 4:case"end":return t.stop()}},t)}))()},setConfigValues:function(t){return new Promise(function(e,r){CRM.util.showLoading_new(),CRM.util.setConfigValues(t).then(function(t){CRM.util.remind(1,$t("设置成功")),e(!0)},function(t){CRM.util.remind(3,t||$t("设置失败")),r(t)}).always(function(){CRM.util.hideLoading_new()})})}}})}})});