function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,c=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},n=r.iterator||"@@iterator",e=r.asyncIterator||"@@asyncIterator",o=r.toStringTag||"@@toStringTag";function f(t,r,e,n){return Object.defineProperty(t,r,{value:e,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(s){f=function(t,r,e){return t[r]=e}}function u(t,r,e,n){var o,i,a,u,r=r&&r.prototype instanceof p?r:p,r=Object.create(r.prototype);return f(r,"_invoke",(o=t,i=e,a=new x(n||[]),u=1,function(t,r){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw r;return{value:s,done:!0}}for(a.method=t,a.arg=r;;){var e=a.delegate;if(e){e=function t(r,e){var n=e.method,o=r.i[n];if(o===s)return e.delegate=null,"throw"===n&&r.i.return&&(e.method="return",e.arg=s,t(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),l;n=h(o,r.i,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,l;o=n.arg;return o?o.done?(e[r.r]=o.value,e.next=r.n,"return"!==e.method&&(e.method="next",e.arg=s),e.delegate=null,l):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}(e,a);if(e){if(e===l)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;e=h(o,i,a);if("normal"===e.type){if(u=a.done?4:2,e.arg===l)continue;return{value:e.arg,done:a.done}}"throw"===e.type&&(u=4,a.method="throw",a.arg=e.arg)}}),!0),r}function h(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var l={};function p(){}function i(){}function y(){}var r={},g=(f(r,n,function(){return this}),Object.getPrototypeOf),g=g&&g(g(_([]))),d=(g&&g!==t&&c.call(g,n)&&(r=g),y.prototype=p.prototype=Object.create(r));function v(t){["next","throw","return"].forEach(function(r){f(t,r,function(t){return this._invoke(r,t)})})}function m(a,u){var r;f(this,"_invoke",function(e,n){function t(){return new u(function(t,r){!function r(t,e,n,o){var i,t=h(a[t],a,e);if("throw"!==t.type)return(e=(i=t.arg).value)&&"object"==_typeof(e)&&c.call(e,"__await")?u.resolve(e.__await).then(function(t){r("next",t,n,o)},function(t){r("throw",t,n,o)}):u.resolve(e).then(function(t){i.value=t,n(i)},function(t){return r("throw",t,n,o)});o(t.arg)}(e,n,t,r)})}return r=r?r.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var r=t[4]||{};r.type="normal",r.arg=s,t[4]=r}function x(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function _(r){if(null!=r){var e,t=r[n];if(t)return t.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return e=-1,(t=function t(){for(;++e<r.length;)if(c.call(r,e))return t.value=r[e],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(r)+" is not iterable")}return f(d,"constructor",i.prototype=y),f(y,"constructor",i),i.displayName=f(y,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===i||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,f(t,o,"GeneratorFunction")),t.prototype=Object.create(d),t},a.awrap=function(t){return{__await:t}},v(m.prototype),f(m.prototype,e,function(){return this}),a.AsyncIterator=m,a.async=function(t,r,e,n,o){void 0===o&&(o=Promise);var i=new m(u(t,r,e,n),o);return a.isGeneratorFunction(r)?i:i.next().then(function(t){return t.done?t.value:i.next()})},v(d),f(d,o,"Generator"),f(d,n,function(){return this}),f(d,"toString",function(){return"[object Generator]"}),a.keys=function(t){var r,e=Object(t),n=[];for(r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},a.values=_,x.prototype={constructor:x,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(b),!t)for(var r in this)"t"===r.charAt(0)&&c.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function t(t){i.type="throw",i.arg=r,e.next=t}for(var n=e.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n],i=o[4],a=this.prev,u=o[1],c=o[2];if(-1===o[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=a){if(a<u)return this.method="next",this.arg=s,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(-1<n[0]&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o[0]<=r&&r<=o[2]?null:o)?o[4]:{};return i.type=t,i.arg=r,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),l},finish:function(t){for(var r=this.tryEntries.length-1;0<=r;--r){var e=this.tryEntries[r];if(e[2]===t)return this.complete(e[4],e[3]),b(e),l}},catch:function(t){for(var r=this.tryEntries.length-1;0<=r;--r){var e,n,o=this.tryEntries[r];if(o[0]===t)return"throw"===(e=o[4]).type&&(n=e.arg,b(o)),n}throw Error("illegal catch attempt")},delegateYield:function(t,r,e){return this.delegate={i:_(t),r:r,n:e},"next"===this.method&&(this.arg=s),l}},a}function asyncGeneratorStep(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function _asyncToGenerator(u){return function(){var t=this,a=arguments;return new Promise(function(r,e){var n=u.apply(t,a);function o(t){asyncGeneratorStep(n,r,e,o,i,"next",t)}function i(t){asyncGeneratorStep(n,r,e,o,i,"throw",t)}o(void 0)})}}define("crm-setting/employeeusageanalysis/employeeusageanalysis",[],function(t,r,e){var n=Backbone.View.extend({initialize:function(n){var r,o=this;Fx.getBizComponent("fbi","EmployeeUsageAnalysisService").then((r=_asyncToGenerator(_regeneratorRuntime().mark(function t(r){var e;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r();case 2:e=t.sent.default,o.ins=new e(n),o.ins.init();case 5:case"end":return t.stop()}},t)})),function(t){return r.apply(this,arguments)}))},destroy:function(){this.ins&&this.ins.destroy&&this.ins.destroy()}});e.exports=n});