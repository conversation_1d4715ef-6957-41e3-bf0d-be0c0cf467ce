define("crm-setting/shiporder/shiporder",["./shiporder/model/model","./tpl-html"],function(i,e,t){var s=i("./shiporder/model/model").default;t.exports=Backbone.View.extend({template:i("./tpl-html"),initialize:function(e){this.setElement(e.wrapper),window.$$stock=this.model=new s,this.listenTo(this.model,"change:stockType",this.changeType),this.listenTo(this.model,"change:title",this.changeTitle),this.listenTo(this.model,"change:titleLabel",this.changeTitleLabel),this.listenTo(this.model,"change:showMask",this.renderMask),this.listenTo(this.model,"change:_reloadNum",this.reload)},render:function(){this.$el.html(this.template({title:this.model.get("title")})),this.model.fetchDHTStatus(),this.reload()},reload:function(){this.model.fetchStockType(),this.model.fetchStockOpen()},changeType:function(){var t=this,e=this.model.get("stockType"),e=this.getPagePath(e);i.async(e,function(e){t.page=new e({wrapper:t.$(".j-stock-con"),model:t.model}),t.page.render()})},getPagePath:function(e){var t={1:"none",2:"fs",3:"fs",4:"fs"};return"./shiporder/page/"+t[e]+"/"+t[e]},changeTitle:function(){var e="",e=3==+this.model.get("stockType")?$t("ERP库存管理"):$t("纷享库存管理");this.$(".j-setting-title").text(e)},changeTitleLabel:function(){var e=this.model.get("titleLabel");e?this.$(".j-setting-titlelabel").removeClass("crm-s-shiporder_hide").text(e):this.$(".j-setting-titlelabel").addClass("crm-s-shiporder_hide")},renderMask:function(){var t=this;i.async("./shiporder/page/mask/index",function(e){t.page=new e({wrapper:t.$(".b-g-con"),model:t.model}),t.page.render()})},destroy:function(){this.page&&this.page.destroy(),this.page=null,this.remove(),window.$$stock=null}})});
define("crm-setting/shiporder/shiporder/components/base",[],function(t,e,i){var s=Backbone.View.extend({initialize:function(){this.super=s.prototype,this.options=_.extend({},s.prototype.options,this.options),this.fieldAttr=this.getAttr(this.options.name),this._addEvents()},get:function(t){return this.model.get(t)},set:function(t,e){this.model.set(t,e)},getAttr:function(t){return this.get("fields")[t]||{}},isReadonly:function(){return this.fieldAttr.isReadonly},isHidden:function(){return this.fieldAttr.isHidden},disabeld:function(){this.fieldAttr.isReadonly=!0},setStatus:function(){this.isHidden()?this.$el.addClass("shiporder-set--hide"):this.$el.removeClass("shiporder-set--hide"),this.isReadonly()?this.$el.addClass("shiporder-set--disabled"):this.$el.removeClass("shiporder-set--disabled")},_addEvents:function(){this.listenTo(this.model,"change:"+this.options.name,this.changeValue)},changeValue:function(){},destroy:function(){this.$el.off(),this.stopListening(),this.model=null,this.$el.empty(),this.$el=this.el=this.options=null}});i.exports=s});
define("crm-setting/shiporder/shiporder/components/checkboxs/checkboxs",["../base","./tpl-html"],function(t,e,a){var n=t("../base");a.exports=n.extend({template:t("./tpl-html"),events:{"click .j-shiporder-checkbox":"selectHandle"},render:function(){var t=this.fieldAttr,e=this.getDatas();this.$el.html(this.template({label:t.label,options:t.options,data:e}))},_addEvents:function(){var e=this,a=[];_.each(this.fieldAttr.options,function(t){a.push(t.name),e.listenTo(e.model,"change:"+t.name,e.changeValue(t.name))})},getDatas:function(){var e=this,t=this.fieldAttr.options,a={};return _.each(t,function(t){t=t.name;a[t]=e.getData(t)}),a},formatData:function(t,e){return 1!==t?2:1},getData:function(t){return this.formatData(this.get(t))},set:function(t,e){this.model.set(t,this.formatData(e,!0))},selectHandle:function(t){var e,a;this.isReadonly()||this.isHidden()||(e=(t=$(t.currentTarget)).attr("data-name"),a=t.hasClass("shiporder-checkbox_icon--on")?2:1,this.set(e,a),this.setValue(e,a,t))},setValue:function(t,e,a){(a||this.$("[data-name="+t+"]"))[1===e?"addClass":"removeClass"]("shiporder-checkbox_icon--on")},changeValue:function(t){var e=this;return function(){e.setValue(t,e.getData(t))}},destroy:function(){this.dataList=null,this.super.destroy.call(this)}})});
define("crm-setting/shiporder/shiporder/components/checkboxs/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-set"> <label class="shiporder-set_label">' + __e(obj.label) + '</label> <div class="shiporder-checkboxs"> ';
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-checkbox"> <i class="j-shiporder-checkbox shiporder-checkbox_icon ' + ((__t = obj.data[item.name] == 1 ? "shiporder-checkbox_icon--on" : "") == null ? "" : __t) + '" data-name="' + ((__t = item.name) == null ? "" : __t) + '"></i> <span>' + __e(item.label) + "</span> </div> ";
            });
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/components/components",["./base","./base","./radio/radio","./radio/radio","./checkboxs/checkboxs","./singleselect/singleselect"],function(e,s,o){o.exports={base:e("./base"),Base:e("./base"),radio:e("./radio/radio"),Radio:e("./radio/radio"),checkboxs:e("./checkboxs/checkboxs"),singleSelect:e("./singleselect/singleselect")}});
define("crm-setting/shiporder/shiporder/components/radio/radio",["../base","./tpl-html"],function(t,e,s){var i=t("../base");s.exports=i.extend({template:t("./tpl-html"),events:{"click .shiporder-radio_set":"selectHandle"},render:function(){var t=this.fieldAttr,e=this.get(this.options.name);this.$el.html(this.template({label:t.label,options:t.options,tip:t.tip,value:e,parseTipHtml:function(t){var e="";return _.each(t,function(t){"b"===t.type?e+="<b>"+_.escape(t.label)+"</b>":e+="<p>"+_.escape(t.label)+"</p>"}),e}}))},selectHandle:function(t){var e;this.isReadonly()||this.isHidden()||(e=this.options.name,(t=+$(t.currentTarget).attr("data-value"))!==this.get(e)&&this.set(e,t))},setValue:function(t){this.options.name;this.$(".shiporder-radio_set").removeClass("on"),this.$("[data-value="+t+"]").addClass("on")},changeValue:function(){var t=this.get(this.options.name);this.setValue(t)}})});
define("crm-setting/shiporder/shiporder/components/radio/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-set"> <label class="shiporder-set_label">' + __e(obj.label) + "</label> ";
            if (obj.tip) {
                __p += ' <span class="shiporder-set_label--tip">' + ((__t = obj.tip) == null ? "" : __t) + "</span> ";
            }
            __p += ' <div class="shiporder-radios"> ';
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-radio"> <div class="shiporder-radio_set ' + ((__t = obj.value == item.value ? "on" : "") == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '"> <i class="shiporder-radio_icon"></i> <span>' + __e(item.name) + "</span> ";
                if (!_.isEmpty(item.tipHtml)) {
                    __p += ' <span class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = obj.parseTipHtml(item.tipHtml)) == null ? "" : __t) + " </div> </span> ";
                }
                __p += " </div> </div> ";
            });
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/components/singleselect/singleselect",["../base","crm-widget/select/select","./tpl-html"],function(t,e,s){var i=t("../base"),l=t("crm-widget/select/select");s.exports=i.extend({template:t("./tpl-html"),render:function(){var t=this.fieldAttr;this.$el.html(this.template({label:t.label,options:t.options})),this.initSelect()},initSelect:function(){var e=this,s=this.options.name;this.select&&this.select.destroy&&this.select.destroy(),this.select=new l({$wrap:e.$(".j-shiporder-singleselect"),width:100,options:e.getSelectOptions()}),this.select.setValue(this.get(s)),this.select.on("change",function(t){e.set(s,t)})},getSelectOptions:function(){for(var t=this.fieldAttr.range,e=[],s=t[0];s<=t[1];s++)e.push({name:s,value:s});return e},setTip:function(t){this.$(".shiporder-action_intro").text(t)},setValue:function(t,e){this.select&&this.select.setValue(t),e&&this.set(this.options.name,t)},changeValue:function(){this.setValue(this.get(this.options.name),!0)},destroy:function(){this.select&&this.select.destroy&&this.select.destroy(),this.select=null,this.super.destroy.call(this)}})});
define("crm-setting/shiporder/shiporder/components/singleselect/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="shiporder-set shiporder-set--flex"> <label class="shiporder-set_label">' + __e(obj.label) + '</label> <div class="shiporder-singleselect"> <div class="shiporder-singleselect_con j-shiporder-singleselect"></div> <p class="shiporder-action_intro">' + __e(obj.tip) + "</p> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/layout/layout",["../components/components","./switch/switch","./tpl-html"],function(t,i,e){var s=t("../components/components"),n=t("./switch/switch"),o=FS.crmUtil,h=Backbone.View.extend({template:t("./tpl-html"),mycomponents:{},dataEvents:{},events:{"click .j-reload-config":"reloadHandle","click .j-shiporder-service":"openQx","click [data-action]":"doActionHandle"},initialize:function(t){this.super=h.prototype,this.options=_.extend({},this.options,t),this.events=_.extend({},this.events,this.events),this.forms={},this.widgets={},this.model=this.options.model,this._addEvents()},render:function(){var t=this.getDescribe();this.$el.html(this.template(t)),this.initWidgest(),this.initForm()},getDescribe:function(){var t=this.options;return this.setFields(t.layout&&t.layout.fields),{loading:!1,errCode:0,topInfo:t.topInfo,layout:t.layout}},setFields:function(t){var i={};_.each(t,function(t){i[t.name]=t}),this.model.set("fields",i)},setField:function(t,i){var e=this.model.get("fields")||{};e[t]&&_.extend(e[t],i)},getField:function(t){return(this.model.get("fields")||{})[t]},initWidgest:function(){this.initSwitch()},initSwitch:function(){var e=this,t=this.options.layout||{};t.switchInfo&&(this.widgets.switchInfo&&this.widgets.switchInfo.destroy(),this.widgets.switchInfo=new n(_.extend({el:e.$(".j-action-switch").get(0)},t.switchInfo)),this.widgets.switchInfo.render(),this.widgets.switchInfo.on("switch",function(t,i){e.switchHandle(t,i)}))},switchHandle:function(t,i){},initForm:function(){var e=this.model,n=this.forms,o=this.mycomponents;this.$(".j-shiporder-comp-wrap").each(function(){var t=$(this).data(),i=o[t.name]||o[t.type]||s[t.type];i&&((i=new i({el:this,model:e,type:t.type,name:t.name})).render(),i.setStatus&&i.setStatus(),n[t.name]=i)})},showTip:function(t,i){i?o.remind(i,t||$t("操作成功")):o.alert(t||$t("操作失败请稍后尝试或联系纷享客服"))},_doLog:function(t){CRM.util.uploadLog("crmsetting","s-rule",{eventId:{delivery:"ondeliverynoteobj",stock:"onstockobj"}[t],eventType:"cl"})},showLoading:function(){this.$(".j-action-loading").show(),this.$(".j-action-error").hide(),this.$(".j-fields").hide()},hideLoading:function(){this.$(".j-action-loading").hide()},showErrMsg:function(){this.$(".j-action-loading").hide(),this.$(".j-action-error").show(),this.$(".j-fields").hide()},hideErrMsg:function(){this.$(".j-action-error").hide()},showContent:function(){this.hideLoading(),this.hideErrMsg(),this.$(".j-fields").show()},_addEvents:function(){_.isEmpty(this.modelEvents)||this.listenTo(this.model,this.modelEvents)},reloadHandle:function(){this.render()},openQx:function(){setTimeout(function(){FS.MEDIATOR.trigger("qx.shareGroup.open")},0)},doActionHandle:function(t){t=$(t.target).attr("data-action");this.doAction(t)},doAction:function(t){},destroy:function(){this.$el.off(),this.$el.empty(),_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),_.each(this.forms,function(t){t&&t.destroy&&t.destroy()}),this.widgets=this.forms=null}});e.exports=h});
define("crm-setting/shiporder/shiporder/layout/switch/switch",["./tpl-html","./type-html"],function(t,e,i){i.exports=Backbone.View.extend({template:t("./tpl-html"),typeTemplate:t("./type-html"),options:{label:"",type:"off",desc:$t("已启用"),unopenBtn:$t("开启")},events:{"click .j-switch":"actionHandle"},render:function(){this.$el.html(this.template(this.options))},renderType:function(){this.$(".switch_oprate").html(this.typeTemplate(this.options))},setType:function(t,e){this.options.type=t,e&&("info"===t&&(this.options.desc=e),"unopnen"===t)&&(this.options.unopenBtn=e),this.renderType()},setTip:function(t){this.$(".j-switch-tip").html(t)},actionHandle:function(t){var t=$(t.currentTarget),e=t.attr("data-type");this.trigger("switch",e,t)},destroy:function(){this.$el.remove()}})});
define("crm-setting/shiporder/shiporder/layout/switch/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="shiporder-switch"> <label class="switch_label">' + __e(obj.label) + '</label> <div class="switch_oprate"> </div> </div> <div class="j-switch-tip"></div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/layout/switch/type-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (obj.type === "info") {
                __p += " <span>" + __e(obj.desc) + "</span> ";
            }
            __p += " ";
            if (obj.type === "off") {
                __p += ' <i class="oprate-btn_switch j-switch" data-type="off"></i> ';
            }
            __p += " ";
            if (obj.type === "on") {
                __p += ' <i class="oprate-btn_switch oprate-btn_switch--on j-switch" data-type="on"></i> ';
            }
            __p += " ";
            if (obj.type === "unopen") {
                __p += ' <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-switch" data-type="unopen">' + __e(obj.unopenBtn) + "</a> ";
            }
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/layout/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-con_box"> ';
            if (obj.topInfo) {
                __p += ' <div class="crm-intro"> ';
                if (_.isString(obj.topInfo.messages)) {
                    __p += " " + __e(obj.topInfo.messages) + " ";
                } else {
                    __p += " <h3>" + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> ";
                    _.each(obj.topInfo.messages, function(msg, index) {
                        __p += " <li>" + ((__t = index + 1) == null ? "" : __t) + ". " + __e(msg) + "</li> ";
                    });
                    __p += " </ul> ";
                    if (obj.hasService) {
                        __p += ' <a class="fs-service j-shiporder-service" href="javascript:;">' + ((__t = $t("客服")) == null ? "" : __t) + "</a> ";
                    }
                    __p += " ";
                }
                __p += " </div> ";
            }
            __p += ' <div class="so-actions"> <div class="crm-loading tab-loading j-action-loading" style="' + ((__t = obj.loading ? "" : "display: none;") == null ? "" : __t) + '"></div> <p class="j-action-error" style="' + ((__t = obj.errCode !== 0 ? "" : "display: none;") == null ? "" : __t) + '"> <span class="so-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + '</a> </p> <div class="j-fields" style="' + ((__t = !obj.loading && obj.errCode === 0 ? "" : "display: none;") == null ? "" : __t) + '"> ';
            if (obj.layout.switchInfo) {
                __p += ' <div class="j-action-switch"></div> ';
            }
            __p += ' <div class="so-settings-wrapper j-settings"> ';
            _.each(obj.layout.fields, function(b) {
                __p += ' <div class="j-shiporder-comp-wrap" data-type="' + ((__t = b.type) == null ? "" : __t) + '" data-name="' + ((__t = b.name) == null ? "" : __t) + '"></div> ';
            });
            __p += ' <div class="shiporder-btns"> ';
            _.each(obj.layout.buttons, function(btn) {
                __p += ' <a href="javascript:;" class="crm-btn crm-btn-primary j-layout-btn" data-action="' + ((__t = btn.action) == null ? "" : __t) + '" style="' + ((__t = btn.show ? "" : "display: none;") == null ? "" : __t) + '">' + ((__t = btn.label) == null ? "" : __t) + "</a> ";
            });
            __p += " </div> </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/model/api",["crm-modules/common/stock/api"],function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var c=FS.crmUtil,o=e("crm-modules/common/stock/api");t.default={_queryApi:function(r){var o=this,e=_.extend({url:"",type:"post",success:function(e){var t;0==e.Result.StatusCode?t=e.Value:o.set({errCode:500}),r.successCb&&r.successCb(t)}},r||{});return c.FHHApi(_.omit(e,"successCb"),{errorAlertModel:2})},_operateApi:function(e){var t=_.extend({url:"",type:"post"},e||{}),e={submitSelector:t.submitSelector};return e.errorAlertModel=t.errorAlertModel||2,t.successCb&&!t.success&&(t.success=function(e){0==e.Result.StatusCode&&t.successCb(e.Value)}),c.FHHApi(_.omit(t,"errorAlertModel","submitSelector","successCb"),e)},_commonApi:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=_.extend({url:"",type:"post",errorCb:$.noop},e),o=t.errorAlertModel||r.errorAlertModel||2;return t.errorAlertModel=1,r.successCb&&!r.success&&(r.success=function(e){0==e.Result.StatusCode?r.successCb(e.Value):(1!==o&&c.alert(e.Result.FailureMessage),r.errorCb(e))}),r.error||(r.error=function(){r.errorCb()}),c.FHHApi(_.omit(r,"errorAlertModel","submitSelector","successCb","errorCb"),t)},requestManySections:function(t,r,u){return new Promise(function(o,s){var c=_.clone(r||{}),i=_.isFunction(u)?u:function(e){return{resultCode:e.resultCode,token:e.token}};function e(){t(c).then(function(e){var t=i(e),r=t.resultCode;0==+r?(c.token=t.token,n()):1==+r?o(e):s({errCode:2,errMsg:e.errMessage||e.errMsg||"请求错误"})},s)}function n(){c.token?setTimeout(e,3e3):e()}n()})},fetchStockType:function(r){var o=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/stock/service/query_enabled_stock_type",successCb:function(e){var t;e&&(t=+e.stockType,o.set({hasStockPackage:!!e.purchaseStockModuleLicense,stockType:t,isForErp:e.isForErp}),r)&&r(t)}})},fetchStockOpen:function(){var t=this;return c.FHHApi({url:"/EM1HNCRM/API/v1/object/stock/service/is_purchase_stock_license",type:"post",success:function(e){e&&e.Value&&t.set({isPurchase:e.Value.isPurchase})}})},fetchDHTStatus:function(){var t=this;return c.FHHApi({url:"/FHH/EM1HSailAdmin/sail-admin/config/isDHTOpen",type:"post",success:function(e){e.Error||t.set("enableSetStockView",e.Value)}},{autoPrependPath:!1,errorAlertModel:1})},fetchDeliveryNote:function(t){var r=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/delivery_note/service/query_delivery_note_config",successCb:function(e){e&&r.set({errCode:0,deliveryStatus:+e.switchStatus||0,hasSalesOrderNeedUpdate:e.hasSalesOrderNeedUpdate,isPaid:e.displayAdvancedLogisticsQuery,expressQueryFuncId:+e.expressQueryFuncId||1,isAllowModifySalesOrderProduct:+e.isAllowModifySalesOrderProduct||1}),t&&t(r.toJSON())}})},enableDeliveryNote:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/delivery_note/service/enable_delivery_note",errorAlertModel:1},e))},setDeliveryConfig:function(e,t){var r=this.toJSON();return this._operateApi({url:"/EM1HNCRM/API/v1/object/delivery_note/service/update_delivery_note_config",data:{functionId:r.expressQueryFuncId+"",isAllowModifySalesOrderProduct:r.isAllowModifySalesOrderProduct+""},submitSelector:t,successCb:function(){e&&e()}})},fetchStock:function(t){var r=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/stock/service/query_stock_config",successCb:function(e){e&&r.set({errCode:0,stockStatus:+e.stockSwitch||1,validateOrderType:+e.validateOrderType||1,stockViewType:+e.stockViewType||1,orderWarehouseType:+e.orderWarehouseType||1,stockWarningType:+e.stockWarningType||1,isNotShowZeroStockType:+e.isNotShowZeroStockType||1,safetyStockType:+e.safetyStockType||1,salesOrderProductDecimal:+e.salesOrderProductDecimal,stockDecimal:+e.stockDecimal,poSwitch:e.isPurchaseOrderEnable?2:0,poDecimal:+e.purchaseOrderDecimal,isOnlyShowOnSaleStockType:+e.isOnlyShowOnSaleStockType||2,hasUiEventModule:!!e.hasUiEventModule,showStockForOrder:1==+e.isSalesOrderShowStock}),t&&t(r.toJSON())}})},changeStockStatus:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/stock/service/save_stock_switch",errorAlertModel:1},e))},setStockConfig:function(e,t){var r=this.toJSON();return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock/service/save_stock_config",data:{validateOrderType:r.validateOrderType,stockViewType:r.stockViewType,orderWarehouseType:r.orderWarehouseType,stockWarningType:r.stockWarningType,isNotShowZeroStockType:r.isNotShowZeroStockType,safetyStockType:r.safetyStockType,stockDecimal:r.stockDecimal,isOnlyShowOnSaleStockType:r.isOnlyShowOnSaleStockType},errorAlertModel:2,submitSelector:t,successCb:function(){e&&e()}})},enableOrderShowStock:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/stock/service/enable_sales_order_show_stock",errorAlertModel:1},e))},fetchBatch:function(t){var r=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/batch_sn/service/query_batch_sn_config",successCb:function(e){e&&r.set({batchSNSwitch:Number(e.batchSNSwitch),willExpireStockWarningType:Number(e.willExpireStockWarningType)}),t&&t(r.toJSON())}})},fetchPO:function(t){var r=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/purchase_order/service/is_purchase_order_enable",successCb:function(e){e&&r.set({errCode:0,poSwitch:+e.switchStatus||0,poDecimal:+e.purchaseOrderDecimal,stockDecimal:+e.stockDecimal}),t&&t(r.toJSON())}})},enablePO:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/purchase_order/service/enable_purchase_order",errorAlertModel:2},e))},setPOConfig:function(e,t){var r=this.toJSON();return this._operateApi({url:"/EM1HNCRM/API/v1/object/purchase_order/service/save_purchase_order_config",data:{purchaseOrderDecimal:r.poDecimal},errorAlertModel:2,submitSelector:t,successCb:function(){e&&e()}})},fetchExchangeManageConfig:function(t){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/exchange_return_note/service/query_exchange_return_note_config",errorAlertModel:2,successCb:function(e){e&&r.set({errCode:0,stockStatus:e.isStockEnable?2:1,exchangeReturnNoteStatus:+e.exchangeReturnNoteSwitch||1}),t&&t(r.toJSON())}})},enableExchangeReturn:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/exchange_return_note/service/enable_exchange_return_note",errorAlertModel:1},e)).done(function(e){e.Value||c.remind(3,e.Result.FailureMessage)})},fetchErpInfo:function(t){var r=this;return this._queryApi({url:"/EM1HNCRM/API/v1/object/erp_stock_biz/service/query_erp_stock_config",successCb:function(e){e&&r.set({errCode:0,erpStock:e.enable,erpValidateOrderType:+e.validateOrderType||2,erpIsNotShowZeroStockType:+e.isNotShowZeroStockType||2,erpStockDecimal:+e.stockDecimal,validateOrderType:e.validateOrderType,isNotShowZeroStockType:e.isNotShowZeroStockType,stockDecimal:e.stockDecimal}),t&&t(e)}})},enableErpStock:function(t,e){return this._operateApi({url:"/EM1HNCRM/API/v1/object/erp_stock_biz/service/enable_erp_stock",submitSelector:e,successCb:function(e){t&&t(e.enableStatus)}})},closeErpStock:function(t,e){return this._operateApi({url:"/EM1HNCRM/API/v1/object/erp_stock_biz/service/close_erp_stock",submitSelector:e,successCb:function(e){t&&t(e.enableStatus)}})},setErpStockConfig:function(e,t){var r=this.toJSON();return this._operateApi({url:"/EM1HNCRM/API/v1/object/erp_stock_biz/service/save_erp_stock_config",data:{validateOrderType:r.validateOrderType,isNotShowZeroStockType:r.isNotShowZeroStockType,stockDecimal:r.stockDecimal},submitSelector:t,successCb:function(){e&&e()}})},initShipOrder:function(e){var o=this;return this.requestManySections(function(e){return new Promise(function(t,r){o._commonApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/async_init",data:e,successCb:function(e){t(e)},errorCb:function(e){r(e)}})})},e).then(function(e){return c.remind(1,$t("操作成功")),e})},getQueryAllConfigData:function(){var t=this;return new Promise(function(e){t._queryApi({url:"/EM1HNCRM/API/v1/object/biz_conf/service/query_all_config_data",data:{bizType:"record_type",describeApiName:"SalesOrderObj"},errorAlertModel:2,successCb:e},{autoPrependPath:!1})})},initOrderRecordTypeRelateDeliveryMode:function(){var t=this;return new Promise(function(e){t._queryApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/init_order_warehouse_type_for_order_record_type",data:{},errorAlertModel:2,successCb:e},{autoPrependPath:!1})})},getBaseConfig:function(){var s=this;return new Promise(function(o){s._queryApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/get_base_config",errorAlertModel:2,successCb:function(e){var t=Number(e.stockSwitch)||1,r=s.toJSON().titleLabel,r=4==e.stockType?$t("crm.发货单"):1==e.isErpStockEnable?$t("对接版·C类"):e.isForErp?$t("对接版·B类"):$t("完整版·A类");e.stockStatus=t,s.set({isErpStockEnable:e.isErpStockEnable,isAllowModifySalesOrderProduct:e.isAllowModifySalesOrderProduct,orderWarehouseType:e.orderWarehouseType,validateOrderType:e.validateOrderType,isNotShowZeroStockType:e.isNotShowZeroStockType,isOnlyShowOnSaleStockType:e.isOnlyShowOnSaleStockType,safetyStockType:e.safetyStockType,salesOrderProductDecimal:e.salesOrderProductDecimal,stockDecimal:e.stockDecimal,purchaseOrderDecimal:e.purchaseOrderDecimal,deliveryNoteDecimal:e.deliveryNoteDecimal,isPurchaseOrderEnable:e.isPurchaseOrderEnable,stockSwitch:t,stockStatus:t,isForErp:e.isForErp,titleLabel:r,hasUiEventModule:e.hasUiEventModule,isSalesOrderShowStock:e.isSalesOrderShowStock,stockViewType:e.stockViewType,expressFuncCode:e.expressFuncCode,allowOrderWarehouseBlank:e.allowOrderWarehouseBlank,batchSNSwitch:e.batchSNSwitch,orderWarehouseTypeConfigRangeCode:e.orderWarehouseTypeConfigRangeCode,stockCheckNoteUpdateStockType:e.stockCheckNoteUpdateStockType,requisitionNoteAutomaticInbound:e.requisitionNoteAutomaticInbound,requisitionNoteAutomaticOutbound:e.requisitionNoteAutomaticOutbound}),s.initVersion(),o(e)}},{autoPrependPath:!1})})},setOrderWarehouseType:function(e,r,o){var s=this;return new Promise(function(t){s._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_order_warehouse_type",data:e,submitSelector:r,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),o&&o(),t(e)}})})},setValidateOrderType:function(e,t,r){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_validate_order_type",data:{validateOrderType:e},submitSelector:t,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),r&&r()}})},setShowStockType:function(e,t,r){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_show_stock_type",data:{isNotShowZeroStockType:e.isNotShowZeroStockType,isOnlyShowOnSaleStockType:e.isOnlyShowOnSaleStockType},submitSelector:t,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),r&&r()}})},setSafetyStockType:function(e,t,r){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_safety_stock_type",data:{safetyStockType:e},submitSelector:t,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),r&&r()}})},setStockWarningType:function(e,t,r){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_stock_warning_type",data:{stockWarningType:e},submitSelector:t,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),r&&r()}})},enableSalesOrder:function(e){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/enable_sales_order_show_stock",errorAlertModel:2,successCb:function(){c.remind(1,$t("操作成功")),e&&e()}})},getReceivingInfo:function(t){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/get_automatic_receiving_conf",errorAlertModel:1,successCb:function(e){e={daysAfterShipment:e.daysAfterShipment,autoReceiveStatus:e.status};r.set(e),t&&t(r.toJSON())}})},setReceiving:function(e,t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_automatic_receiving",data:e,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),t&&t()}})},setDecimal:function(e,t,r){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_decimal",data:e,submitSelector:t,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),r&&r(e)}})},getScanInfo:function(t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_scan_code/service/query_scan_code_config",errorAlertModel:1,successCb:function(e){t&&t(e.scanCodeType)}})},setScanInfo:function(e,t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_scan_code/service/save_scan_code_config",data:{scanCodeType:e},errorAlertModel:2,successCb:function(){c.remind(1,$t("操作成功")),t&&t()}})},setExpressFun:function(e){return this._operateApi({url:"/EM2HNCRM/API/v1/object/stock_module/service/set_express_func",data:{expressFuncCode:e},errorAlertModel:2,successCb:function(){c.remind(1,$t("操作成功"))}})},initStockData:function(e,t){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_special_operation/service/delete_all_object_data",errorAlertModel:2,submitSelector:t,successCb:function(){c.remind(1,$t("操作成功")),e&&e()}}).then(function(e){e=e.Result;if(1035!==e.FailureCode)return c.remind(3,e.FailureMessage);r.queryStockStatus()})},getStockData:function(t,e){return o.getStockConfigs(e||["dht_stock_switch","delivery_note_status","purchase_order_status","dht_exchange_return_note_switch"]).then(function(e){return t&&t(e),e})},getPrivilege:function(t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_special_operation/service/get_privilege",errorAlertModel:1,successCb:function(e){t&&t(e)}})},changeStockType:function(t,e){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_special_operation/service/change_stock_type",errorAlertModel:2,data:{tenantId:String(CRM.enterpriseId)},submitSelector:e,successCb:function(){c.remind(1,$t("操作成功")),t&&t()}}).then(function(e){e=e.Result;if(1035!==e.FailureCode)return c.remind(3,e.FailureMessage);r.queryStockStatus(t)})},isShowStockLoading:function(){var t=this;this.isStockAvailable(function(e){e&&e.result&&t.queryStockStatus()})},isStockAvailable:function(t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_special_operation/service/is_special_operation_working",errorAlertModel:2,successCb:function(e){t&&t(e)}})},restartStock:function(t){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_special_operation/service/restart_stock",errorAlertModel:2,successCb:function(e){t&&t(e)}}).then(function(e){e=e.Result;if(1035!==e.FailureCode)return c.remind(3,e.FailureMessage);r.queryStockStatus(function(){t&&t()})})},queryStockStatus:function(t){var r=this,o=0,s=(r.set({maskTips:"正在进行高危操作...",showMask:!0}),setInterval(function(){r.isStockAvailable(function(e){o++,e.result&&10!==o||(clearInterval(s),r.set({showMask:!1}),c.remind(1,$t("操作成功")),t&&t())})},1e4))},setEditOrder:function(e,t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_is_allow_modify_sales_order_product",data:e,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),t&&t()}})},setStockCheckNoteUpdateStockType:function(e,t){return this._operateApi({url:"/EM1HNCRM/API/v1/object/stock_module/service/set_stock_check_note_update_stock_type",data:e,errorAlertModel:2,successCb:function(e){c.remind(1,$t("操作成功")),t&&t()}})},getCPQDeliveryWay:function(e){return o.getCpqDeliveryWay()},setStockConfigs:function(e,t,r){return this._operateApi({url:"/EM2HNCRM/API/v1/object/stock_config/service/set_config_value",data:{key:e,value:String(t),oldValue:""},errorAlertModel:2,successCb:function(e){r&&r(e)}})},enableStockSaleMulti:function(t){return this._operateApi({url:"/EM2HNCRM/API/v1/object/stock_multi_unit/service/enable_stock_sales_module_optimize",errorAlertModel:2,successCb:function(e){t&&t(e)}})},enableParentWareHouse:function(t){var r=this;return this._operateApi({url:"/EM2HNCRM/API/v1/object/warehouse/service/open_ware_position",errorAlertModel:2,successCb:function(e){e.result&&(r.set("isOpenWareHousePosition",!0),t)&&t(e)}})}}});
define("crm-setting/shiporder/shiporder/model/format",[],function(e,r,a){Object.defineProperty(r,"__esModule",{value:!0}),r.default={setOrderRecordTypeRelateDeliveryMode:function(e){var a,i,o;if(e&&e.configDataMap&&e.configDataMap.order_record_type_relate_delivery_mode&&e.bizTypeValueList)return a={1:"单一仓库订货",2:"合并仓库订货",3:"无仓库订货"},i=e.configDataMap.order_record_type_relate_delivery_mode,o={},e.bizTypeValueList.forEach(function(r){var e=i.find(function(e){return e.bizTypeValue===r.apiName});o[r.apiName]=Object.assign({configLabel:a[Number(e.configValue)]},r,e)}),this.set("orderRecordTypeRelateDeliveryMode",o)}}});
define("crm-setting/shiporder/shiporder/model/model",["./api","./format"],function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var i=e("./api"),e=e("./format"),o={_reloadNum:0,title:$t("stock.stock_management.stock_management"),titleLabel:"",hasStockPackage:!1,isPurchase:!1,stockType:0,isForErp:!1,isErpStockEnable:!1,fields:null,deliveryStatus:0,deliveryErrMsg:$t("开启发货单失败请重试。如有疑问请联系纷享客服400-1869-000"),isPaid:!1,expressQueryFuncId:1,isAllowModifySalesOrderProduct:1,deliveryNoteDecimal:2,stockSwitch:1,stockStatus:1,hasUiEventModule:!1,showStockForOrder:!1,validateOrderType:1,orderWarehouseType:1,stockViewType:1,enableSetStockView:!1,stockWarningType:1,isNotShowZeroStockType:2,isOnlyShowOnSaleStockType:1,safetyStockType:1,salesOrderProductDecimal:2,stockDecimal:2,scanCodeType:1,batchSNSwitch:1,willExpireStockWarningType:"1",poSwitch:0,poDecimal:2,exchangeReturnNoteStatus:1,erpStock:!1,erpValidateOrderType:2,erpIsNotShowZeroStockType:2,erpStockDecimal:2,daysAfterShipment:7,autoReceiveStatus:2,autoConfirmReceiptType:1,showMask:!1,maskTips:$t("正在加载..."),isOpenWareHousePosition:!1,cpqDeliveryWay:2,hasMultiOrderDeliveryGray:!1,multiOrderDeliverySwitch:2,orderRecordTypeRelateDeliveryMode:{},kingDeeK3CSyncPluginSwitch:2,kingDeeK3CSyncType:null,isUpstreamEnterprise:!1,isDownstreamEnterprise:!1,upstreamDisplayStockInterconnection:!1,downstreamDisplayStockInterconnection:!1,autoGenerateConfig:0,currentIdentity:"none",correspondingUpstreamList:[],negativeInventorySwitch:"2",negative_inventory_allowed_plugin_switch:!1,openMultiSn:"1",stock_instant_available_quantity_switch:"1",delivery_note_returned_goods_switch:"1",non_note_returned_goods_switch:"1",delivery_note_interaction_model:"1",stock_order_product_fill:"1",isVersionA:!1,isVersionB:!1,isVersionDeliveryOnly:!1,SSstatus:"1",SSupStreamSigner:"0",SSdownStreamSigner:"0",SSprintTemplateId:"",SSsignAppType:"",return_goods_invoice_of_sales_order_type:"multiple",purchase_order_calculation:"1",stock_check_note_product_source_type:"1",delivery_stock_related_check:"1",cost_management_calculate:"1",isDistributionStockSupportMultiOrderingEnable:!1,enable_edit_purchase_order_when_in_stock:"1"},o=Object.assign(Object.assign({defaults:o,getAllConfigData:function(){var r=this;return 2!==this.get("stockStatus")?Promise.resolve():this.getQueryAllConfigData().then(function(t){return new Promise(function(e){t.configDataMap&&t.configDataMap.order_record_type_relate_delivery_mode?e(t):r.initOrderRecordTypeRelateDeliveryMode().then(function(){e(r.getQueryAllConfigData())})})})},initVersion:function(){this.set({isVersionA:!1,isVersionB:!1,isVersionDeliveryOnly:!1}),1===this.get("stockSwitch")?this.set("isVersionDeliveryOnly",!0):this.get("isForErp")?this.set("isVersionB",!0):this.set("isVersionA",!0)},reload:function(){this.set("_reloadNum",this.get("_reloadNum")+1)},getWarningTypeDesc:function(){var e,t=this.get("stockWarningType");if(this.get("recordRemindIsOpen"))switch(t){case"1":e=$t("stock.stock_manage.warn.title");break;case"2":e=$t("stock.stock_manage.warn.text_4");break;case"3":e=$t("stock.stock_manage.warn.text_5");break;default:e=$t("stock.stock_manage.warn.text_6")}else switch(t){case"1":e=$t("stock.stock_manage.warn.text_no");break;case"2":e=$t("stock.stock_manage.warn.text_1");break;case"3":e=$t("stock.stock_manage.warn.text_2");break;default:e=$t("stock.stock_manage.warn.text_3")}return e}},i.default),e.default);t.default=Backbone.Model.extend(o)});
define("crm-setting/shiporder/shiporder/page/advance/index",["crm-widget/dialog/dialog","crm-modules/common/stock/api","./tpl-html","./switchTpl-html","./stopstock-html"],function(t,e,n){var i=FS.crmUtil,o=t("crm-widget/dialog/dialog"),c=t("crm-modules/common/stock/api");n.exports=Backbone.View.extend({template:t("./tpl-html"),switchTpl:t("./switchTpl-html"),stopStockTpl:t("./stopstock-html"),events:{"click .j-init-data":"_handleInitData","click .j-enable-purchase":"_handleEnablePurchase","click .j-enable-delivery":"_handleEnableDelivery","click .j-enable-exchange":"_handleEnableExchangeReturn","click .j-switch-stock":"_handleSwitchStock","click .j-stock-status":"_showStockOptions","click .j-stock-option":"_handleSwitchStockStatus","click .j-restart-stock":"_handleRestartStock"},isLoading:!1,initialize:function(){this.model.isShowStockLoading()},render:function(){var e=this,n=this.model.toJSON();this.model.getPrivilege(function(t){t=t.privilegeVO||{};_.extend(n,t),e.model.set("canDeleteAllStockData",t.canDeleteAllStockData),e.model.set("canChangeStockType",t.canChangeStockType),e.model.set("canRestartStock",t.canRestartStock),e.doRender(n)}).done(function(){e.renderStockData()})},doRender:function(t){t=t||this.model.toJSON();this.$el&&this.$el.html(this.template(t))},renderStockData:function(){var n=this;this.model.getStockData(function(t){var e=Number(t.dht_stock_switch)||0;n.model.set("stockStatus",e),t.canRestartStock=n.model.get("canRestartStock"),t.isForErp=n.model.get("isForErp"),t.canRestartStock&&3===e&&(t.tips=t.isForErp?"重启库存模块将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单。":"重启库存模块将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细。"),n.doRender(),$(".j-advance_switch").html(n.switchTpl(_.extend({},t,{stockType:n.model.get("stockType"),isPurchase:n.model.get("isPurchase")})))})},_handleInitData:function(t){var n=this,e=this.model.get("isForErp"),o="",s=i.confirm((o+="<p>您将进行库存模块的数据初始化操作，请确认您已知晓以下注意事项:</p>")+(e?'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>':'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>')+"<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>",$t("数据初始化"),function(){var e=$(t.currentTarget);s.destroy(),n.model.set("showMask",!0),c.initStockData.circle({submitSelector:$(t.currentTarget)},function(t,e){1==t.resultCode&&e(t)}).res(function(t){n.isLoading=!1,n.model.set("showMask",!1);t=t.Value&&t.Value.Result.FailureMessage||null;t?FS.crmUtil.remind(3,t):FS.crmUtil.remind(1,$t("操作成功")),e.removeClass("crm-btn-primary").addClass("crm-btn-disabled")}).catch(function(t){n.isLoading=!1,n.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))})})},_handleEnablePurchase:function(t){var e;this.isLoading||(this.isLoading=!0,(e=this).model.set("showMask",!0),c.enablePO.circle({submitSelector:$(t.currentTarget)},function(t,e){1==t.resultCode&&e(t)}).res(function(t){e.isLoading=!1,e.model.set("showMask",!1);t=t.Value&&t.Value.Result.FailureMessage||null;t?FS.crmUtil.remind(3,t):FS.crmUtil.remind(1,$t("操作成功")),e.render()}).catch(function(t){e.isLoading=!1,e.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))}))},_handleEnableDelivery:function(t){var e=this;this.model.enableDeliveryNote({submitSelector:$(t.currentTarget)}).done(function(){e.renderStockData()})},_handleEnableExchangeReturn:function(t){var e;this.isLoading||(this.isLoading=!0,this.model.set("showMask",!0),e=this,c.enableExchangeReturn.circle({submitSelector:$(t.currentTarget)},function(t,e){1==t.resultCode&&e(t)}).res(function(t){e.isLoading=!1,e.model.set("showMask",!1);t=t.Value&&t.Value.Result.FailureMessage||null;t?FS.crmUtil.remind(3,t):FS.crmUtil.remind(1,$t("操作成功")),e.renderStockData()}).catch(function(t){e.isLoading=!1,e.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))}))},_handleSwitchStock:function(t){var e=this,n=e.model.get("isForErp"),o="",s=i.confirm((o+="<p>您将进行库存模式切换的操作，请确认您已知晓以下注意事项:</p>")+(n?'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>':'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>')+"<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>",$t("stock.stock_manage.info.text1"),function(){s.destroy(),e.model.set("showMask",!0),c.changeStockType.circle({submitSelector:$(t.currentTarget)},function(t,e){1==t.resultCode&&e(t)}).res(function(t){e.model.set("showMask",!1);t=t.Value&&t.Value.Result.FailureMessage||null;t?FS.crmUtil.remind(3,t):FS.crmUtil.remind(1,$t("操作成功")),e.model.set("isForErp",!n),e.render()}).catch(function(t){e.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))})})},_showStockOptions:function(){var t=$(".j-stock-options");t.is(":hidden")?t.show():t.hide()},_handleSwitchStockStatus:function(t){var t=$(t.currentTarget),e=!!t.data("type"),t=t.data("status"),n=this;if(3==+t)return this._handleStopStock();this.isLoading||(this.isLoading=!0,n.model.set("showMask",!0),c.changeStockStatus.circle({stockSwitch:t,isForErp:e},function(t,e){1==t.resultCode&&e(t)}).res(function(t){n.isLoading=!1,n.model.set("showMask",!1);t=t.Value&&t.Value.Result.FailureMessage||null;t?FS.crmUtil.remind(3,t):FS.crmUtil.remind(1,$t("操作成功")),n.render()}).catch(function(t){n.isLoading=!1,n.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))}))},_handleStopStock:function(){function e(){s&&clearInterval(s),t&&t.destroy&&t.destroy(),t=s=null}var s,n=this,t=new o({title:$t("库存停用"),classPrefix:"crm-c-dialog crm-c-dialog-stopstock",width:650,showBtns:!0,showScroll:!1,content:n.stopStockTpl()});t.on("hide",e),t.on("dialogCancel",e),t.on("dialogEnter",function(t){n.model.set("showMask",!0),c.changeStockStatus.circle({stockSwitch:3,isForErp:n.model.get("isForErp")},function(t,e){1==t.resultCode&&e(t)}).res(function(t){e(),FS.crmUtil.remind(1,$t("操作成功")),n.model.set("showMask",!1),n.render()}).catch(function(t){n.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))})}),t.on("agree",function(t){var e,t=$(t.target);this._canAgree&&(t.toggleClass("on"),e=$('[action-type="dialogEnter"]',this.element),t.hasClass("on")?e.removeClass("btn-disabled"):e.addClass("btn-disabled"))}),t.on("show",function(t){var e=this,n=(e._canAgree=!1,$('[action-type="dialogEnter"]',e.element).addClass("btn-disabled"),30),o=$(".stopstock-countdown",e.element);s&&clearInterval(s),s=setInterval(function(){0===--n?(e._canAgree=!0,$(".j-tips-checkbox",e.element).removeAttr("disabled"),s&&clearInterval(s),s=null,o.text("")):o.text(n+"s")},1e3)}),t.show()},_handleRestartStock:function(t){var e=this,n=e.model.get("isForErp"),o="",s=i.confirm((o+="<p>您将进行库存模块重启的操作，请确认您已知晓以下注意事项:</p>")+(n?'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、退换货单等对象的数据将会被完全清除，不可恢复。</p>':'<p style="margin: 20px 0;">1. 仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细等对象的数据将会被完全清除，不可恢复。</p>')+"<p>2. 清除库存数据如果导致部分订单数据出现异常，将不会针对这部分订单数据做任何修复。</p>",$t("stock.stock_manage.info.text2"),function(){s.destroy(),e.model.set("showMask",!0),c.restartStock.circle({},function(t,e){1==t.resultCode&&e(t)}).res(function(t){FS.crmUtil.remind(1,$t("操作成功")),e.model.set("showMask",!1),e.render()}).catch(function(t){e.model.set("showMask",!1),FS.crmUtil.remind(3,$t(t||"启用失败请稍后刷新重试或联系纷享客服"))})})},destroy:function(){this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null}})});
define("crm-setting/shiporder/shiporder/page/advance/stopstock-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="stopstock-content"> <p>' + ((__t = $t("库存停用前请确保您已知晓停用库存的结果如下")) == null ? "" : __t) + "</p> <p>1." + ((__t = $t("库存停用后相关对象仍然可用。")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("开启库存后创建的历史订单数据仍然按照库存开启时的逻辑处理。")) == null ? "" : __t) + "</p> <p>3." + ((__t = $t("停用后创建订单时不再校验库存创建发货单也不再扣减实际库存。")) == null ? "" : __t) + "</p> <p>4." + ((__t = $t("库存停用后不可再手动启用。")) == null ? "" : __t) + '</p> <p><i class="stopstock-checkbox" action-type="agree"></i><span class="stopstock-countdown">30s</span>' + ((__t = $t("我已知晓停用库存的结果并确定这些结果不会影响业务的正常流转。")) == null ? "" : __t) + "</p> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/advance/switchTpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-advance_item-title"> <span>' + ((__t = $t("功能开关")) == null ? "" : __t) + "</span> ";
            if (obj.tips) {
                __p += ' <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = $t(obj.tips)) == null ? "" : __t) + " </div> </div> ";
            }
            __p += ' </div> <div class="shiporder-advance_item-content-container"> <div class="shiporder-advance_item-content width-border minus-20"> <div class="shiporder-advance_item-info"> <div class="shiporder-advance_item-info-title"> <span class="info-title">' + ((__t = $t("发货单")) == null ? "" : __t) + "</span> ";
            if (obj.delivery_note_status == 2) {
                __p += ' <i class="icon-status icon-selected"></i>' + ((__t = $t("已开启")) == null ? "" : __t) + " ";
            } else {
                __p += ' <i class="icon-status icon-minus"></i>' + ((__t = $t("未开启")) == null ? "" : __t) + " ";
            }
            __p += " </div> ";
            if (obj.delivery_note_status == 2) {
                __p += ' <p class="tips">' + ((__t = $t("已初始化发货单对象")) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <p class="tips">' + ((__t = $t("开启后，会初始化发货单对象")) == null ? "" : __t) + "</p> ";
            }
            __p += ' </div> <div class="shiporder-advance_item-btn"> ';
            if (obj.delivery_note_status == 0) {
                __p += ' <div class="crm-btn crm-btn-primary j-enable-delivery">' + ((__t = $t("启用")) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> </div> <div class="shiporder-advance_item-content"> <div class="shiporder-advance_item-info"> <div class="shiporder-advance_item-info-title"> <span class="info-title">' + ((__t = $t("库存")) == null ? "" : __t) + "</span> ";
            if (obj.dht_stock_switch == 1) {
                __p += ' <i class="icon-status icon-minus"></i>' + ((__t = $t("未开启")) == null ? "" : __t) + " ";
            } else if (obj.dht_stock_switch == 2) {
                __p += ' <i class="icon-status icon-selected"></i>' + ((__t = $t("已开启")) == null ? "" : __t) + " ";
            } else {
                __p += ' <i class="icon-status icon-minus"></i>' + ((__t = $t("已停用")) == null ? "" : __t) + " ";
            }
            __p += " ";
            if (obj.isPurchase == false) {
                __p += ' <i class="icon-status icon-minus" style="margin-left: 24px"></i>' + ((__t = "License" + $t("已过期")) == null ? "" : __t) + " ";
            }
            __p += " </div> ";
            if (obj.dht_stock_switch == 2) {
                __p += ' <p class="tips">' + ((__t = obj.isForErp ? $t("已初始化仓库、库存两个对象") : $t("已初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细等对象")) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <p class="tips">' + ((__t = obj.isForErp ? $t("开启后，会初始化仓库、库存两个对象") : $t("开启后，会初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细等对象")) == null ? "" : __t) + "</p> ";
            }
            __p += ' </div> <div class="shiporder-advance_item-btn"> ';
            if (obj.dht_stock_switch == 1 && obj.isPurchase) {
                __p += ' <div class="crm-btn j-stock-status crm-btn-primary">' + ((__t = $t("启用库存")) == null ? "" : __t) + '</div> <div class="crm-stock-options j-stock-options"> <div class="crm-stock-option j-stock-option" data-type="false" data-status="2">' + ((__t = $t("开启完整版库存")) == null ? "" : __t) + '</div> <div class="crm-stock-option j-stock-option" data-type="true" data-status="2">' + ((__t = $t("开启对接版库存")) == null ? "" : __t) + "</div> </div> ";
            } else if (obj.dht_stock_switch == 2) {
                __p += ' <div class="crm-btn j-stock-option crm-btn-primary" data-status="3">' + ((__t = $t("停用库存")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="crm-btn j-restart-stock ' + ((__t = obj.canRestartStock ? "crm-btn-primary" : "crm-btn-disabled") == null ? "" : __t) + '" data-status="3">' + ((__t = $t("重启库存")) == null ? "" : __t) + "</div> ";
            }
            __p += " </div> </div> ";
            if (obj.dht_stock_switch == 2) {
                __p += " ";
                if (!obj.isForErp) {
                    __p += ' <div class="shiporder-advance_item-content shiporder-advance_item-child"> <div class="shiporder-advance_item-info"> <div class="shiporder-advance_item-info-title"> <span class="info-title">' + ((__t = $t("采购")) == null ? "" : __t) + "</span> ";
                    if (obj.purchase_order_status == 2) {
                        __p += ' <i class="icon-status icon-selected"></i>' + ((__t = $t("已启用")) == null ? "" : __t) + " ";
                    } else {
                        __p += ' <i class="icon-status icon-minus"></i>' + ((__t = $t("未启用")) == null ? "" : __t) + ' <span class="tips">' + ((__t = $t("必须先开启库存")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " </div> ";
                    if (obj.purchase_order_status == 2) {
                        __p += ' <p class="tips">' + ((__t = $t("已初始化采购订单、采购退货单等对象")) == null ? "" : __t) + "</p> ";
                    } else {
                        __p += ' <p class="tips">' + ((__t = $t("开启后，会初始化采购订单、采购退货单等对象")) == null ? "" : __t) + "</p> ";
                    }
                    __p += ' </div> <div class="shiporder-advance_item-btn"> ';
                    if (+obj.purchase_order_status !== 2) {
                        __p += ' <div class="crm-btn crm-btn-primary j-enable-purchase">' + ((__t = $t("启用")) == null ? "" : __t) + "</div> ";
                    }
                    __p += " </div> </div> ";
                }
                __p += ' <div class="shiporder-advance_item-content shiporder-advance_item-child"> <div class="shiporder-advance_item-info"> <div class="shiporder-advance_item-info-title"> ';
                if (obj.dht_exchange_return_note_switch == 1) {
                    __p += ' <!-- <i class="icon-status icon-minus"></i>' + ((__t = $t("未启用")) == null ? "" : __t) + ' <span class="tips">' + ((__t = $t("必须先开启库存")) == null ? "" : __t) + "</span> --> ";
                } else {
                    __p += ' <span class="info-title">' + ((__t = $t("退换货")) == null ? "" : __t) + '</span> <i class="icon-status icon-selected"></i>' + ((__t = $t("已启用")) == null ? "" : __t) + " ";
                }
                __p += " </div> ";
                if (obj.dht_exchange_return_note_switch == 1) {
                    __p += ' <!-- <p class="tips">' + ((__t = $t("开启后，会初始化退款单、退换货单两个对象")) == null ? "" : __t) + '</p> --> <!-- <p class="tips">' + ((__t = $t("stock.stock_manage.warn.text10")) == null ? "" : __t) + "</p> --> ";
                } else {
                    __p += ' <p class="tips">' + ((__t = $t("已初始化退款单、退换货单两个对象")) == null ? "" : __t) + "</p> ";
                }
                __p += ' </div> <!-- <div class="shiporder-advance_item-btn"> ';
                if (obj.dht_exchange_return_note_switch == 1) {
                    __p += ' <div class="crm-btn crm-btn-primary j-enable-exchange">启用</div> ';
                }
                __p += " </div> --> </div> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/advance/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-advance_wrapper"> <div class="shiporder-advance_item"> <div class="shiporder-advance_item-title"> <span>' + ((__t = $t("数据初始化")) == null ? "" : __t) + '</span> <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ';
            if (obj.stockType === 4) {
                __p += " " + ((__t = $t("数据初始化将会清除发货单的全部数据")) == null ? "" : __t) + " ";
            } else if (obj.isForErp) {
                __p += " " + ((__t = $t("数据初始化将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单")) == null ? "" : __t) + " ";
            } else {
                __p += " " + ((__t = $t("数据初始化将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细")) == null ? "" : __t) + " ";
            }
            __p += ' </div> </div> </div> <div class="shiporder-advance_item-content"> <div class="shiporder-advance_item-info"> <p>' + ((__t = $t("适用场景：试用纷享一段时间后想要清除测试数据")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("操作步骤：联系纷享服务中心")) == null ? "" : __t) + "：400-112-2778，" + ((__t = $t("经审核后开通高危操作权限，此时到次日凌晨前，用户可在权限失效前自行操作")) == null ? "" : __t) + '</p> </div> <div class="shiporder-advance_item-btn crm-btn j-init-data ' + ((__t = obj.canDeleteAllStockData ? "crm-btn-primary" : "crm-btn-disabled") == null ? "" : __t) + '">' + ((__t = $t("数据初始化")) == null ? "" : __t) + "</div> </div> </div> ";
            if (Number(obj.stockStatus) === 2) {
                __p += ' <div class="shiporder-advance_item"> <div class="shiporder-advance_item-title"> <span>' + ((__t = $t("库存切换")) == null ? "" : __t) + '</span> <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ';
                if (obj.isForErp) {
                    __p += " " + ((__t = $t("切换完整版库存，将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、退换货单")) == null ? "" : __t) + " ";
                } else {
                    __p += " " + ((__t = $t("切换到对接版库存，将会清除以下对象的全部数据：仓库、库存、发货单、批次、批次库存、序列号、入库单、出库单、调拨单、盘点单、供应商、采购订单、退换货单、出入库明细")) == null ? "" : __t) + " ";
                }
                __p += ' </div> </div> </div> <div class="shiporder-advance_item-content"> <div class="shiporder-advance_item-info"> <p>' + ((__t = $t("当前模式")) == null ? "" : __t) + "：" + ((__t = obj.isForErp ? $t("对接版") : $t("完整版")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("操作步骤：联系纷享服务中心")) == null ? "" : __t) + "：400-112-2778，" + ((__t = $t("经审核后开通高危操作权限，此时到次日凌晨前，用户可在权限失效前自行操作")) == null ? "" : __t) + '</p> </div> <div class="shiporder-advance_item-btn crm-btn j-switch-stock ' + ((__t = obj.canChangeStockType ? "crm-btn-primary" : "crm-btn-disabled") == null ? "" : __t) + '"> ' + ((__t = obj.isForErp ? $t("切换为完整版") : $t("切换为对接版")) == null ? "" : __t) + "</div> </div> </div> ";
            }
            __p += ' <div class="shiporder-advance_item j-advance_switch"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/checkbox-selection-4-warning-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-base-info_float-setting-item shiporder-set--flex fx-checkbox shiporder-base-info_warning-setting-row"> <label> <input class="shiporder-base-info_radio j-setting-option" type="checkbox" name="' + ((__t = item.name) == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '" ' + ((__t = obj[item.name] ? "checked" : "") == null ? "" : __t) + ' /> <span class="fx-checkbox-icon"></span> <div class="shiporder-base-info_float-setting-content"> ' + ((__t = item.title) == null ? "" : __t) + ' <i class="icon-tips">' + ((__t = item.tips) == null ? "" : __t) + "</i> </div> </label> </div> ";
            });
            __p += " <div class='shiporder-base-info_warning-setting-row'> <div class='shiporder-base-info_warning-setting-row'>" + ((__t = $t("stock.stock_manage.warn.text_7")) == null ? "" : __t) + "</div> <ul class='shiporder-base-info_warning-setting-list j-warning-setting-list-del'> ";
            _.each(obj.recordRemindRolesMap, function(name, id) {
                __p += " <li class='shiporder-base-info_warning-setting-list-item' >" + ((__t = name) == null ? "" : __t) + " <span class='shiporder-base-info_warning-setting-list-item__close' data-id='" + ((__t = id) == null ? "" : __t) + "'></span></li> ";
            });
            __p += " <li class='shiporder-base-info_warning-setting-list-btn j-waring-pick-role'>" + ((__t = "+" + $t("stock.stock_manage.warn.text_8")) == null ? "" : __t) + '</li> </ul> </div> <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-confirm" data-action="' + ((__t = obj.name) == null ? "" : __t) + '"> ' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel" data-action="' + ((__t = obj.name) == null ? "" : __t) + '">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/checkbox-selection-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-base-info_float-setting-item shiporder-set--flex fx-checkbox"> <label> <input class="shiporder-base-info_radio j-setting-option" type="checkbox" name="' + ((__t = item.name) == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '" ' + ((__t = obj[item.name] ? "checked" : "") == null ? "" : __t) + ' /> <span class="fx-checkbox-icon"></span> <div class="shiporder-base-info_float-setting-content"> ' + ((__t = item.title) == null ? "" : __t) + ' <i class="icon-tips">' + ((__t = item.tips) == null ? "" : __t) + "</i> </div> </label> </div> ";
            });
            __p += ' <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-confirm" data-action="' + ((__t = obj.name) == null ? "" : __t) + '"> ' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel" data-action="' + ((__t = obj.name) == null ? "" : __t) + '">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var o;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(o="Object"===(o={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:o)||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,i=Array(t);o<t;o++)i[o]=e[o];return i}function _iterableToArrayLimit(e,t){var o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var i,n,a,r,c=[],l=!0,s=!1;try{if(a=(o=o.call(e)).next,0===t){if(Object(o)!==o)return;l=!1}else for(;!(l=(i=a.call(o)).done)&&(c.push(i.value),c.length!==t);l=!0);}catch(e){s=!0,n=e}finally{try{if(!l&&null!=o.return&&(r=o.return(),Object(r)!==r))return}finally{if(s)throw n}}return c}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/shiporder/shiporder/page/baseinfo/index",["crm-modules/common/stock/api","./tpl-html","./tpl4C-html","./tpl-delivery-html","./radio-selection-html","./checkbox-selection-html","./checkbox-selection-4-warning-html","./radio-selection-4-warehouse-html"],function(e,t,o){Object.defineProperty(t,"__esModule",{value:!0}),t.BaseInfo=void 0;function n(e){var t="";return _.each(e,function(e){"b"===e.type?t+="<b>"+_.escape(e.label)+"</b>":t+="<p>"+_.escape(e.label)+"</p>"}),t}var c=e("crm-modules/common/stock/api"),l=FS.crmUtil,a="WAREHOUSE",r="ordermode",s="VALIDATE",d="EDIT",h="STOCK",u="COUPLING",m="FILLPRODUCT",p="isNotShowZeroStockType",k="isOnlyShowOnSaleStockType",g="STOCK_WARNING_TYPE",f="STOCK_SAFETY_TYPE",S="SCAN",y="lowStockWarning",v="highStockWarning",b="recordRemindIsOpen",w="stockCheckFreezeWarehouse",T="stockCheckNoteUpdateStockType",C="requisitionNoteAutomaticInbound",D="requisitionNoteAutomaticOutbound",O="returnGoodsInvoiceOfSalesOrderType",R="setValidateOrderTypeByRule",W="purchase_order_calculation",N="stock_check_note_product_source_type",j="delivery_stock_related_check",I="enable_edit_purchase_order_when_in_stock",i=null,e=Backbone.View.extend({template:e("./tpl-html"),templateForC:e("./tpl4C-html"),templateForDelivery:e("./tpl-delivery-html"),radioSelection:e("./radio-selection-html"),checkboxSelection:e("./checkbox-selection-html"),checkboxSelection4Warning:e("./checkbox-selection-4-warning-html"),radioSelection4Warehouse:e("./radio-selection-4-warehouse-html"),tempData:{orderMode:null,orderWarehouseType:null,validateOrderType:null,isNotShowZeroStockType:null,isOnlyShowOnSaleStockType:null,autoReceiveStatus:null,allowOrderWarehouseBlank:null,lowStockWarning:null,highStockWarning:null,stockWarningType:null,stockCheckNoteUpdateStockType:null,requisitionNoteAutomaticInbound:null,requisitionNoteAutomaticOutbound:null,returnGoodsInvoiceOfSalesOrderType:null},events:{"click .j-decimal":"_handleSettingDecimal","click .j-decimal-confirm":"_handleConfirmDecimal","click .j-validate":"_handleSettingValidate","click .j-stock":"_handleSettingStock","click .j-delivery":"_handleSettingDelivery","click .j-cancel":"_handleCancel","click .j-confirm":"_handleConfirm","click .j-warehouse":"_handleSettingWarehouse","click .j-warehouse-setting":"_handleWarehouseConfirm","click .j-setting-option":"_handleOptionClick","click .j-warning":"_handleSettingWarning","click .j-waring-pick-role":"handlePickRecordRemindRole","click .j-safety":"_handleSettingSafety","click .j-scan":"_handleSettingScan","click .j-stock-switch":"_handleStockSwitch","click .j-auto-receive":"_handleAutoReceive","click .j-auto-receive-radio":"_handleAutoReciveRadio","click .j-edit":"_handleEditOrder","click .j-allow-blank":"_handleBlankOrder","click .j-stock-grey-check-switch":"_handleStockGreyCheckSwitch","click .j-freeze":"_handleStockCheckFreezeWarehouse","click .j-order-delivery-mode_edit":"_handleOrderDeliveryModeEdit","click .j-setting-stock-check-note-update-stock-type":"_handleStockCheckNoteUpdateStockType","click .j-setting-requisition-note-automatic-inbound":"_handleRequisitionNoteAutomaticInbound","click .j-setting-requisition-note-automatic-outbound":"_handleRequisitionNoteAutomaticOutbound","click .j-setting-return-goods-invoice-of-sales-order-type":"_handleReturnGoodsInvoiceOfSalesOrderType","click .j-coupling-edit":"_handleEditCoupling","click .j-fillProduct-edit":"_handleFillProduct","click .j-stock-scan-switch":"_handleStocScanSwitch","click .j-purchase-order-calculation":"_handlePurchaseOrderCalculation","click .j-stock-check-note-product-source-type":"_handleStockCheckNoteProductSourceType","click .j-setting-delivery-stock-related-check":"_handleDeliveryStockRelatedCheck","click .j-setting-enable_edit_purchase_order_when_in_stock":"_handlePurchaseOrderWhenInStock","click .j-warning-setting-list-del":"handleWarningSettingListDel","click .j-setting-location_management":"_handleLocationManagementSwitch"},initialize:function(){this.listenTo(this.model,"change:orderWarehouseType",this._toggleBlankOrderOption)},render:function(){var o=this;return Promise.all([(0,c.commonGetConfigValueByKeys)({keys:["delivery_note_interaction_model","stock_order_product_fill","return_goods_invoice_of_sales_order_type","purchase_order_calculation","stock_check_note_product_source_type","delivery_stock_related_check","enable_edit_purchase_order_when_in_stock","distribution_stock_switch","spare_part_inventory_switch","location_management"],isAllConfig:!1}),(0,c.getRecordRemindByRoleSwitch)()]).then(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1];o.model.set({delivery_note_interaction_model:t.delivery_note_interaction_model,stock_order_product_fill:t.stock_order_product_fill,return_goods_invoice_of_sales_order_type:t.return_goods_invoice_of_sales_order_type,purchase_order_calculation:t.purchase_order_calculation,stock_check_note_product_source_type:t.stock_check_note_product_source_type,delivery_stock_related_check:t.delivery_stock_related_check,enable_edit_purchase_order_when_in_stock:t.enable_edit_purchase_order_when_in_stock,distribution_stock_switch:t.distribution_stock_switch,spare_part_inventory_switch:t.spare_part_inventory_switch,location_management:t.location_management,recordRemindIsOpen:e.recordRemindIsOpen,recordRemindRolesMap:e.recordRemindRolesMap,stockWarningType:e.stockWarningType})}).catch(function(){}).then(function(){var e=+o.model.get("stockType");3==e?o._renderCTemplate():4==e?o.renderPageByDelivery():(o.model.getBaseConfig().then(function(e){o._getScanInfo(),e.isForErp?(o.model.set({isDistributionStockSupportMultiOrderingEnable:e.isDistributionStockSupportMultiOrderingEnable}),o.model.getAllConfigData().then(function(e){o.model.setOrderRecordTypeRelateDeliveryMode(e),o._renderCommonTemplate()})):o.model.getAllConfigData().then(function(e){return o.model.setOrderRecordTypeRelateDeliveryMode(e)}).then(function(){return(0,c.isAllowSetStockConf)({configTypes:["stock_grey_check_switch"]})}).then(function(e){return o.model.set({isShowStockGreyCheckSwitch:e.stock_grey_check_switch})}).then(function(){return o.model.getStockData(function(){},["stock_grey_check_switch","stock_check_freeze_warehouse"])}).then(function(e){o.model.set("stockGreyCheckSwitch",e.stock_grey_check_switch),o._renderCommonTemplate()})}),o.model.isShowStockLoading())})},_renderCommonTemplate:function(e){e=e||this.model.toJSON(),e.scanCodeType||(e.scanCodeType=Number(this.model.get("scanCodeType"))),e.warningTypeDesc=this.model.getWarningTypeDesc(),e=this.template(e);return this.$el&&this.$el.html(e),this.rendered()},_renderCTemplate:function(){var t=this;this.model.fetchErpInfo(function(e){e=t.templateForC(e);return t.$el&&t.$el.html(e),this.rendered()})},renderPageByDelivery:function(){var t=this;this.model.getBaseConfig().then(function(e){return t.$el&&t.$el.html(t.templateForDelivery(t.model.toJSON())),t.rendered()})},_handleLocationManagementSwitch:function(e){var t,o=$(e.currentTarget),i=this,e=i.model.get("location_management");o.hasClass("switch-disabled")&&"1"===e||(e="<p>"+$t("开启后，此功能不可关闭，是否确认开启此功能？")+"</p>",t=l.confirm(e,$t("温馨提示"),function(){t.destroy(),i.model.set("maskTips",$t("正在加载")),i.model.set("showMask",!0),(0,c.asyncSetConfigValue)().circle({key:"location_management",value:"1",token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){i.model.set("showMask",!1);e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):(FS.crmUtil.remind(1,$t("操作成功")),i.model.set("location_management","1"),o.addClass("switch-on switch-disabled"))}).catch(function(e){i.model.set("showMask",!1),FS.crmUtil.remind(3,$t(e||"启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleShowSettingDetail:function(e){e=$(e.currentTarget);e.addClass("active"),e.find(".space-between").hide(),e.find(".shiporder-base-info_setting-detail").show()},rendered:function(){var e=FS.util.getTplQueryParams();if(null!=e&&e.param)return this.handleFocusOnDecimalSetting(e)},handleFocusOnDecimalSetting:function(e){var t=$(".j-decimal");t.addClass("active"),t.find(".space-between").hide(),t.find(".shiporder-base-info_setting-detail").show(),$(".shiporder-base-info_item-content-stock").addClass("shiporder-shake")},_handleHideSettingDetail:function(e){e=$(e.currentTarget).parents(".shiporder-base-info_item");e.removeClass("active"),e.find(".space-between").show(),e.find(".shiporder-base-info_setting-detail").hide()},_handleSettingDecimal:function(e){this._handleShowSettingDetail(e)},_checkDecimals:function(e,t,o,i){return t<e?l.remind(3,$t("stock.stock_manage.warn.text12")):o<e||t<o?l.remind(3,$t("发货单精度应不可小于订单产品精度，不可大于库存精度")):!(t<i)||l.remind(3,$t("采购订单的精度不可大于库存精度"))},_handleConfirmDecimal:function(e){e.stopPropagation();var t=+this.model.get("stockType"),o=Number(this.model.get("salesOrderProductDecimal")),i=Number($(".j-delivery-decimal").attr("value"));if(4==t)return i<o?l.remind(3,$t("发货单精度应不可小于订单产品精度")):void this.model.setDecimal({deliveryNoteDecimal:i},$(e.target));var n=Number($(".j-stock-decimal").attr("value")),a=Number($(".j-po-decimal").attr("value")),r=this;3==t?(this.model.set("stockDecimal",n),this.model.setErpStockConfig(function(){l.remind(1,$t("stock.stock_manage.info.text3")),r._handleHideSettingDetail(e)},$(e.target))):(t={},null!=n&&(t.stockDecimal=n),null!=i&&(t.deliveryNoteDecimal=i),null!=a&&(t.purchaseOrderDecimal=a),this._checkDecimals(o,n,i,a)&&this.model.setDecimal(t,$(e.target),function(e){window.opener&&window.opener.postMessage(JSON.stringify({isSpareMessage:!0,shouldUpdateSpareSetting:!0}),location.origin),r.render()}))},_handleCancel:function(e){e.stopPropagation(),this._handleHideSettingDetail(e);e=$(e.currentTarget).data("action");switch(e){case r:this.tempData.orderMode=null,this.tempData.orderWarehouseType=null,this.tempData.allowOrderWarehouseBlank=null;break;case s:this.tempData.validateOrderType=null;break;case h:this.tempData.isNotShowZeroStockType=null;break;case d:this.tempData.isAllowModifySalesOrderProduct=null;break;case w:this.tempData.stockCheckFreezeWarehouse=null;break;case T:this.tempData.stockCheckNoteUpdateStockType=null;break;case C:this.tempData.requisitionNoteAutomaticInbound=null;break;case D:this.tempData.requisitionNoteAutomaticOutbound=null;break;case u:this.tempData.delivery_note_interaction_model=null;break;case m:this.tempData.stock_order_product_fill=null;break;case W:this.tempData.purchase_order_calculation=null;break;case N:this.tempData.stock_check_note_product_source_type=null;break;case j:this.tempData.delivery_stock_related_check=null;break;case I:this.tempData.enable_edit_purchase_order_when_in_stock=null}},_handleConfirm:function(e){switch(e.stopPropagation(),$(e.currentTarget).data("action")){case r:this._handleWarehouseConfirm(e);break;case s:this._handleValidateConfirm(e);break;case h:this._handleStockConfirm(e);break;case g:this._handleWarningConfirm(e);break;case f:this._handleSafetyConfirm(e);break;case S:this._handleScanConfirm(e);break;case d:this._handleEditConfirm(e);break;case w:this._handleFreezeConfirm(e);break;case T:this._handleStockCheckNoteUpdateStockTypeConfirm(e);break;case C:this._handleRequisitionNoteAutomaticInboundConfirm(e);break;case D:this._handleRequisitionNoteAutomaticOutboundConfirm(e);break;case u:this._handleSetCoupling(e);break;case m:this._handleFillProductConfirm(e);break;case O:this._handleReturnGoodsInvoiceOfSalesOrderTypeConfirm(e);break;case W:this._handlePurchaseOrderCalculationConfirm(e);break;case N:this._handleStockCheckNoteProductSourceTypeConfirm(e);break;case j:this._handleDeliveryStockRelatedCheckConfirm(e);break;case I:this._handleEnableEditPurchaseOrderWhenInStockConfirm(e)}},_handleReturnGoodsInvoiceOfSalesOrderTypeConfirm:function(e){var t=this.tempData.returnGoodsInvoiceOfSalesOrderType||this.model.get("return_goods_invoice_of_sales_order_type"),o=(1===t?t="one":2===t&&(t="multiple"),this);(0,c.setReturnGoodsInvoiceOfSalesOrderType)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleSetCoupling:function(e){var t=this.tempData.delivery_note_interaction_model||this.model.get("delivery_note_interaction_model"),o=this;(0,c.setCoupling)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleFillProductConfirm:function(e){var t=this.tempData.stock_order_product_fill||this.model.get("stock_order_product_fill"),o=this;(0,c.setFillProduct)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handlePurchaseOrderCalculationConfirm:function(e){var t=this.tempData.purchase_order_calculation||this.model.get("purchase_order_calculation"),o=this;(0,c.setPurchaseOrderCalculation)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleStockCheckNoteProductSourceTypeConfirm:function(e){var t=this.tempData.stock_check_note_product_source_type||this.model.get("stock_check_note_product_source_type"),o=this;(0,c.setStockCheckNoteProductSourceType)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleDeliveryStockRelatedCheckConfirm:function(e){var t=this.tempData.delivery_stock_related_check||this.model.get("delivery_stock_related_check"),o=this;(0,c.setDeliveryStockRelatedCheck)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleEnableEditPurchaseOrderWhenInStockConfirm:function(e){var t=this.tempData.enable_edit_purchase_order_when_in_stock||this.model.get("enable_edit_purchase_order_when_in_stock"),o=this;(0,c.setEnableEditPurchaseOrderWhenInStock)(t).then(function(e){o.render()}).catch(function(e){l.remind(3,e)})},_handleOptionClick:function(e){e.stopPropagation();var t=$(e.currentTarget).attr("name"),o=$(e.currentTarget).data("value");switch(t){case r:1==(this.tempData.orderMode=o)?($(".crm-s-shiporder .shiporder-base-info_float-setting-item--child").show(),$(".crm-s-shiporder .shiporder-base-info_order-delivery-mode").hide()):($(".crm-s-shiporder .shiporder-base-info_float-setting-item--child").hide(),$(".crm-s-shiporder .shiporder-base-info_order-delivery-mode").show());break;case a:2==+(this.tempData.orderWarehouseType=o)?$(".j-allow-blank").hide():$(".j-allow-blank").show();break;case s:this.tempData.validateOrderType=o;break;case p:this.tempData.isNotShowZeroStockType=$(e.currentTarget).is(":checked")?1:2;break;case k:this.tempData.isOnlyShowOnSaleStockType=$(e.currentTarget).is(":checked")?1:2;break;case y:var i=$(e.currentTarget).is(":checked");this.tempData.lowStockWarning=i;break;case v:i=$(e.currentTarget).is(":checked");this.tempData.highStockWarning=i;break;case b:i=$(e.currentTarget).is(":checked");this.tempData.recordRemindIsOpen=i;break;case g:this.tempData.stockWarningType=o;break;case f:this.tempData.safetyStockType=o;break;case S:this.tempData.scanCodeType=o;break;case d:this.tempData.isAllowModifySalesOrderProduct=o;break;case w:this.tempData.stockCheckFreezeWarehouse=o;break;case T:this.tempData.stockCheckNoteUpdateStockType=o;break;case C:this.tempData.requisitionNoteAutomaticInbound=o;break;case D:this.tempData.requisitionNoteAutomaticOutbound=o;break;case u:this.tempData.delivery_note_interaction_model=o;break;case m:this.tempData.stock_order_product_fill=o;break;case O:this.tempData.returnGoodsInvoiceOfSalesOrderType=o;break;case W:this.tempData.purchase_order_calculation=o;break;case N:this.tempData.stock_check_note_product_source_type=o;break;case j:this.tempData.delivery_stock_related_check=o;break;case I:this.tempData.enable_edit_purchase_order_when_in_stock=o}},_handleSettingValidate:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;var t=this.model.get("validateOrderType"),o=this.model.get("stockType"),i=this.model.get("isForErp"),o=2==+o&&!i;$(".j-validate-detail").html(this.radioSelection({options:[{title:$t("可用库存数量不足时，不可提交订单"),value:1,tipHtml:o?[{type:"b",label:$t("可用库存数量不足时，不可提交订单")},{label:$t("此设置项下，创建出库单/调拨单时，校验可用库存，可用库存不足不可创建出库单/调拨单。")}]:""},{title:$t("可用库存数量不足时，仍可提交订单"),value:2,tipHtml:o?[{type:"b",label:$t("可用库存数量不足时，仍可提交订单")},{label:$t("此设置项下，创建出库单/调拨单时，校验实际库存，实际库存不足不可创建出库单/调拨单。")}]:""},{title:$t("根据订单的业务类型，设置不同的校验规则"),value:3,link:{dataType:R,text:$t("设置")}}],value:t,name:s,parseTipHtml:n})),$(".shiporder-base-info_float-setting-item__href").click(this._handleSettingItemHrefClick.bind(this)),this._handleShowSettingDetail(e)},_handleSettingItemHrefClick:function(e){e.stopPropagation(),this.tempData.validateOrderType=3;var t=e.currentTarget,o=$(t).data("type"),i=$(t).data("hash");o===R&&this._handleValidateConfirm(e).then(function(){FS.tpl.navRouter.navigate(i,{trigger:!0,mod:"crmmanage"})})},_handleValidateConfirm:function(n){var a=this;return new Promise(function(e){var t=a,o=a.model.get("stockType"),i=a.tempData.validateOrderType||a.model.get("validateOrderType");a.model.set("validateOrderType",i),3==+o?a.model.setErpStockConfig(function(){l.remind(1,$t("操作成功!")),e(),t._handleHideSettingDetail(n),t.render()},$(n.target)):a.model.setValidateOrderType(i,$(n.target),function(){e(),t.render()})})},_handleEditOrder:function(e){var t=this.model.get("isAllowModifySalesOrderProduct");$(".j-edit-detail").html(this.radioSelection({options:[{title:$t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类"),value:2},{title:$t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类"),value:1}],value:t,name:d})),this._handleShowSettingDetail(e)},_handleEditCoupling:function(e){var t=this.model.get("delivery_note_interaction_model");$(".j-edit-coupling-detail").html(this.radioSelection({options:[{title:$t("强耦合，发货单产品的产品范围必须基于订单产品选择，发货单产品金额必须与订单产品保持一致，发货单确认后系统自动更新订单的发货数量、金额等信息"),value:1},{title:$t("弱耦合，发货单产品可以从订单产品之外的范围内选择，发货单产品金额可以与订单产品不一致，发货单确认后系统不会更新订单的任何信息"),value:2,desc:this.model.get("isDistributionStockSupportMultiOrderingEnable")?$t("stock.stock_manage.DeliveryNoteObj.distribution.not_applicable"):""}],value:t,name:u})),this._handleShowSettingDetail(e)},_handleFillProduct:function(e){var t=this.model.get("stock_order_product_fill");$(".j-edit-fillProduct-detail").html(this.radioSelection({options:[{title:$t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行"),value:1},{title:$t("新建发货单时，系统不自动填充任何产品到发货单产品明细行"),value:2}],value:t,name:m})),this._handleShowSettingDetail(e)},_handlePurchaseOrderCalculation:function(e){var t=this.model.get("purchase_order_calculation");$(".j-edit-purchase-order-calculation").html(this.radioSelection({options:[{title:$t("用户手动计算：新建采购订单时，金额相关的字段，均由用户自行计算后输入"),value:1},{title:$t("系统自动计算：新建采购订单时，金额相关的字段，系统根据相关条件进行自动计算"),value:2}],value:t,name:W})),this._handleShowSettingDetail(e)},_handlePurchaseOrderWhenInStock:function(e){var t=this.model.get("enable_edit_purchase_order_when_in_stock");$(".j-setting-enable_edit_purchase_order_when_in_stock_detail").html(this.radioSelection({options:[{title:$t("采购订单部分入库/全部入库后，不允许编辑采购订单产品的数量和种类"),value:1},{title:$t("采购订单部分入库/全部入库后，允许编辑采购订单产品的数量和种类"),value:2}],value:t,name:I})),this._handleShowSettingDetail(e)},_handleStockCheckNoteProductSourceType:function(e){var t=this.model.get("stock_check_note_product_source_type");$(".j-edit-stock-check-note-product-source-type").html(this.radioSelection({options:[{title:$t("StockCheckNoteObj.check_stock"),value:1},{title:$t("StockCheckNoteObj.check_product"),value:2}],value:t,name:N})),this._handleShowSettingDetail(e)},_handleDeliveryStockRelatedCheck:function(e){var t=this.model.get("delivery_stock_related_check");$(".j-edit-delivery-stock-related-check").html(this.radioSelection({options:[{title:$t("stock.stock_manage.weak_stock"),value:1},{title:$t("stock.stock_manage.strong_stock"),value:2}],value:t,name:j})),this._handleShowSettingDetail(e)},_handleStockCheckNoteUpdateStockType:function(e){var t=this.model.get("stockCheckNoteUpdateStockType");$(".j-setting-stock-check-note-update-stock-type-detail").html(this.radioSelection({options:[{title:$t("盘点单确认后，需要手动操作盘盈入库、盘亏出库来调整库存"),value:1},{title:$t("盘点单确认后，系统自动生成盘盈入库单、盘亏出库单来进行库存调整"),value:2}],value:t,name:T})),this._handleShowSettingDetail(e)},_handleRequisitionNoteAutomaticInbound:function(e){var t=this.model.get("requisitionNoteAutomaticInbound");$(".j-setting-requisition-note-automatic-inbound-detail").html(this.radioSelection({options:[{title:$t("调拨单确认后，系统自动处理调拨确认入库"),value:1},{title:$t("调拨单确认后，需手动操作调拨确认入库"),value:2}],value:t,name:C})),this._handleShowSettingDetail(e)},_handleRequisitionNoteAutomaticOutbound:function(e){var t=this.model.get("requisitionNoteAutomaticOutbound");$(".j-setting-requisition-note-automatic-outbound-detail").html(this.radioSelection({options:[{title:$t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-automatic"),value:1},{title:$t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-mannul"),value:2}],value:t,name:D})),this._handleShowSettingDetail(e)},_handleReturnGoodsInvoiceOfSalesOrderType:function(e){var t=this.model.get("return_goods_invoice_of_sales_order_type");$(".j-setting-return-goods-invoice-of-sales-order-type-detail").html(this.radioSelection({options:[{title:$t("允许多张销售订单一起退货"),value:2},{title:$t("每次仅可基于一张销售订单退货"),value:1}],value:"one"===t?1:2,name:O})),this._handleShowSettingDetail(e)},_handleEditConfirm:function(e){var t=this,o=this.tempData.isAllowModifySalesOrderProduct||this.model.get("isAllowModifySalesOrderProduct");this.model.set("isAllowModifySalesOrderProduct",o),this.model.setEditOrder({isAllowModifySalesOrderProduct:o},function(){t.render()})},_handleStockCheckNoteUpdateStockTypeConfirm:function(e){var t=this,o=this.tempData.stockCheckNoteUpdateStockType||this.model.get("stockCheckNoteUpdateStockType");this.model.set("stockCheckNoteUpdateStockType",o),this.model.setStockCheckNoteUpdateStockType({stockCheckNoteUpdateStockType:o},function(){t.render()})},_handleRequisitionNoteAutomaticInboundConfirm:function(e){var t=this,o=this.tempData.requisitionNoteAutomaticInbound||this.model.get("requisitionNoteAutomaticInbound");this.model.set("requisitionNoteAutomaticInbound",o),this.model.setStockConfigs("requisition_note_automatic_inbound",o,function(){t.render()})},_handleRequisitionNoteAutomaticOutboundConfirm:function(e){var t=this,o=this.tempData.requisitionNoteAutomaticOutbound||this.model.get("requisitionNoteAutomaticOutbound");this.model.set("requisitionNoteAutomaticOutbound",o),this.model.setStockConfigs("requisition_note_automatic_outbound",o,function(){t.render()})},_handleSettingWarehouse:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;var t=this.model.get("orderWarehouseTypeConfigRangeCode"),o=this.model.get("orderWarehouseType"),i=this.model.get("orderRecordTypeRelateDeliveryMode");this.tempData.allowOrderWarehouseBlank||this.model.get("allowOrderWarehouseBlank");$(".j-warehouse-detail").html(this.radioSelection4Warehouse({unifiedSetting:{title:$t("统一设置"),childName:a,childValue:o,child:[{title:$t("crm.无仓库订货"),value:3,tipHtml:[{type:"b",label:$t("crm.无仓库订货")+":"},{label:$t("提交销售订单时，无需指定订货仓库，且不校验库存")}]},{title:$t("crm.单一仓库订货"),value:1,tipHtml:[{type:"b",label:$t("crm.单一仓库订货:")},{label:$t("提交销售订单时需指定订货仓库根据指定的订货仓库来校验库存")}]},{title:$t("crm.合并仓库订货"),value:2,tipHtml:[{type:"b",label:$t("crm.合并仓库订货:")},{label:$t("提交订单时无需手动选择仓库根据所有适用的仓库来校验库存")}]}]},orderRecordTypeRelateDeliveryMode:i,value:t,name:r,parseTipHtml:n})),1==t?($(".crm-s-shiporder .shiporder-base-info_float-setting-item--child").show(),$(".crm-s-shiporder .shiporder-base-info_order-delivery-mode").hide()):($(".crm-s-shiporder .shiporder-base-info_float-setting-item--child").hide(),$(".crm-s-shiporder .shiporder-base-info_order-delivery-mode").show()),this._handleShowSettingDetail(e),this._toggleBlankOrderOption()},_handleWarehouseConfirm:function(e,t){var o=this,i=this.tempData.orderWarehouseType||this.model.get("orderWarehouseType"),n=this.tempData.allowOrderWarehouseBlank||this.model.get("allowOrderWarehouseBlank"),a=this.tempData.orderMode||this.model.get("orderWarehouseTypeConfigRangeCode"),i=(this.model.set("orderWarehouseType",i),this.model.set("allowOrderWarehouseBlank",n),this.model.set("orderWarehouseTypeConfigRangeCode",a),{orderWarehouseType:i,allowOrderWarehouseBlank:2===i?2:n,orderWarehouseTypeConfigRangeCode:a});return this.model.setOrderWarehouseType(i,e.target?$(e.target):$(e)).then(function(){t||o.render()})},_handleSettingStock:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;var t=this.model.get("isNotShowZeroStockType"),o=this.model.get("isOnlyShowOnSaleStockType"),i=3==+this.model.get("stockType")?[{title:$t("stock.stock_manage.inventory_display.dot_not_display_empty_stock"),name:p}]:[{title:$t("stock.stock_manage.inventory_display.dot_not_display_empty_stock"),name:p},{title:$t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product"),name:k}];$(".j-stock-detail").html(this.checkboxSelection({options:i,isNotShowZeroStockType:1===Number(t),isOnlyShowOnSaleStockType:1===Number(o),name:h})),this._handleShowSettingDetail(e)},_handleStockConfirm:function(e){var t=this.tempData.isNotShowZeroStockType||this.model.get("isNotShowZeroStockType"),o=this.tempData.isOnlyShowOnSaleStockType||this.model.get("isOnlyShowOnSaleStockType"),i=(this.model.set("isNotShowZeroStockType",t),this.model.set("isOnlyShowOnSaleStockType",o),this.model.get("stockType")),n=this;3==+i?this.model.setErpStockConfig(function(){l.remind(1,$t("操作成功!")),n._handleHideSettingDetail(e),n.render()},$(e.target)):this.model.setShowStockType({isNotShowZeroStockType:t,isOnlyShowOnSaleStockType:o},$(e.target),function(){n.render()})},initWarningSetting:function(e){var t,o,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=2==this.model.get("batchSNSwitch"),a=+this.model.get("stockWarningType"),e=e||_.clone(this.model.get("recordRemindRolesMap")),a=(i=Object.keys(i).length?(t=i.lowStock,o=i.highStock,i.recordRemindIsOpen):(t=2==a||4==a,o=3==a||4==a,this.model.get("recordRemindIsOpen")),this.tempData.lowStockWarning=t,this.tempData.highStockWarning=o,this.tempData.recordRemindIsOpen=i,this.tempData.recordRemindRolesMap=e,[{title:$t("stock.stock_manage.warn.text_1"),name:y},{title:$t("stock.stock_manage.warn.text_2"),name:v}]);n&&a.push({title:$t("stock.stock_manage.warn.title"),name:b}),$(".j-warning-detail").html(this.checkboxSelection4Warning({isBatchSNOpen:n,options:a,recordRemindRolesMap:e,lowStockWarning:t,highStockWarning:o,recordRemindIsOpen:i,name:g}))},handleWarningSettingListDel:function(e){e.stopPropagation();e=$(event.target).data("id"),delete this.tempData.recordRemindRolesMap[e],e={lowStock:this.tempData.lowStockWarning,highStock:this.tempData.highStockWarning,recordRemindIsOpen:this.tempData.recordRemindIsOpen};this.initWarningSetting(this.tempData.recordRemindRolesMap,e)},_handleSettingWarning:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;this.initWarningSetting(),this._handleShowSettingDetail(e)},handlePickRecordRemindRole:function(e){e.stopPropagation();var e=this.tempData.recordRemindRolesMap,t=Object.keys(e),n=this;FxUI.create({wrapper:"body",template:'\n            <fx-selector-box-v2\n              ref=\'sb\'\n              :show.sync="showSelectorBox"\n              v-bind="opts"\n              @confirm="confirm"\n              @cancel="cancel"\n              @selector-mounted="onSelectorMounted"\n            >\n            </fx-selector-box-v2>\n          ',data:function(){return{showSelectorBox:!1,opts:FS.selectorParseContactV2.parseContacts({role:!0,defaultSelectedItems:{role:t}})}},methods:{confirm:function(e){var i=e.role;this.$refs.sb.getRoleList().then(function(e){var o=e.reduce(function(e,t){return e[t.id]=t.name,e},{}),e=i.reduce(function(e,t){return e[t]=o[t],e},{}),t=(n.tempData.recordRemindRolesMap=e,{lowStock:n.tempData.lowStockWarning,highStock:n.tempData.highStockWarning,recordRemindIsOpen:n.tempData.recordRemindIsOpen});n.initWarningSetting(e,t)})},cancel:function(){this._destroy()},_destroy:function(){i&&i.destroy&&(i.destroy(),i=null)},onSelectorMounted:function(){var e=this;this.$nextTick(function(){e.showSelectorBox=!0})}},mounted:function(){}})},_handleWarningConfirm:function(e){var t=this,o=(this.model.get("batchSNSwitch"),this.tempData.lowStockWarning),i=this.tempData.highStockWarning,n=this.tempData.recordRemindIsOpen,a=this.tempData.recordRemindRolesMap,r=o&&i?4:i?3:o?2:1;return(0,c.saveRecordRemindByRoleSwitch)({stockWarningType:r+"",willExpireStockWarningType:n?"2":"1",roles:Object.keys(a)},$(e.target)).then(function(){t.model.set("stockWarningType",r),t.model.set("recordRemindIsOpen",n),t.model.set("recordRemindRolesMap",a),t.render(),l.remind(1,$t("操作成功!"))}).catch(function(e){})},_handleSettingSafety:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;var t=this.model.get("safetyStockType");$(".j-safety-detail").html(this.radioSelection({options:[{title:$t("统一设置安全库存与最高库存"),value:1,tipHtml:[{label:$t("在产品对象中设置相应的字段值，对所有仓库均适用")}]},{title:$t("分仓库设置安全库存与最高库存"),value:2,tipHtml:[{label:$t("在库存对象中设置相应的字段值，仅对该产品库存适用")}]}],value:t,name:f,parseTipHtml:n})),this._handleShowSettingDetail(e)},_handleStockCheckFreezeWarehouse:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;var t=this.model.get("stockCheckFreezeWarehouse");$(".j-freeze-detail").html(this.radioSelection({options:[{title:$t("stock.stock_manage.stock_check_freeze_warehouse.not_create_inbound_outbound"),value:1},{title:$t("stock.stock_manage.stock_check_not_freeze_warehouse.create_inbound_outbound"),value:2}],value:t,name:w})),this._handleShowSettingDetail(e)},_handleSafetyConfirm:function(e){var t=this.tempData.safetyStockType||this.model.get("safetyStockType"),o=this;this.model.set("safetyStockType",t),this.model.setSafetyStockType(t,$(e.target),function(){o.render()})},_handleFreezeConfirm:function(e){var t=this.tempData.stockCheckFreezeWarehouse||this.model.get("stockCheckFreezeWarehouse"),o=this;this.model.set("stockCheckFreezeWarehouse",t),o.model.setStockConfigs("stock_check_freeze_warehouse",t,function(){o.render()})},_handleSettingScan:function(e){if($(e.currentTarget).hasClass("item-disabled"))return!1;e=$(e.currentTarget.parentNode.parentNode.parentNode);e.addClass("active"),e.find(".space-between").hide(),e.find(".shiporder-base-info_setting-detail").show();e=[],e=2==+this.model.get("batchSNSwitch")?[{title:$t("扫描产品条码录入产品信息"),value:1},{title:$t("扫描批次条码录入产品信息"),value:2},{title:$t("扫描序列号条码录入产品信息"),value:3}]:[{title:$t("扫描产品条码录入产品信息"),value:1}];$(".j-scan-detail").html(this.radioSelection({options:e,value:this.model.get("scanCodeType"),name:S}))},_handleScanConfirm:function(e){var t=this.tempData.scanCodeType||this.model.get("scanCodeType"),o=this;this.model.set("scanCodeType",t),this.model.setScanInfo(t,function(){o.render()})},_handleStockSwitch:function(e){var t,o=$(e.currentTarget),i=this;o.hasClass("switch-disabled")||(e="",e=(e+="<p>"+$t("此功能开启后，将在销售订单的提交页面显示所选仓库（单一仓库订货模式）或所有适用仓库（合并仓库订货模式）的可用库存数量。")+"</p>")+'<p style="margin-top: 20px">'+$t("开启后，此功能不可关闭，是否确认开启此功能？")+"</p>",t=l.confirm(e,$t("温馨提示"),function(){t.destroy(),i.model.set("maskTips",$t("正在加载")),i.model.set("showMask",!0),i.model.enableSalesOrder(function(){o.addClass("switch-on switch-disabled"),i.model.set("showMask",!1)})}))},_handleStockGreyCheckSwitch:function(e){var t,o=$(e.currentTarget),i=this;o.hasClass("switch-disabled")||(e="",e+="<p>"+$t("开启后，此功能不可关闭，是否确认开启此功能？")+"</p>",t=l.confirm(e,$t("温馨提示"),function(){t.destroy(),i.model.set("maskTips",$t("正在加载"));var e=i.model.get("stockGreyCheckSwitch");i.model.set("showMask",!0),i.model.setStockConfigs("stock_grey_check_switch",1===e?2:1,function(){o.addClass("switch-on switch-disabled"),i.model.set("showMask",!1)})}))},_handleStocScanSwitch:function(e){var e=$(e.currentTarget),t=this;e.hasClass("switch-disabled")||(e=10==this.model.get("scanCodeType")?1:10,this.model.set("scanCodeType",e),this.model.setScanInfo(e,function(){t.render()}))},_handleSettingDelivery:function(e){this._handleShowSettingDetail(e)},_handleAutoReceive:function(t){var o=this,i=$(".j-auto-receive-days").attr("value"),n=this.tempData.autoReceiveStatus||this.model.get("autoReceiveStatus");this.model.set("daysAfterShipment",parseInt(i)),this.model.set("autoReceiveStatus",parseInt(n)),this.model.setReceiving({status:parseInt(n),daysAfterShipment:parseInt(i)},function(){var e=1===n?"物流签收起"+i+"天后，自动对发货单进行收货确认":"不启动自动收货";$(".j-auto-receive-text").text(e),o._handleHideSettingDetail(t)})},_handleAutoReciveRadio:function(e){e=$(e.currentTarget),e=parseInt(e[0].value);this.tempData.autoReceiveStatus=e},_getScanInfo:function(){var t=this,o={1:$t("扫描产品条码录入产品信息"),2:$t("扫描批次条码录入产品信息"),3:$t("扫描序列号条码录入产品信息")};this.model.getScanInfo(function(e){t.model.set("scanCodeType",e),$(".j-scan-type").html(o[e])})},_handleBlankOrder:function(e){e.stopPropagation();e=this.model.get("allowOrderWarehouseBlank");this.tempData.allowOrderWarehouseBlank=1==+e?2:1},_toggleBlankOrderOption:function(){2==+this.model.get("orderWarehouseType")?$(".j-allow-blank").hide():$(".j-allow-blank").show()},_handleOrderDeliveryModeEdit:function(){function o(){return/XV\/UI/.test(window.location.href)}var i="#crmmanage/=/module-businessconfig";this._handleWarehouseConfirm($('.j-confirm[data-action="ordermode"]'),!0).then(function(){var e,t;if(o())return e=i,t={mod:"manage"},t=o()?"/XV/UI/".concat(t.mod).concat(e):e,window.open(t,"_blank");FS.tpl.navRouter.navigate(i,{trigger:!0,mod:"crmmanage"})})},destroy:function(){this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null,window.addEventListener("message",this.receiveMessage,!1)}});t.BaseInfo=e});
define("crm-setting/shiporder/shiporder/page/baseinfo/radio-selection-4-warehouse-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-base-info_float-setting-item shiporder-base-info_float-setting-item--no-margin" style="display: block"> <div class="shiporder-base-info_float-setting-item fx-radio"> <label> <input class="shiporder-base-info_radio j-setting-option" type="radio" name="ordermode" data-value="1" ' + ((__t = Number(obj.value) === 1 ? "checked" : "") == null ? "" : __t) + ' /> <div class="fx-radio-icon"></div> <div class="shiporder-base-info_float-setting-content">' + ((__t = obj.unifiedSetting.title) == null ? "" : __t) + '</div> </label> </div> <div class="shiporder-base-info_float-setting-item--child"> ';
            _.each(obj.unifiedSetting.child, function(child) {
                __p += ' <div class="shiporder-base-info_float-setting-item fx-radio"> <label> <input class="shiporder-base-info_radio j-setting-option" type="radio" name="' + ((__t = obj.unifiedSetting.childName) == null ? "" : __t) + '" data-value="' + ((__t = child.value) == null ? "" : __t) + '" ' + ((__t = Number(obj.unifiedSetting.childValue) === Number(child.value) ? "checked" : "") == null ? "" : __t) + ' /> <div class="fx-radio-icon"></div> <div class="shiporder-base-info_float-setting-content"> ' + ((__t = child.title) == null ? "" : __t) + " </div> </label> ";
                if (!_.isEmpty(child.tipHtml)) {
                    __p += ' <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = obj.parseTipHtml(child.tipHtml)) == null ? "" : __t) + " </div> </div> ";
                }
                __p += " </div> ";
            });
            __p += ' </div> </div> <div class="shiporder-base-info_float-setting-item--no-margin"> <div class="shiporder-base-info_float-setting-item fx-radio"> <label> <input class="shiporder-base-info_radio j-setting-option" id="option-title" type="radio" name="ordermode" data-value="2" ' + ((__t = Number(obj.value) === 2 ? "checked" : "") == null ? "" : __t) + ' /> <div class="fx-radio-icon"></div> <div class="shiporder-base-info_float-setting-content"> ' + ((__t = $t("根据订单业务类型设置")) == null ? "" : __t) + ' </div> </label> <span class="j-order-delivery-mode_edit"> ' + ((__t = $t("编辑")) == null ? "" : __t) + '</span> </div> <div class="shiporder-base-info_order-delivery-mode"> ';
            _.each(obj.orderRecordTypeRelateDeliveryMode, function(val) {
                __p += ' <div class="shiporder-base-info_order-delivery-mode_item"> <span>' + ((__t = $t(val.label)) == null ? "" : __t) + ": </span> <span>" + ((__t = $t(val.configLabel)) == null ? "" : __t) + "</span> </div> ";
            });
            __p += ' </div> </div> <!--提交--> <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-confirm" data-action="' + ((__t = obj.name) == null ? "" : __t) + '"> ' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel" data-action="' + ((__t = obj.name) == null ? "" : __t) + '">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/radio-selection-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-base-info_float-setting-item fx-radio"> <label for="radion"> <input class="shiporder-base-info_radio j-setting-option" type="radio" name="' + ((__t = obj.name) == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '" ' + ((__t = Number(obj.value) === Number(item.value) ? "checked" : "") == null ? "" : __t) + ' /> <div class="fx-radio-icon"></div> <div class="shiporder-base-info_float-setting-content"> ' + ((__t = item.title) == null ? "" : __t) + " </div> </label> ";
                if (!_.isEmpty(item.tipHtml)) {
                    __p += ' <div class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = obj.parseTipHtml(item.tipHtml)) == null ? "" : __t) + " </div> </div> ";
                }
                __p += " ";
                if (!_.isEmpty(item.tipText)) {
                    __p += " <span class='shiporder-tiptext'>" + ((__t = item.tipText) == null ? "" : __t) + '</span> <!-- <div class="shiporder-tip">--> <!-- <i class="shiporder-tip_icon">?</i>--> <!-- <div class="shiporder-tip_intro">--> <!-- ' + ((__t = item.tipText) == null ? "" : __t) + "--> <!-- </div>--> <!-- </div>--> ";
                }
                __p += " <!-- 选项末尾的链接 --> ";
                if (item.link) {
                    __p += ' <span class="shiporder-base-info_float-setting-item__href" data-hash="' + ((__t = "#crmmanage/=/module-businessconfig") == null ? "" : __t) + '" data-type="' + ((__t = item.link.dataType) == null ? "" : __t) + '">' + ((__t = item.link.text) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
                if (item.option) {
                    __p += " " + ((__t = item.option) == null ? "" : __t) + " ";
                }
                __p += " </div> <div class='shiporder-base-info_float-setting-item__desc'>" + ((__t = item.desc) == null ? "" : __t) + "</div> ";
            });
            __p += ' <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-confirm" data-action="' + ((__t = obj.name) == null ? "" : __t) + '">' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel" data-action="' + ((__t = obj.name) == null ? "" : __t) + '">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/tpl-delivery-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-base-info_wrapper"> <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("版本状态")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content"> <p>' + ((__t = $t("仅开启发货单")) == null ? "" : __t) + '</p> </div> </div> <div class="shiporder-base-info_item j-decimal"> <div class="shiporder-base-info_item-title">' + ((__t = $t("精度设置")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = $t("设置发货单的精度")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn j-btn-setting btn-float">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail"> <div class="shiporder-base-info_float-setting-item"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("发货单")) == null ? "" : __t) + '：</div> <div class="shiporder-base-info_float-setting-content"> <input class="j-delivery-decimal fx-form-control" type="number" value="' + ((__t = obj.deliveryNoteDecimal) == null ? "" : __t) + '" onkeyup="value=value.replace(/[^\\d]+/g,\'\')"> <span>' + ((__t = $t("位小数")) == null ? "" : __t) + '</span> <span class="decimal-tips"> ' + ((__t = $t("发货单精度应不可小于订单产品精度")) == null ? "" : __t) + '</span> </div> </div> <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-decimal-confirm">' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div> </div> </div> ";
            if (obj.isForErp || obj.stockStatus === 1) {
                __p += ' <div class="shiporder-base-info_item j-coupling-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单耦合度")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.delivery_note_interaction_model) === 1 ? $t("强耦合") : $t("弱耦合")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-coupling-detail"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.delivery_note_interaction_model) === 1) {
                __p += ' <div class="shiporder-base-info_item j-fillProduct-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单产品填充")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.stock_order_product_fill) === 1 ? $t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行") : $t("新建发货单时，系统不自动填充任何产品到发货单产品明细行")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-fillProduct-detail"> </div> </div> ';
            }
            __p += " ";
            if (!obj.isForErp && !(obj.stockStatus === 1) || Number(obj.delivery_note_interaction_model) === 1) {
                __p += ' <div class="shiporder-base-info_item j-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单编辑")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.isAllowModifySalesOrderProduct) === 2 ? $t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类") : $t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-detail"> </div> </div> ';
            }
            __p += ' <div class="shiporder-base-info_item j-setting-return-goods-invoice-of-sales-order-type"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单退货")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
            if (obj.return_goods_invoice_of_sales_order_type === "one") {
                __p += " <p>" + ((__t = $t("每次仅可基于一张销售订单退货")) == null ? "" : __t) + "</p> ";
            } else {
                __p += " <p>" + ((__t = $t("允许多张销售订单一起退货")) == null ? "" : __t) + "</p> ";
            }
            __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-check"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-return-goods-invoice-of-sales-order-type-detail"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- 库存管理-基础设置 --> <div class="shiporder-base-info_wrapper"> <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("版本状态")) == null ? "" : __t) + "</div> ";
            if (obj.isForErp) {
                __p += ' <div class="shiporder-base-info_item-content"> <p>' + ((__t = $t("对接版")) == null ? "" : __t) + '</p> <p class="shiporder-base-info_item-tips"> ' + ((__t = $t("纷享库存对接版，主要实现与ERP对接，出入库、盘点、调拨等业务均在ERP中处理，并将同步部分库存业务数据到纷享CRM。")) == null ? "" : __t) + "</p> </div> ";
            } else {
                __p += " ";
                if (obj.stockStatus === 1) {
                    __p += ' <div class="shiporder-base-info_item-content"> <p>' + ((__t = $t("仅开启发货单")) == null ? "" : __t) + "</p> </div> ";
                } else {
                    __p += ' <div class="shiporder-base-info_item-content"> <p>' + ((__t = $t("完整版")) == null ? "" : __t) + '</p> <p class="shiporder-base-info_item-tips">' + ((__t = $t("纷享库存完整版，包括出入库、盘点、调拨等业务，均在纷享CRM管理并形成业务闭环。")) == null ? "" : __t) + "</p> </div> ";
                }
                __p += " ";
            }
            __p += ' </div> <div class="shiporder-base-info_item j-decimal"> <div class="shiporder-base-info_item-title">' + ((__t = $t("精度设置")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = $t("设置发货单及库存各个模块的精度")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn j-btn-setting btn-float">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail"> ';
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_float-setting-item shiporder-base-info_item-content-stock"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("库存")) == null ? "" : __t) + '：</div> <div class="shiporder-base-info_float-setting-content"> <input class="j-stock-decimal fx-form-control" type="number" value="' + ((__t = obj.stockDecimal) == null ? "" : __t) + '" ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled" : "") == null ? "" : __t) + " onkeyup=\"value=value.replace(/[^\\d]+/g,'')\"> <span>" + ((__t = $t("位小数")) == null ? "" : __t) + '</span> <span class="decimal-tips"> ' + ((__t = $t("库存精度不可小于订单产品精度，当前订单产品精度为")) == null ? "" : __t) + " " + ((__t = obj.salesOrderProductDecimal) == null ? "" : __t) + "</span> </div> </div> ";
            }
            __p += ' <div class="shiporder-base-info_float-setting-item"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("发货单")) == null ? "" : __t) + '：</div> <div class="shiporder-base-info_float-setting-content"> <input class="j-delivery-decimal fx-form-control" type="number" value="' + ((__t = obj.deliveryNoteDecimal) == null ? "" : __t) + '" onkeyup="value=value.replace(/[^\\d]+/g,\'\')"> <span>' + ((__t = $t("位小数")) == null ? "" : __t) + '</span> <span class="decimal-tips"> ' + ((__t = $t("发货单精度应不可小于订单产品精度，不可大于库存精度")) == null ? "" : __t) + "</span> </div> </div> ";
            if (obj.isPurchaseOrderEnable) {
                __p += ' <div class="shiporder-base-info_float-setting-item"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("采购")) == null ? "" : __t) + '：</div> <div class="shiporder-base-info_float-setting-content"> <input class="j-po-decimal fx-form-control" type="number" value="' + ((__t = obj.purchaseOrderDecimal) == null ? "" : __t) + '" ' + ((__t = Number(obj.stockSwitch) !== 2 ? "disabled" : "") == null ? "" : __t) + " onkeyup=\"value=value.replace(/[^\\d]+/g,'')\"> <span>" + ((__t = $t("位小数")) == null ? "" : __t) + '</span> <span class="decimal-tips"> ' + ((__t = $t("采购订单的精度不可大于库存精度")) == null ? "" : __t) + "</span> </div> </div> ";
            }
            __p += ' <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-decimal-confirm">' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + "</div> </div> </div> </div> ";
            if (obj.isVersionB || obj.isVersionDeliveryOnly) {
                __p += ' <div class="shiporder-base-info_item j-coupling-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单耦合度")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.delivery_note_interaction_model) === 1 ? $t("强耦合") : $t("弱耦合")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-coupling-detail"> </div> </div> ';
            }
            __p += " ";
            if (!obj.isForErp && !(obj.stockStatus === 1) || Number(obj.delivery_note_interaction_model) === 1) {
                __p += ' <div class="shiporder-base-info_item j-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单编辑")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.isAllowModifySalesOrderProduct) === 2 ? $t("订单部分发货/全部发货时，允许编辑订单产品的数量和种类") : $t("订单部分发货/全部发货时，不允许编辑订单产品的数量和种类")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-detail"> </div> </div> ';
                if (obj.isForErp) {
                    __p += ' <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单超发")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p> ' + ((__t = $t("根据订单的业务类型，来设置是否允许超出订单产品的数量发货")) == null ? "" : __t) + ' <a href="#crmmanage/=/module-businessconfig" target="_blank">' + ((__t = $t("设置")) == null ? "" : __t) + "</a> </p> </div> </div> ";
                }
                __p += " ";
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-validate") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单校验")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
                if (Number(obj.validateOrderType) === 1) {
                    __p += " <p>" + ((__t = $t("可用库存数量不足时，不可提交订单")) == null ? "" : __t) + "</p> ";
                } else if (Number(obj.validateOrderType) === 2) {
                    __p += " <p>" + ((__t = $t("可用库存数量不足时，仍可提交订单")) == null ? "" : __t) + "</p> ";
                } else {
                    __p += " <p>" + ((__t = $t("根据订单的业务类型，设置不同的校验规则")) == null ? "" : __t) + "</p> ";
                }
                __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-check ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-validate-detail"></div> </div> ';
            }
            __p += " ";
            if (obj.isVersionA || Number(obj.delivery_note_interaction_model) === 1) {
                __p += ' <div class="shiporder-base-info_item j-fillProduct-edit"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单产品填充")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.stock_order_product_fill) === 1 ? $t("新建发货单时，系统自动将可发货的订单产品，全部填充到发货单产品明细行") : $t("新建发货单时，系统不自动填充任何产品到发货单产品明细行")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-fillProduct-detail"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-warehouse") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订货模式")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
                if (Number(obj.orderWarehouseTypeConfigRangeCode) === 1) {
                    __p += " <p> <span>" + ((__t = $t("统一设置")) == null ? "" : __t) + ": </span> ";
                    if (Number(obj.orderWarehouseType) === 1) {
                        __p += " <span>" + ((__t = $t("单一仓库订货")) == null ? "" : __t) + "</span> ";
                    } else if (Number(obj.orderWarehouseType) === 2) {
                        __p += " <span>" + ((__t = $t("合并仓库订货")) == null ? "" : __t) + "</span> ";
                    } else {
                        __p += " <span>" + ((__t = $t("无仓库订货")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " </p> ";
                } else {
                    __p += " <p>" + ((__t = $t("根据订单业务类型设置")) == null ? "" : __t) + "</p> ";
                }
                __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-warehouse ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-warehouse-detail"></div> </div> ';
            }
            __p += ' <div class="shiporder-base-info_item j-setting-return-goods-invoice-of-sales-order-type"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单退货")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
            if (obj.return_goods_invoice_of_sales_order_type === "one") {
                __p += " <p>" + ((__t = $t("每次仅可基于一张销售订单退货")) == null ? "" : __t) + "</p> ";
            } else {
                __p += " <p>" + ((__t = $t("允许多张销售订单一起退货")) == null ? "" : __t) + "</p> ";
            }
            __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-check"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-return-goods-invoice-of-sales-order-type-detail"></div> </div> ';
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item"> <!-- 货位管理 --> <div class="shiporder-base-info_item-title">' + ((__t = $t("stock.stock_manage.location_manage.title")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <!-- 开启后，将新增货位等对象，新建仓库时可选择是否启用货位管理，启用后出入库均需填写货位信息 --> <p>' + ((__t = $t("stock.stock_manage.location_manage.describe_text")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-switch j-setting-location_management ' + ((__t = obj.location_management == 1 ? "switch-on switch-disabled" : "") == null ? "" : __t) + ' "> </div> </div> </div> ';
            }
            __p += " <!-- 【发货单校验】发货通知单 开关展示条件：B类库存 且 未开启弱耦合的开关 --> ";
            if (obj.isVersionB && obj.delivery_note_interaction_model == 1) {
                __p += ' <div class="shiporder-base-info_item j-setting-delivery-stock-related-check"> <div class="shiporder-base-info_item-title">' + ((__t = $t("stock.stock_manage.deliverynote_verification")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
                if (obj.delivery_stock_related_check == 1) {
                    __p += " <p>" + ((__t = $t("stock.stock_manage.weak_stock")) == null ? "" : __t) + "</p> ";
                } else {
                    __p += " <p>" + ((__t = $t("stock.stock_manage.strong_stock")) == null ? "" : __t) + "</p> ";
                }
                __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-check"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-delivery-stock-related-check"></div> </div> ';
            }
            __p += " ";
            if (obj.isVersionA && obj.isPurchaseOrderEnable) {
                __p += ' <div class="shiporder-base-info_item j-setting-enable_edit_purchase_order_when_in_stock"> <div class="shiporder-base-info_item-title">' + ((__t = $t("采购订单编辑")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> ';
                if (obj.enable_edit_purchase_order_when_in_stock === "1") {
                    __p += " <p>" + ((__t = $t("采购订单部分入库/全部入库后，不允许编辑采购订单产品的数量和种类")) == null ? "" : __t) + "</p> ";
                } else {
                    __p += " <p>" + ((__t = $t("采购订单部分入库/全部入库后，允许编辑采购订单产品的数量和种类")) == null ? "" : __t) + "</p> ";
                }
                __p += ' <div class="shiporder-base-info_item-btn btn-setting btn-check"> ' + ((__t = $t("设置")) == null ? "" : __t) + ' </div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-enable_edit_purchase_order_when_in_stock_detail"></div> </div> ';
            }
            __p += " ";
            if (obj.isVersionA) {
                __p += ' <div class="shiporder-base-info_item j-purchase-order-calculation"> <div class="shiporder-base-info_item-title">' + ((__t = $t("采购订单计算")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.purchase_order_calculation) === 1 ? $t("用户手动计算") : $t("系统自动计算")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-purchase-order-calculation"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1 && obj.isForErp == false && obj.isShowStockGreyCheckSwitch) {
                __p += ' <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("盘点暗盘模式")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = $t("开启后，将新增暗盘业务类型的盘点单，盘点时不显示系统数量和盘盈盘亏数量")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-switch j-stock-grey-check-switch ' + ((__t = obj.stockGreyCheckSwitch == 1 ? "switch-on switch-disabled" : "") == null ? "" : __t) + ' "></div> </div> </div> ';
            }
            __p += " ";
            if (!obj.isForErp && Number(obj.stockSwitch) !== 1 || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) {
                __p += ' <div class="shiporder-base-info_item j-setting-stock-check-note-update-stock-type"> <div class="shiporder-base-info_item-title">' + ((__t = $t("盘点后库存调整")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.stockCheckNoteUpdateStockType) === 1 ? $t("盘点单确认后，需要手动操作盘盈入库、盘亏出库来调整库存") : $t("盘点单确认后，系统自动生成盘盈入库单、盘亏出库单来进行库存调整")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-stock-check-note-update-stock-type-detail"></div> </div> ';
            }
            __p += " <!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 --> ";
            if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) {
                __p += ' <div class="shiporder-base-info_item j-stock-check-note-product-source-type"> <!-- 开关文案：盘点产品来源 --> <div class="shiporder-base-info_item-title">' + ((__t = $t("stock.inventory_of_product_source")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.stock_check_note_product_source_type) === 1 ? $t("库存") : $t("产品")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-edit-stock-check-note-product-source-type"> </div> </div> ';
            }
            __p += " <!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 --> ";
            if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) {
                __p += ' <div class="shiporder-base-info_item j-setting-requisition-note-automatic-outbound"> <!-- 调拨自动出库 --> <div class="shiporder-base-info_item-title">' + ((__t = $t("stock.RequisitionNoteObj.automatic-outbound-setting.title")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <!-- 调拨单确认后，系统自动处理调拨出库/调拨单确认后，需手动操作调拨出库 --> <p>' + ((__t = Number(obj.requisitionNoteAutomaticOutbound) === 1 ? $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-automatic") : $t("stock.RequisitionNoteObj.automatic-outbound-setting.radio-mannul")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-requisition-note-automatic-outbound-detail"> </div> </div> ';
            }
            __p += " <!-- 开关展示条件：A类库存，或者B类库存且开了渠道库存/备件库存的租户 --> ";
            if (!obj.isForErp || obj.isForErp && (obj.distribution_stock_switch == 2 || obj.spare_part_inventory_switch == 2)) {
                __p += ' <div class="shiporder-base-info_item j-setting-requisition-note-automatic-inbound"> <div class="shiporder-base-info_item-title">' + ((__t = $t("调拨确认入库")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.requisitionNoteAutomaticInbound) === 1 ? $t("调拨单确认后，系统自动处理调拨确认入库") : $t("调拨单确认后，需手动操作调拨确认入库")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-setting-requisition-note-automatic-inbound-detail"> </div> </div> ';
            }
            __p += " <!-- 盘点冻结仓库 --> <!-- ";
            if (Number(obj.stockSwitch) !== 1 && obj.isForErp == false) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-freeze") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("stock.stock_manage.stock_check_freeze_warehouse")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.stockCheckFreezeWarehouse) === 1 ? $t("盘点时，冻结盘点仓库，不可再新建任何与出入库有关的业务单据") : $t("盘点时，不冻结盘点仓库，仍可新建与出入库有关的业务单据")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-stock ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '">设置</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-freeze-detail"> </div> </div> ';
            }
            __p += " --> <!-- 【多语显示】 stock.stock_manage.inventory_display.dot_not_display_empty_and_discontinued 不显示可用库存和实际库存为0的库存记录，不显示已下架产品的库存记录 stock.stock_manage.inventory_display.dot_not_display_empty_stock 不显示可用库存和实际库存为0的库存记录 stock.stock_manage.inventory_display.dot_not_display_discontinued_product 不显示已下架产品的库存记录 --> ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-stock") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("库存显示")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p> ';
                if (obj.isNotShowZeroStockType == 1 && obj.isOnlyShowOnSaleStockType == 1) {
                    __p += " " + ((__t = $t("stock.stock_manage.inventory_display.dot_not_display_empty_and_discontinued")) == null ? "" : __t) + " ";
                } else if (obj.isNotShowZeroStockType == 1) {
                    __p += " " + ((__t = $t("stock.stock_manage.inventory_display.dot_not_display_empty_stock")) == null ? "" : __t) + " ";
                } else if (obj.isOnlyShowOnSaleStockType == 1) {
                    __p += " " + ((__t = $t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product")) == null ? "" : __t) + " ";
                } else {
                    __p += " " + ((__t = $t("未设置")) == null ? "" : __t) + " ";
                }
                __p += ' </p> <div class="shiporder-base-info_item-btn btn-setting btn-stock ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-stock-detail"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-safety") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("库存结余设置")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.safetyStockType) === 1 ? $t("统一设置安全库存与最高库存") : $t("分仓库设置安全库存与最高库存")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-safety-detail"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item ' + ((__t = Number(obj.stockSwitch) === 3 ? "disabled-item" : "j-warning") == null ? "" : __t) + '"> <div class="shiporder-base-info_item-title">' + ((__t = $t("库存预警")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = obj.warningTypeDesc) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting ' + ((__t = Number(obj.stockSwitch) === 3 ? "btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-warning-detail"> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("可用库存显示")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = $t("启用后，在订单新建/编辑页面，显示订货仓库的可用库存数量")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-switch j-stock-switch ' + ((__t = obj.isSalesOrderShowStock == 1 ? "switch-on switch-disabled" : "") == null ? "" : __t) + ' "> </div> </div> </div> ';
            }
            __p += " ";
            if (Number(obj.stockSwitch) !== 1) {
                __p += ' <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("扫码设置")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <div> <p>' + ((__t = $t("开启扫码录入，快速提升单据处理效率")) == null ? "" : __t) + '</p> </div> <div class="shiporder-base-info_item-switch j-stock-scan-switch ' + ((__t = obj.scanCodeType != 10 ? "switch-on" : "") == null ? "" : __t) + ' "></div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-scan-detail"> </div> </div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/baseinfo/tpl4C-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-base-info_wrapper"> <div class="shiporder-base-info_item"> <div class="shiporder-base-info_item-title">' + ((__t = $t("版本状态")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content"> <p>' + ((__t = $t("对接版")) == null ? "" : __t) + '</p> <p class="shiporder-base-info_item-tips">' + ((__t = $t("基础对接版，仅包含ERP仓库和ERP库存两个对象，可升级至高级对接版，获得更丰富的功能及更好的体验。")) == null ? "" : __t) + '</p> </div> </div> <div class="shiporder-base-info_item j-decimal"> <div class="shiporder-base-info_item-title">' + ((__t = $t("精度设置")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <!-- <p>' + ((__t = $t("设置库存精度")) == null ? "" : __t) + "</p>--> <p>" + ((__t = $t("stock.stock_manage.info.text4")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn j-btn-setting btn-float">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail"> <div class="shiporder-base-info_float-setting-item"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("库存")) == null ? "" : __t) + '：</div> <div class="shiporder-base-info_float-setting-content"> <input class="j-stock-decimal fx-form-control" type="number" value="' + ((__t = obj.stockDecimal) == null ? "" : __t) + '" onkeyup="value=value.replace(/[^\\d]+/g,\'\')"> <span>' + ((__t = $t("位小数")) == null ? "" : __t) + '</span> <span class="decimal-tips">' + ((__t = $t("库存精度不可小于订单产品精度")) == null ? "" : __t) + '</span> </div> </div> <div class="shiporder-base-info_float-setting-btns"> <div class="setting-btn crm-btn-primary j-decimal-confirm">' + ((__t = $t("确定")) == null ? "" : __t) + '</div> <div class="setting-btn btn-cancel j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + '</div> </div> </div> </div> <div class="shiporder-base-info_item j-validate"> <div class="shiporder-base-info_item-title">' + ((__t = $t("订单校验")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p>' + ((__t = Number(obj.validateOrderType) === 1 ? $t("可用库存数量不足时，不可提交订单") : $t("可用库存数量不足时，仍可提交订单")) == null ? "" : __t) + '</p> <div class="shiporder-base-info_item-btn btn-setting btn-check">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-validate-detail"> </div> </div> <div class="shiporder-base-info_item j-stock"> <div class="shiporder-base-info_item-title">' + ((__t = $t("库存显示")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_item-content space-between"> <p> ';
            if (obj.isNotShowZeroStockType == 1) {
                __p += " <span>" + ((__t = $t("不显示可用库存和实际库存均为0的库存记录")) == null ? "" : __t) + "</span> ";
            } else {
                __p += " <!-- <span>" + ((__t = $t("显示可用库存和实际库存均为0的库存记录")) == null ? "" : __t) + "</span>--> <span>" + ((__t = $t("stock.stock_manage.info.text5")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </p> <div class="shiporder-base-info_item-btn btn-setting btn-stock">' + ((__t = $t("设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-base-info_item-content shiporder-base-info_setting-detail j-stock-detail"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/erp/erp",["../../view/view","./erpstock"],function(e,t,r){var i=e("../../view/view"),n=e("./erpstock");return i.extend({options:{title:$t("ERP库存管理"),titleLabel:$t("对接版·C类")},getCurPage:function(){return n}})});
define("crm-setting/shiporder/shiporder/page/erp/erpstock",["../../layout/layout"],function(t,e,o){var t=t("../../layout/layout"),i=FS.crmUtil;return t.extend({options:{topInfo:{messages:[$t("已成功对接第三方 ERP将新增ERP仓库和ERP库存两个对象。"),$t("请在第三方中维护库存相关数据纷享侧不可维护库存相关数据。"),$t("可设置订单校验规则以及库存数据的展示规则。"),$t("必须将客户端版本升级至6.3.4或以上，才能在移动端使用。")]},layout:{buttons:[{label:$t("保存"),action:"save_erpstock_config",show:!0}],switchInfo:{name:"erpStock",label:$t("ERP库存状态"),type:"off",desc:""},config:{setting:!0},fields:[{name:"erpValidateOrderType",type:"radio",label:$t("订单校验"),options:[{name:$t("可用库存数量不足时不可提交订单"),value:1},{name:$t("可用库存数量不足时仍可提交订单"),value:2}]},{name:"erpStockShow",type:"checkboxs",label:$t("库存显示"),options:[{name:"erpIsNotShowZeroStockType",label:$t("不显示可用库存和实际库存均为0的库存记录")}]},{name:"erpStockDecimal",type:"singleSelect",label:$t("精度设置"),range:[0,10],tip:""}]}},render:function(){this.super.render.call(this),this.$setting=this.$(".j-settings"),this._loadInfos()},getDescribe:function(){var t=this.super.getDescribe.call(this);return t.loading=!0,t},_loadInfos:function(){var e=this;this.model.fetchErpInfo(function(t){0!==t.errCode?e.showErrMsg():(t.erpStock?(e.changeSwitchType("on"),e.$setting.show()):(e.changeSwitchType("off"),e.$setting.hide()),e.showContent())})},changeSwitchType:function(t){var e=this.widgets.switchInfo;e&&e.setType(t)},switchHandle:function(t){var e,o,n;-1!==["off","on"].indexOf(t)&&(n=(e=this).model.get("erpStock")?(o=$t("关闭后ERP仓库和ERP库存两个对象将不可用提交撤销订单时将不再根据设置的规则校验库存。")+"<br><br>"+$t("您确认要关闭ERP库存吗"),i.confirm(o,$t("关闭ERP库存"),function(){e.model.closeErpStock(function(t){n.hide(),0==t?(e.showTip($t("ERP库存已关闭"),1),e.render()):e.showTip($t("操作失败请稍后重试或联系纷享客服"))},n.$(".b-g-btn"))})):(o=$t("您确认要开启ERP库存吗"),i.confirm(o,$t("开启ERP库存"),function(){e.model.enableErpStock(function(t){n.hide(),2==t?(e.showTip($t("ERP库存已开启"),1),e.render()):e.showTip($t("开启失败请稍后重试或联系纷享客服"))},n.$(".b-g-btn"))})))},doActionHandle:function(t){var e;"save_erpstock_config"===$(t.target).attr("data-action")&&(e=this).model.setErpStockConfig(function(){e.showTip($t("保存成功"),1)},$(t.target))}})});
define("crm-setting/shiporder/shiporder/page/erptip/erptip",["../../view/view","./page"],function(e,r,i){var t=e("../../view/view"),n=e("./page");return t.extend({getCurPage:function(){return n}})});
define("crm-setting/shiporder/shiporder/page/erptip/page",["../../layout/layout","./tip/tip"],function(t,e,i){return t("../../layout/layout").extend({options:{topInfo:{messages:[$t("如果您的企业已购买了ERP，且-db2602ac"),$t("默认包括发货单、仓库、库存等对象"),$t("支持商品开启批次和序列号管理，-886743f7"),$t("仓库和库存数据的维护在ERP处-3766d70b")]},layout:{config:{setting:!0},fields:[{name:"tip",type:"tip",tip:$t("请联系纷享客服或者对应的纷享实施工程师开通")}]}},mycomponents:{tip:t("./tip/tip")}})});
define("crm-setting/shiporder/shiporder/page/erptip/tip/tip",["./tpl-html"],function(t,i,e){e.exports=Backbone.View.extend({template:t("./tpl-html"),initialize:function(){this.fieldAttr=this.getAttr(this.options.name)},render:function(){this.$el.html(this.template({tip:this.fieldAttr.tip}))},getAttr:function(t){return this.model.get("fields")[t]||{}},destroy:function(){this.$el.off(),this.model=null,this.$el.empty(),this.$el=this.el=this.options=null}})});
define("crm-setting/shiporder/shiporder/page/erptip/tip/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="c-shiporder-tip"> <div class="crm-ico-bee"></div> <div class="tip-info">' + __e(obj.tip) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/fs/batch",["../../layout/layout"],function(t,e,n){var t=t("../../layout/layout"),i=FS.crmUtil;return t.extend({options:{topInfo:{messages:[$t("启用批次和序列号管理功能之前，必须先开启库存。"),$t("启用批次和序列号管理功能后，不可停用，请谨慎开启。")]},layout:{buttons:[{label:$t("保存"),action:"save_batch_config",show:!0}],switchInfo:{name:"batch",label:$t("批次和序列号管理状态："),type:"unopen",desc:$t("已启用")},fields:[{name:"willExpireStockWarningType",type:"radio",label:$t("临到期库存预警"),options:[{name:$t("不开启预警"),value:1},{name:$t("开启预警"),value:2}]}]}},render:function(){this.super.render.call(this),this.$setting=this.$(".j-settings"),this._loadInfos()},getDescribe:function(){var t=this.super.getDescribe.call(this);return t.loading=!0,t},_loadInfos:function(){var e=this;this.model.fetchBatch(function(t){0!==t.errCode?e.showErrMsg():(e.changeSwitchType(t),2===t.batchSNSwitch?e.$setting.show():e.$setting.hide(),e.showContent())})},changeSwitchType:function(t){var e,n=this.widgets.switchInfo;n&&(e="unopen",2===t.batchSNSwitch&&(e="info"),n.setType(e))},switchHandle:function(t){var n;"unopen"===t&&(n=this).model.fetchStock(function(t){var e;0!==t.errCode?n.showTip($t("查询库存是否开启失败，请稍后重试或联系纷享客服")):2!==t.stockStatus?n.showTip($t("启用批次和序列号管理之前必须先启用库存，请确认库存成功开启后，再来尝试。")):(t=$t("启用批次和序列号管理功能后，不可停用此功能，是否确定开启？"),e=i.confirm(t,$t("温馨提示"),function(){n.model.enableBatch({submitSelector:e.$(".b-g-btn")}).done(function(t){e.hide(),0==t.Result.StatusCode&&t.Value.isSuccess?(i.remind(1,$t("批次和序列号管理开启成功")),n.render(),CRM.control.refreshAside()):n.showTip(t.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服"))})}))})},doActionHandle:function(t){var e;"save_batch_config"===$(t.target).attr("data-action")&&(e=this).model.setBatchConfig(function(){e.showTip($t("保存成功"),1)},$(t.target))}})});
define("crm-setting/shiporder/shiporder/page/fs/deliverynote",["../../layout/layout"],function(e,t,s){var e=e("../../layout/layout"),o=FS.crmUtil;return e.extend({options:{topInfo:{messages:[$t("订单审核后可新建发货单可维护发货产品和数量支持分批发货。"),$t("单独开启发货单未开启库存的情况下创建发货单不会校验及扣减库存。"),$t("同时开启了发货单和库存的情况下创建发货单时会校验发货仓库的实际库存发货单确认后会自动创建出库单并扣减发货仓库的实际库存。"),$t("发货单一旦开启不可关闭。"),$t("为保证发货单功能的正常使用，请将移动客户端的版本升级至6.3及以上。")],hasService:!0},layout:{buttons:[{label:$t("保存"),action:"save_deliverynote_config",show:!0}],switchInfo:{name:"erpStock",label:$t("发货单状态"),type:"unopen",btnLabel:$t("开启"),desc:$t("已启用"),tipHtml:""},fields:[{name:"expressQueryFuncId",type:"radio",label:$t("stock.stock_manage.logistics_inquiry"),isHidden:!0,options:[{name:$t("stock.stock_manage.use_standard_query_features"),value:1,tipHtml:[{type:"b",label:$t("stock.stock_manage.standard_query_features")},{label:$t("stock.stock_manage.logistics_only_supports")}]},{name:$t("stock.stock_manage.use_advanced_query_features"),value:2,tipHtml:[{type:"b",label:$t("stock.stock_manage.advanced_query_features")},{label:$t("stock.stock_manage.advanced_query_function_is_charge")}]}]},{name:"isAllowModifySalesOrderProduct",type:"radio",label:$t("订单部分发货/全部发货后"),options:[{name:$t("不允许编辑订单产品的种类和数量"),value:1},{name:$t("允许编辑订单产品的种类和数量"),value:2}]}]}},render:function(){this.super.render.call(this),this.$setting=this.$(".j-settings"),this._loadInfos()},getDescribe:function(){var e=this.super.getDescribe.call(this);return e.loading=!0,e},setFields:function(e){this.super.setFields.call(this,e)},_loadInfos:function(){var t=this;this.model.fetchDeliveryNote(function(e){0!==e.errCode?t.showErrMsg():(t.setField("expressQueryFuncId",{isHidden:!e.isPaid}),t.forms.expressQueryFuncId.setStatus(),t.changeSwitchType(e),2===e.deliveryStatus?t.$setting.show():t.$setting.hide(),t.showContent())})},changeSwitchType:function(e){var t=this.widgets.switchInfo;if(t){var s,i,o="unopen";switch((e=e||this.model.toJSON()).deliveryStatus){case 1:s='<p class="shiporder-action_intro shiporder-action_intro__fail">'+_.escape(e.deliveryErrMsg)+"</p>";break;case 2:o="info",s='<p class="shiporder-action_intro">'+$t("发货单已开启建议先去后台设置")+$t("crm.发货单")+$t("对象的字段布局等信息")+"</p>",i=$t("已启用");break;case 3:o="info",i=$t("系统正在处理请耐心等待")}s&&t.setTip(s),t.setType(o,i)}},switchHandle:function(e){var s,i;"unopen"===e&&((s=this)._doLog("delivery"),e=$t("启用发货单后将无法停用确认启用吗"),i=o.confirm(e,$t("发货单启用提示"),function(){s.widgets.switchInfo.setTip(""),s.widgets.switchInfo.setType("info",$t("系统正在处理请耐心等待")),s.model.enableDeliveryNote({submitSelector:i.$(".b-g-btn")}).done(function(e){var t;i.hide(),0==e.Result.StatusCode&&3==e.Value.enableStatus||(0==e.Result.StatusCode&&2==e.Value.enableStatus?(s.showTip($t("发货单开启成功"),1),s.render(),CRM.control.refreshAside()):1029==e.Result.FailureCode?(t=e.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服"),s.model.set("deliveryErrMsg",t),s.widgets.switchInfo.setType("info",t),s.showTip(e.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服"))):s.showTip($t("启用失败请稍后重试或联系纷享客服")))})}))},doActionHandle:function(e){var t;"save_deliverynote_config"===$(e.target).attr("data-action")&&(t=this).model.setDeliveryConfig(function(){t.showTip($t("保存成功"),1)},$(e.target))}})});
define("crm-setting/shiporder/shiporder/page/fs/exchange",["crm-widget/dialog/dialog","../../layout/layout","./stopstock-html","./stockviewtype/stockviewtype"],function(t,e,o){t("crm-widget/dialog/dialog");var n=t("../../layout/layout"),s=(t("./stopstock-html"),FS.crmUtil);return n.extend({mycomponents:{stockViewType:t("./stockviewtype/stockviewtype")},options:{topInfo:{messages:[$t("开启退换货管理后会新增退换货单、退款单两个对象"),$t("退换货单默认包括2种业务类型均不强关联销售订单分别对应退货、换货业务场景"),$t("退款单不强关联销售订单，主要用来处理退货的退款及换货、补发场景下的差价退还")],hasService:!0},layout:{switchInfo:{name:"erpStock",label:$t("退换货管理状态："),type:"unopen",btnLabel:$t("开启"),desc:$t("已启用"),tipHtml:""}}},render:function(){this.super.render.call(this),this._loadInfos()},getDescribe:function(){var t=this.super.getDescribe.call(this);return t.loading=!0,t},_loadInfos:function(){var e=this;this.model.fetchExchangeManageConfig(function(t){0!==t.errCode?e.showErrMsg():(e.changeSwitchType(t),e.showContent())})},changeSwitchType:function(t){var e=this.widgets.switchInfo;if(e){var o,n,s="unopen";switch((t=t||this.model.toJSON()).exchangeReturnNoteStatus){case 2:s="info",o='<p class="shiporder-action_intro">'+$t("退换货单、退款单两个对象已开启")+"，"+$t("建议先去设置对象的字段、布局等信息")+"</p>",n=$t("已启用");break;case 3:s="info",n=$t("系统正在处理请耐心等待")}o&&e.setTip(o),e.setType(s,n)}},switchHandle:function(t){var e,o;"unopen"===t&&(2!==(e=this).model.toJSON().stockStatus?e.showTip($t("stock.stock_manage.info.text14")):(t=$t("stock.stock_manage.info.text15"),o=s.confirm(t,$t("温馨提示"),function(){e.model.enableExchangeReturn({submitSelector:o.$(".b-g-btn")}).done(function(t){o.hide(),0==t.Result.StatusCode?(s.remind(1,$t("退换货管理开启成功")),e.render(),CRM.control.refreshAside()):e.showTip(t.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服"))})})))}})});
define("crm-setting/shiporder/shiporder/page/fs/fs",["../../view/view","../baseinfo/index","../manage/index","../advance/index"],function(e,t,a){var i=e("../../view/view"),n={baseinfo:e("../baseinfo/index").BaseInfo,manage:e("../manage/index").Manage,advance:e("../advance/index")};return i.extend({options:{title:$t("stock.stock_management.stock_management"),titleLabel:"",tabMap:["baseinfo","manage","advance"],tabs:[$t("基础设置"),$t("插件管理"),$t("高级功能")],getCurTab:function(){var e=sessionStorage.getItem("curTab")||0;return sessionStorage.removeItem("curTab"),Number(e)}},initialize:function(){this.setBeginTab();for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];i.prototype.initialize.apply(this,t)},setBeginTab:function(){var e=CRM.util.getTplQueryParams(window.location.href).tabs;this.options.tabMap.map(function(e,t){return t.toString()}).includes(e)&&sessionStorage.setItem("curTab",Number(e))},formatTabs:function(e){var t="";switch(Number(this.model.get("stockType"))){case 3:t=$t("对接版·C类"),e=e.splice(0,1);break;case 4:t=$t("crm.发货单");break;default:t=this.model.get("isForErp")?$t("对接版·B类"):$t("完整版·A类")}return this.options.titleLabel=t,_.isEmpty(e)||!_.isArray(e)||e.length<2?[]:e},getCurPage:function(){var e=this.options.tabMap[this.options.curTab]||"baseinfo";return n[e]},renderTpl:function(){var e=this.getCurPage();this.widgets.page&&this.widgets.page.destroy(),this.widgets.page=new e({el:this.$container.get(0),model:this.model}),this.widgets.page.$el.html('<div class="crm-loading tab-loading"></div>'),this.widgets.page.render()}})});
define("crm-setting/shiporder/shiporder/page/fs/ordersubmit/ordersubmit",["../crm-modules/components/components","./tpl-html"],function(t,e,n){var s=t("../crm-modules/components/components").base,r=FS.crmUtil;n.exports=s.extend({template:t("./tpl-html"),events:{"click .j-comp-enable":"enable"},render:function(){var t=this.fieldAttr;this.$el.html(this.template({label:t.label,text:t.text})),this.changeValue()},setValue:function(t){var e=$(".text-btns",this.$el);t?e.html("<span>".concat($t("已开启"),"</span>")):e.html('<a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-comp-enable">'.concat($t("开启"),"</a>"))},enable:function(){var e=this,t=$t("此功能开启后，将在销售订单的提交页面显示所选仓库（单一仓库订货模式）或所有适用仓库（合并仓库订货模式）的可用库存数量。")+"<br><br>"+$t("开启后，此功能不可关闭，是否确认开启此功能？"),n=r.confirm(t,$t("温馨提示"),function(){e.model.enableOrderShowStock({submitSelector:n.$(".b-g-btn")}).done(function(t){n.hide(),0==t.Result.StatusCode&&t.Value.isSuccess?(r.remind(1,$t("开启成功")),e.set("showStockForOrder",!0),e.renderBtns(!0)):r.alert(t.Vlaue.message||$t("操作失败请稍后尝试或联系纷享客服"))})})},changeValue:function(){var t=this.get(this.options.name);this.setValue(t)}})});
define("crm-setting/shiporder/shiporder/page/fs/ordersubmit/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="shiporder-set"> <label class="shiporder-set_label">' + __e(obj.label) + '</label> <div class="shiporder-text" style="display:flex;"> <div class="text-content"> ' + ((__t = obj.text) == null ? "" : __t) + ' </div> <div class="text-btns" style="margin-left:10px;"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/fs/po",["../../layout/layout"],function(t,e,s){var t=t("../../layout/layout"),o=FS.crmUtil;return t.extend({options:{topInfo:{messages:[$t("stock.crm.setting.shiporder.before_purchasing_module_is_opened"),$t("stock.crm.setting.shiporder.after_purchasing_module_is_opened"),$t("stock.crm.setting.shiporder.after_the_purchase_order_is_confirmed"),$t("stock.crm.setting.shiporder.after_the_purchasing_module_is_opened")]},layout:{buttons:[{label:$t("保存"),action:"save_po_config",show:!0}],switchInfo:{name:"po",label:$t("stock.crm.setting.shiporder.purchasing_module_status"),type:"unopen",desc:$t("已启用")},fields:[{name:"poDecimal",type:"singleSelect",label:$t("stock.crm.setting.shiporder.purchase_order_accuracy"),range:[0,9],tip:""}]}},render:function(){this.super.render.call(this),this.$setting=this.$(".j-settings"),this._loadInfos()},getDescribe:function(){var t=this.super.getDescribe.call(this);return t.loading=!0,t},_loadInfos:function(){var e=this;this.model.fetchPO(function(t){0!==t.errCode?e.showErrMsg():(e.changeSwitchType(t),2!==t.poSwitch?e.$setting.hide():(e.$setting.show(),e.forms.poDecimal.setTip($t("stock.crm.setting.shiporder.current_inventory_accuracy_is")+t.stockDecimal+$t("stock.crm.setting.shiporder.accuracy_of_the_purchase_order_cannot_be_greater"))),e.showContent())})},changeSwitchType:function(t){var e,s,o=this.widgets.switchInfo;o&&(e="unopen",2===t.poSwitch&&(e="info",s='<p class="shiporder-action_intro">'+$t("stock.crm.setting.shiporder.the_purchasing_module_has_been_started")+"</p>"),s&&o.setTip(s),o.setType(e))},switchHandle:function(t){var s;"unopen"===t&&(s=this).model.fetchStock(function(t){var e;0!==t.errCode?s.showTip($t("查询库存是否开启失败，请稍后重试或联系纷享客服")):2!==t.stockStatus?s.showTip($t("stock.crm.setting.shiporder.after_the_inventory_is_successfully_opened")):(t=$t("stock.crm.setting.shiporder.after_enabling_the_purchasing_module"),e=o.confirm(t,$t("温馨提示"),function(){s.model.enablePO({submitSelector:e.$(".b-g-btn")}).done(function(t){e.hide(),0==t.Result.StatusCode&&2==t.Value.enableStatus?(o.remind(1,$t("stock.crm.setting.shiporder.the_purchasing_module_is_successfully_enabled")),s.render(),CRM.control.refreshAside()):s.showTip(t.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服"))})}))})},doActionHandle:function(t){var e;"save_po_config"!==$(t.target).attr("data-action")||(e=this).validatePOConfig()||this.model.setPOConfig(function(){e.showTip($t("保存成功"),1)},$(t.target))},validatePOConfig:function(){var t=!1,e=this.model.toJSON();return e.stockDecimal<e.poDecimal&&(t=!0,this.showTip($t("stock.crm.setting.shiporder.accuracy of the purchase order cannot be greater_tip"))),t}})});
define("crm-setting/shiporder/shiporder/page/fs/stock",["crm-widget/dialog/dialog","../../layout/layout","./stopstock-html","./stockviewtype/stockviewtype","./ordersubmit/ordersubmit"],function(t,e,o){var i=t("crm-widget/dialog/dialog"),s=t("../../layout/layout"),n=t("./stopstock-html"),a=FS.crmUtil;return s.extend({mycomponents:{stockViewType:t("./stockviewtype/stockviewtype"),showStockForOrder:t("./ordersubmit/ordersubmit")},options:{topInfo:{messages:[$t("启用库存之前必须开启发货单。"),$t("启用库存后将增加库存仓库入库单出库单和调拨单等对象。"),$t("库存的数据不可直接编辑必须通过入库单出库单等单据来更新。"),$t("库存开启后可手动停用但停用后不可再手动启用。")],hasService:!0},layout:{buttons:[{label:$t("保存"),action:"save_stock_config",show:!0}],switchInfo:{name:"erpStock",label:$t("库存状态"),type:"unopen",desc:$t("已停用")},fields:[{name:"showStockForOrder",type:"text",label:$t("订单提交"),isHidden:!0,text:$t("stock.stock_manage.info.text10")},{name:"validateOrderType",type:"radio",label:$t("订单校验"),options:[{name:$t("可用库存数量不足时不可提交订单"),value:1,tipHtml:[{type:"b",label:$t("可用库存数量不足时不可提交订单")},{label:$t("此设置项下创建出库单调拨单时校验可用库存可用库存不足不可创建出库单调拨单")}]},{name:$t("可用库存数量不足时仍可提交订单"),value:2,tipHtml:[{type:"b",label:$t("可用库存数量不足时仍可提交订单")},{label:$t("此设置项下创建出库单调拨单时校验实际库存实际库存不足不可创建出库单调拨单")}]}]},{name:"orderWarehouseType",type:"radio",label:$t("订货仓库设置"),options:[{name:$t("crm.单一仓库订货"),value:1,tipHtml:[{type:"b",label:$t("crm.单一仓库订货:")},{label:$t("提交销售订单时需指定订货仓库根据指定的订货仓库来校验库存")}]},{name:$t("crm.合并仓库订货"),value:2,tipHtml:[{type:"b",label:$t("crm.合并仓库订货:")},{label:$t("提交订单时无需手动选择仓库根据所有适用的仓库来校验库存")}]}]},{name:"stockViewType",type:"stockViewType",label:$t("订货通设置"),noEnable:$t("请先购买")+$t("企业互联")+$t("、")+$t("需开通订货通后可设置库存的显示方式"),options:[{name:$t("不显示库存"),value:1},{name:$t("模糊显示库存"),value:3,tipHtml:[{type:"b",label:$t("模糊库存")},{label:$t("当安全库存不为空时模糊库存有缺货少量充足三种")},{label:$t("缺货：可用库存")+"<=0"},{label:$t("少量")+"：0 <"+$t("可用库存")+"<"+$t("安全库存")},{label:$t("充足：安全库存<=可用库存，且可用库存大于0。")},{label:$t("当安全库存为空时模糊库存有缺货充足两种")},{label:$t("缺货：可用库存")+"<=0"},{label:$t("充足：可用库存")+"> 0"}]},{name:$t("精确显示库存"),value:2}]},{name:"stockDecimal",type:"singleSelect",label:$t("精度设置"),range:[0,9],tip:""},{name:"safetyStockType",type:"radio",label:$t("安全库存设置"),options:[{name:$t("统一设置"),value:1,tipHtml:[{label:$t("在产品的安全库存字段中设置对所有仓库均适用")}]},{name:$t("分仓库设置"),value:2,tipHtml:[{label:$t("在库存产品的安全库存字段中设置仅对该产品库存有效")}]}]},{name:"stockWarningType",type:"radio",label:$t("库存预警"),tip:$t("建议先去后台设置产品对象的")+$t("安全库存")+$t("字段和布局信息"),options:[{name:$t("不开启预警"),value:1},{name:$t("开启预警当可用库存小于安全库存时收到预警"),value:2}]},{name:"stockShow",type:"checkboxs",label:$t("库存显示"),options:[{name:"isNotShowZeroStockType",label:$t("stock.stock_manage.inventory_display.dot_not_display_empty_stock")},{name:"isOnlyShowOnSaleStockType",label:$t("stock.stock_manage.inventory_display.dot_not_display_discontinued_product")}]}]}},render:function(){this.super.render.call(this),this.$setting=this.$(".j-settings"),this.$btns=this.$(".shiporder-btns"),this._loadInfos()},getDescribe:function(){var t=this.super.getDescribe.call(this);return t.loading=!0,t},_loadInfos:function(){var e=this;this.model.fetchStock(function(t){0!==t.errCode?e.showErrMsg():(t.hasUiEventModule&&(e.setField("showStockForOrder",{isHidden:!1}),e.forms.showStockForOrder.setStatus()),e.changeSwitchType(t),2===t.stockStatus?(e.$setting.show(),e.$btns.show(),e.forms.stockDecimal.setTip($t("当前订单产品的小数位数为")+t.salesOrderProductDecimal+(2===t.poSwitch?$t("stock.stock_manage.info.text11")+t.poDecimal:""))):3===t.stockStatus?e.disableSettings():e.$setting.hide(),e.showContent())})},changeSwitchType:function(t){var e=this.widgets.switchInfo;if(e){var o,s="off";switch(t.stockStatus){case 2:s="on",o='<p class="shiporder-action_intro">'+$t("库存已开启建议先去后台设置")+$t("crm.库存")+$t("、")+$t("crm.仓库")+$t("、")+$t("crm.入库单")+$t("对象的字段布局等信息")+"</p>";break;case 3:s="info"}o&&e.setTip(o),e.setType(s)}},disableSettings:function(){this.$setting.show(),this.$btns.hide(),_.each(this.forms,function(t,e){t.disabeld&&t.disabeld(),t.setStatus&&t.setStatus()})},switchHandle:function(t){if(-1!==["off","on"].indexOf(t)){var e,o,s=this,t=this.model.toJSON();if(s._doLog("stock"),1===t.stockStatus){if(2!==t.deliveryStatus)return void s.showTip($t("启用库存之前必须先启用发货单请确认发货单成功开启后再来尝试。"));o=$t("库存启用后会对销售订单和发货单的使用产生一定的影响开启前请务必确认您已知晓这些影响。")+"<br><br>"+$t("库存启用成功后将无法关闭您确认要启用库存吗"),e=a.confirm(o,$t("库存启用提示"),function(){s.model.changeStockStatus({data:{stockSwitch:"2"},submitSelector:e.$(".b-g-btn")}).done(function(t){e.hide(),0==t.Result.StatusCode&&0==t.Result.FailureCode?(a.remind(1,$t("库存开启成功")),s.render(),CRM.control.refreshAside()):1001==t.Result.FailureCode?s.showTip(t.Result.FailureMessage||$t("启用失败请稍后重试或联系纷享客服")):s.showTip($t("启用失败请稍后重试或联系纷享客服"))})})}2===t.stockStatus&&this.showStopStockDialog(function(t){0==t.Result.StatusCode&&0==t.Result.FailureCode&&t.Value.isSuccess?(a.remind(1,$t("库存停用成功")),s.render()):1001==t.Result.FailureCode?s.showTip(t.Result.FailureMessage||$t("stock.stock_manage.info.text12")):s.showTip($t("stock.stock_manage.info.text12"))})}},showStopStockDialog:function(e){function o(){a&&clearInterval(a),t&&t.destroy&&t.destroy(),t=a=null}var a,s=this,t=new i({title:$t("库存停用"),classPrefix:"crm-c-dialog crm-c-dialog-stopstock",width:650,showBtns:!0,showScroll:!1,content:n()});t.on("hide",o),t.on("dialogCancel",o),t.on("dialogEnter",function(t){s.model.changeStockStatus({data:{stockSwitch:"3"},submitSelector:$('[action-type="dialogEnter"]',this.element)}).done(function(t){o(),e&&e(t)})}),t.on("agree",function(t){var e,t=$(t.target);this._canAgree&&(t.toggleClass("on"),e=$('[action-type="dialogEnter"]',this.element),t.hasClass("on")?e.removeClass("btn-disabled"):e.addClass("btn-disabled"))}),t.on("show",function(t){var e=this,o=(e._canAgree=!1,$('[action-type="dialogEnter"]',e.element).addClass("btn-disabled"),30),s=$(".stopstock-countdown",e.element);a&&clearInterval(a),a=setInterval(function(){0===--o?(e._canAgree=!0,a&&clearInterval(a),a=null,s.text("")):s.text(o+"s")},1e3)}),t.show()},doActionHandle:function(t){var e;"save_stock_config"!==$(t.target).attr("data-action")||(e=this).validateStockConfig()||this.model.setStockConfig(function(){e.showTip($t("保存成功"),1)},$(t.target))},validateStockConfig:function(){var t=!1,e=this.model.toJSON();return e.stockDecimal<e.salesOrderProductDecimal?(t=!0,this.showTip($t("库存精度不可小于订单产品精度请重新设置"))):2===e.poSwitch&&e.stockDecimal<e.poDecimal&&(t=!0,this.showTip($t("stock.stock_manage.info.text13"))),t}})});
define("crm-setting/shiporder/shiporder/page/fs/stockviewtype/stockviewtype",["../crm-modules/components/components","./tpl-html"],function(e,t,n){var o=e("../crm-modules/components/components").Radio;n.exports=o.extend({template:e("./tpl-html"),render:function(){var e=this.fieldAttr,t=this.get(this.options.name),n=this.get("enableSetStockView");this.$el.html(this.template({label:e.label,options:e.options,value:t,enableSetStockView:n,noEnable:e.noEnable,parseTipHtml:function(e){var t="";return _.each(e,function(e){"b"===e.type?t+="<b>"+_.escape(e.label)+"</b>":t+="<p>"+_.escape(e.label)+"</p>"}),t}}))}})});
define("crm-setting/shiporder/shiporder/page/fs/stockviewtype/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-set ' + ((__t = obj.enableSetStockView ? "" : "shiporder-set--flex") == null ? "" : __t) + '"> <label class="shiporder-set_label">' + __e(obj.label) + "</label> ";
            if (!obj.enableSetStockView) {
                __p += " <p>" + ((__t = obj.noEnable) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <div class="shiporder-radios"> ';
                _.each(obj.options, function(item) {
                    __p += ' <div class="shiporder-radio"> <div class="shiporder-radio_set ' + ((__t = obj.value == item.value ? "on" : "") == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '"> <i class="shiporder-radio_icon"></i> <span>' + __e(item.name) + "</span> ";
                    if (!_.isEmpty(item.tipHtml)) {
                        __p += ' <span class="shiporder-tip"> <i class="shiporder-tip_icon">?</i> <div class="shiporder-tip_intro"> ' + ((__t = obj.parseTipHtml(item.tipHtml)) == null ? "" : __t) + " </div> </span> ";
                    }
                    __p += " </div> </div> ";
                });
                __p += " </div> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/fs/stopstock-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="stopstock-content"> <p>' + ((__t = $t("库存停用前请确保您已知晓停用库存的结果如下")) == null ? "" : __t) + "</p> <p>1." + ((__t = $t("库存停用后相关对象仍然可用。")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("开启库存后创建的历史订单数据仍然按照库存开启时的逻辑处理。")) == null ? "" : __t) + "</p> <p>3." + ((__t = $t("停用后创建订单时不再校验库存创建发货单也不再扣减实际库存。")) == null ? "" : __t) + "</p> <p>4." + ((__t = $t("库存停用后不可再手动启用。")) == null ? "" : __t) + '</p> <p><i class="stopstock-checkbox" action-type="agree"></i><span class="stopstock-countdown">30s</span>' + ((__t = $t("我已知晓停用库存的结果并确定这些结果不会影响业务的正常流转。")) == null ? "" : __t) + "</p> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/manage/cardTpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="shiporder-manage_category-card-title">' + ((__t = $t(obj.title)) == null ? "" : __t) + '</div> <div class="shiporder-manage_category-card-content">' + ((__t = $t(obj.content)) == null ? "" : __t) + '</div> <div class="shiporder-manage_category-card-footer"> <div class="shiporder-manage_category-card-options"> <!-- <div class="shiporder-manage_category-card-option j-detail">详情</div> --> <div class="shiporder-manage_category-card-option ' + ((__t = obj.settingTriger) == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + '</div> </div> <div class="shiporder-manage_category-card-switcher"> <i class="oprate-btn_switch ' + ((__t = obj.switchTriger) == null ? "" : __t) + " " + ((__t = obj.isOn ? "oprate-btn_switch--on" : "") == null ? "" : __t) + " " + ((__t = obj.disabled ? "oprate-btn_switch--disabled" : "") == null ? "" : __t) + ' " ></i> </div> </div>';
        }
        return __p;
    };
});
function _slicedToArray(t,n){return _arrayWithHoles(t)||_iterableToArrayLimit(t,n)||_unsupportedIterableToArray(t,n)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,n){var e;if(t)return"string"==typeof t?_arrayLikeToArray(t,n):"Map"===(e="Object"===(e={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:e)||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_arrayLikeToArray(t,n):void 0}function _arrayLikeToArray(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,a=Array(n);e<n;e++)a[e]=t[e];return a}function _iterableToArrayLimit(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var a,i,o,r,l=[],s=!0,p=!1;try{if(o=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;s=!1}else for(;!(s=(a=o.call(e)).done)&&(l.push(a.value),l.length!==n);s=!0);}catch(t){p=!0,i=t}finally{try{if(!s&&null!=e.return&&(r=e.return(),Object(r)!==r))return}finally{if(p)throw i}}return l}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/shiporder/shiporder/page/manage/components/AutoConfirmReceiptDialog",["crm-modules/common/stock/api"],function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.AutoConfirmReceiptDialog=void 0;var a=t("crm-modules/common/stock/api"),t=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync='visible'\n\t\t\t  ref='dialog1'\n\t\t\t  :append-to-body='true'\n\t\t\t  :title=\"$t('确认收货-参数设置')\"\n\t\t\t  @closed='handleClosed'\n\t\t\t  class='auto-confirm-receipt-dialog'\n\t\t\t>\n\t\t\t\t<div v-loading=\"loading\">\n\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab\">\n\t\t\t\t\t <fx-radio-group v-model=\"tab\">\n\t\t\t\t\t\t <fx-radio :label=\"'1'\">\n\t\t\t\t\t\t\t <span>{{ $t('系统自动确认收货') }}</span>\n\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t\t <br>\n\t\t\t\t\t\t <fx-radio :label=\"'2'\" v-if=\"hasOpenSign\">\n\t\t\t\t\t\t\t <span>{{ $t('手动确认收货，且必须电子签名') }}</span>\n\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t </fx-radio-group>\n\t\t\t\t\t </div>\n\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content\" v-show=\"tab=='1'\">\n\t\t\t\t\t\t<fx-radio-group v-model='value'>\n\t\t\t\t\t\t\t<fx-radio :label='1'>\n\t\t\t\t\t\t\t\t<span>{{ $t('发货单确认后') }}\n\t\t\t\t\t\t\t\t\t<fx-input-number\n\t\t\t\t\t\t\t\t\t\tclass='auto-confirm-receipt-dialog__number'\n\t\t\t\t\t\t\t\t\t\tv-model='days'\n\t\t\t\t\t\t\t\t\t\ttype='number'\n\t\t\t\t\t\t\t\t\t\t:min='1'\n\t\t\t\t\t\t\t\t\t\t:max='365'\n\t\t\t\t\t\t\t\t\t\t:step='1'\n\t\t\t\t\t\t\t\t\t\t:step-strictly='true'\n\t\t\t\t\t\t\t\t\t\t:controls='false'\n\t\t\t\t\t\t\t\t\t></fx-input-number>\n\t\t\t\t\t\t\t\t  {{ $t('天，系统自动确认收货') }}\n\t\t\t\t\t\t\t\t  <i class='icon-tips'></i>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t<br>\n\t\t\t\t\t\t\t<fx-radio :label='2'>\n\t\t\t\t\t\t\t\t<span>{{ $t('发货单物流签收后，系统自动确认收货') }}</span>\n\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t<p class='auto-confirm-receipt-dialog__radio-tips'>{{ $t('需要在发货单填写正确的物流信息，并订阅物流签收提醒，每次订阅均会扣除一次物流查询次数。') }}</p>\n\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content\" v-show=\"tab=='2'\">\n\t\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content__inner\">\n\t\t\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content__row\">\n\t\t\t\t\t\t\t\t<span>{{$t('上游签署人')}}</span>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tv-model=\"SSupStreamSigner\"\n\t\t\t\t\t\t\t\t:options=\"options\"\n\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\tplaceholder=\"请选择\">\n\t\t\t\t\t\t\t\t</fx-select>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content__row\">\n\t\t\t\t\t\t\t\t<span>{{$t('下游签署人')}}</span>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tv-model=\"SSdownStreamSigner\"\n\t\t\t\t\t\t\t\t:options=\"options1\"\n\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\tplaceholder=\"请选择\">\n\t\t\t\t\t\t\t\t</fx-select>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"auto-confirm-receipt-dialog__tab__content__row\" style='position:relative;'>\n\t\t\t\t\t\t\t\t<a style=\"position: absolute;right: -110px;width: 100px;top: calc(50% - 11px);height: 22px;cursor: pointer;\" @click='handleClick'>{{$t('设置打印模板')}}</a>\n\t\t\t\t\t\t\t\t<span>{{$t('模板')}}</span>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tv-model=\"SSprintTemplateId\"\n\t\t\t\t\t\t\t\t:options=\"optionsTemplate\"\n\t\t\t\t\t\t\t\tsize=\"small\"\n\t\t\t\t\t\t\t\tplaceholder=\"请选择\">\n\t\t\t\t\t\t\t\t</fx-select>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n    \t\t\t\t<fx-button\n    \t\t\t\t\ttype='primary'\n    \t\t\t\t\t@click='handleConfirm'\n    \t\t\t\t\tsize='small'\n    \t\t\t\t\t:disabled=\"loading\"\n    \t\t\t\t>{{$t('确 定')}}</fx-button>\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click='handleClosed'\n    \t\t\t\t\tsize='small'\n    \t\t\t\t\t:disabled=\"loading\"\n    \t\t\t\t>{{$t('取 消')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",props:{defaultValue:Number,defaultDays:String,defaultSSstatus:String,defaultSSupStreamSigner:String,defaultSSdownStreamSigner:String,defaultSSprintTemplateId:String,defaultSSsignAppType:String},data:function(){return{loading:!0,visible:!0,value:null,days:null,hasOpenSign:!1,SSstatus:"",SSupStreamSigner:"0",SSdownStreamSigner:"0",SSprintTemplateId:"",SSsignAppType:"",tab:"1",options:[{value:"1",label:$t("stock.stock_manage.deliverynote_manager")},{value:"2",label:$t("stock.stock_manage.account_manager")}],options1:[{value:"1",label:$t("stock.stock_manage.account_contact")},{value:"2",label:$t("stock.stock_manage.consignee")}],optionsTemplate:[{}]}},created:function(){this.value=this.defaultValue,this.days=this.defaultDays,this.getSignConfig()},methods:{handleClick:function(){window.open(FS.util.getFxHash("#crmmanage/=/module-templatemanage",{mod:"manage"}))},getValue:function(){return{value:this.value,days:this.days,hasOpenSign:this.hasOpenSign,SSstatus:this.tab,SSupStreamSigner:this.SSupStreamSigner,SSdownStreamSigner:this.SSdownStreamSigner,SSprintTemplateId:this.SSprintTemplateId,SSsignAppType:this.SSsignAppType}},handleConfirm:function(){this.$emit("confirm",this.getValue()),this.handleClosed()},handleClosed:function(){this.$emit("close",this.getValue()),this.$destroy()},getSignConfig:function(){var e=this;return this.loading=!0,(0,a.queryOpenSign)().then(function(t){e.hasOpenSign=t.hasOpenSign,e.SSsignAppType=t.signAppType}).then(function(){var t;if(e.hasOpenSign)return t=[(0,a.getPluginsConfigValueByKey)({key:"delivery_note_support_signatures"}),(0,a.getDnTemplates)()],Promise.all(t)}).then(function(t){var t=_slicedToArray(t,2),n=t[0],t=t[1];e.SSstatus=n.status,e.tab=n.status,e.SSupStreamSigner=n.upStreamSigner,e.SSdownStreamSigner=n.downStreamSigner,e.SSprintTemplateId=n.printTemplateId,e.optionsTemplate=t}).catch(function(t){}).finally(function(){e.loading=!1})}}});n.AutoConfirmReceiptDialog=t});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var s,a={},t=Object.prototype,c=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",e=n.asyncIterator||"@@asyncIterator",i=n.toStringTag||"@@toStringTag";function h(t,n,e,r){return Object.defineProperty(t,n,{value:e,enumerable:!r,configurable:!r,writable:!r})}try{h({},"")}catch(s){h=function(t,n,e){return t[n]=e}}function u(t,n,e,r){var i,o,a,u,n=n&&n.prototype instanceof p?n:p,n=Object.create(n.prototype);return h(n,"_invoke",(i=t,o=e,a=new w(r||[]),u=1,function(t,n){if(3===u)throw Error("Generator is already running");if(4===u){if("throw"===t)throw n;return{value:s,done:!0}}for(a.method=t,a.arg=n;;){var e=a.delegate;if(e){e=function t(n,e){var r=e.method,i=n.i[r];if(i===s)return e.delegate=null,"throw"===r&&n.i.return&&(e.method="return",e.arg=s,t(n,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;r=l(i,n.i,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,f;i=r.arg;return i?i.done?(e[n.r]=i.value,e.next=n.n,"return"!==e.method&&(e.method="next",e.arg=s),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}(e,a);if(e){if(e===f)continue;return e}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(1===u)throw u=4,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=3;e=l(i,o,a);if("normal"===e.type){if(u=a.done?4:2,e.arg===f)continue;return{value:e.arg,done:a.done}}"throw"===e.type&&(u=4,a.method="throw",a.arg=e.arg)}}),!0),n}function l(t,n,e){try{return{type:"normal",arg:t.call(n,e)}}catch(t){return{type:"throw",arg:t}}}a.wrap=u;var f={};function p(){}function o(){}function d(){}var n={},m=(h(n,r,function(){return this}),Object.getPrototypeOf),m=m&&m(m(S([]))),g=(m&&m!==t&&c.call(m,r)&&(n=m),d.prototype=p.prototype=Object.create(n));function y(t){["next","throw","return"].forEach(function(n){h(t,n,function(t){return this._invoke(n,t)})})}function v(a,u){var n;h(this,"_invoke",function(e,r){function t(){return new u(function(t,n){!function n(t,e,r,i){var o,t=l(a[t],a,e);if("throw"!==t.type)return(e=(o=t.arg).value)&&"object"==_typeof(e)&&c.call(e,"__await")?u.resolve(e.__await).then(function(t){n("next",t,r,i)},function(t){n("throw",t,r,i)}):u.resolve(e).then(function(t){o.value=t,r(o)},function(t){return n("throw",t,r,i)});i(t.arg)}(e,r,t,n)})}return n=n?n.then(t,t):t()},!0)}function _(t){this.tryEntries.push(t)}function b(t){var n=t[4]||{};n.type="normal",n.arg=s,t[4]=n}function w(t){this.tryEntries=[[-1]],t.forEach(_,this),this.reset(!0)}function S(n){if(null!=n){var e,t=n[r];if(t)return t.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length))return e=-1,(t=function t(){for(;++e<n.length;)if(c.call(n,e))return t.value=n[e],t.done=!1,t;return t.value=s,t.done=!0,t}).next=t}throw new TypeError(_typeof(n)+" is not iterable")}return h(g,"constructor",o.prototype=d),h(d,"constructor",o),o.displayName=h(d,i,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===o||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,h(t,i,"GeneratorFunction")),t.prototype=Object.create(g),t},a.awrap=function(t){return{__await:t}},y(v.prototype),h(v.prototype,e,function(){return this}),a.AsyncIterator=v,a.async=function(t,n,e,r,i){void 0===i&&(i=Promise);var o=new v(u(t,n,e,r),i);return a.isGeneratorFunction(n)?o:o.next().then(function(t){return t.done?t.value:o.next()})},y(g),h(g,i,"Generator"),h(g,r,function(){return this}),h(g,"toString",function(){return"[object Generator]"}),a.keys=function(t){var n,e=Object(t),r=[];for(n in e)r.unshift(n);return function t(){for(;r.length;)if((n=r.pop())in e)return t.value=n,t.done=!1,t;return t.done=!0,t}},a.values=S,w.prototype={constructor:w,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=s,this.done=!1,this.delegate=null,this.method="next",this.arg=s,this.tryEntries.forEach(b),!t)for(var n in this)"t"===n.charAt(0)&&c.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=s)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var e=this;function t(t){o.type="throw",o.arg=n,e.next=t}for(var r=e.tryEntries.length-1;0<=r;--r){var i=this.tryEntries[r],o=i[4],a=this.prev,u=i[1],c=i[2];if(-1===i[0])return t("end"),!1;if(!u&&!c)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=a){if(a<u)return this.method="next",this.arg=s,t(u),!0;if(a<c)return t(c),!1}}},abrupt:function(t,n){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}var o=(i=i&&("break"===t||"continue"===t)&&i[0]<=n&&n<=i[2]?null:i)?i[4]:{};return o.type=t,o.arg=n,i?(this.method="next",this.next=i[2],f):this.complete(o)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),f},finish:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var e=this.tryEntries[n];if(e[2]===t)return this.complete(e[4],e[3]),b(e),f}},catch:function(t){for(var n=this.tryEntries.length-1;0<=n;--n){var e,r,i=this.tryEntries[n];if(i[0]===t)return"throw"===(e=i[4]).type&&(r=e.arg,b(i)),r}throw Error("illegal catch attempt")},delegateYield:function(t,n,e){return this.delegate={i:S(t),r:n,n:e},"next"===this.method&&(this.arg=s),f}},a}define("crm-setting/shiporder/shiporder/page/manage/components/BatchSnDialog",["crm-modules/common/stock/api"],function(t,n,e){var r=this&&this.__awaiter||function(t,a,u,c){return new(u=u||Promise)(function(e,n){function r(t){try{o(c.next(t))}catch(t){n(t)}}function i(t){try{o(c.throw(t))}catch(t){n(t)}}function o(t){var n;t.done?e(t.value):((n=t.value)instanceof u?n:new u(function(t){t(n)})).then(r,i)}o((c=c.apply(t,a||[])).next())})},i=(Object.defineProperty(n,"__esModule",{value:!0}),n.BatchSnDialog=void 0,t("crm-modules/common/stock/api")),t=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync="visible"\n\t\t\t  ref="dialog1"\n\t\t\t  :append-to-body="true"\n\t\t\t  :title="$t(\'批次与序列号-参数设置\')"\n\t\t\t  width="550px"\n\t\t\t  @closed="handleClosed"\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<fx-tabs\n\t\t\t\t\t\tv-model=\'activeName\'\n\t\t\t \t\t\tv-loading="loading"\n\t\t\t\t\t>\n    \t\t\t\t<fx-tab-pane\n    \t\t\t\t\t:label="$t(\'序列号参数\')"\n    \t\t\t\t\tname=\'sn\'\n    \t\t\t\t>\n\t\t\t\t\t\t<div v-if=\'has_open_multi_sn\'>\n\t\t\t\t\t\t\t<span>{{ $t(\'发货单产品：序列号字段支持查找关联多个序列号\') }}</span>\n\t\t\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\t\t\t:value=\'openMultiSn\'\n\t\t\t\t\t\t\t\t\t@change=\'handleOpenMultiSnSwitch\'\n\t\t\t\t\t\t\t\t\t:disabled="openMultiSn===\'2\'"\n\t\t\t\t\t\t\t\t\tinactive-value=\'1\'\n\t\t\t\t\t\t\t\t\tactive-value=\'2\'\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-switch>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div style="margin-top: 10px;">\n\t\t\t\t\t\t\t<span>{{ $t(\'stock.crm.manage.enable_batch_and_sn_at_the_same_time\') }}</span>\n\t\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\t:value=\'enableBatchAndSnAtTheSameTime\'\n\t\t\t\t\t\t\t@change=\'handleEnableBatchAndSnAtTheSameTime\'\n\t\t\t\t\t\t\t:disabled="enableBatchAndSnAtTheSameTime===\'1\'"\n\t\t\t\t\t\t\tinactive-value=\'0\'\n\t\t\t\t\t\t\tactive-value=\'1\'\n\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-switch>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div style="margin-top: 10px;">\n\t\t\t\t\t\t\t<span>{{ $t(\'开启品级管理，针对序列号管理的产品，库存区分良品、不良品\') }}</span>\n\t\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\t:value=\'dht_serial_number_param_switch\'\n\t\t\t\t\t\t\t@change=\'handleOpenWareHouseSwitch\'\n\t\t\t\t\t\t\t:disabled="dht_serial_number_param_switch"\n\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-switch>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</fx-tab-pane>\n  \t\t\t\t</fx-tabs>\n\t\t\t\t</template>\n\t\t\t\t<span slot="footer" class="dialog-footer">\n\x3c!--    \t\t\t\t<fx-button --\x3e\n\x3c!--    \t\t\t\t\ttype="primary" --\x3e\n\x3c!--    \t\t\t\t\t@click="handleConfirm" --\x3e\n\x3c!--    \t\t\t\t\tsize="small"--\x3e\n\x3c!--    \t\t\t\t>确 定</fx-button>--\x3e\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click="handleClosed"\n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{ $t(\'取 消\') }}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t',props:{propWillExpireStockWarningType:String,has_open_multi_sn:Boolean,propOpenMultiSn:String,dht_serial_number_param_switch:Boolean},data:function(){return{loading:!1,visible:!0,activeName:"sn",willExpireStockWarningType:"1",openMultiSn:"1",enableBatchAndSnAtTheSameTime:"0"}},created:function(){this.willExpireStockWarningType=this.propWillExpireStockWarningType,this.init()},methods:{init:function(){var t=this,n=(this.loading=!0,this.getBatchSnConfig()),e=this.getEnableBatchAndSnAtTheSameTimeConfig(),r=this.has_open_multi_sn&&this.getMultiSnStatus();Promise.all([n,e,r]).catch(function(t){}).finally(function(){t.loading=!1})},getBatchSnConfig:function(){var n=this;return(0,i.getBatchSnConfig)().then(function(t){n.dht_serial_number_param_switch="1"==t.snParamSwitch})},getEnableBatchAndSnAtTheSameTimeConfig:function(){var n=this;return(0,i.commonGetConfigValueByKey)({key:"enable_batch_and_sn_at_the_same_time"}).then(function(t){n.enableBatchAndSnAtTheSameTime=t.value})},getMultiSnStatus:function(){var n=this;return(0,i.commonGetConfigValueByKey)({key:"open_multi_sn"}).then(function(t){n.openMultiSn=t.value})},handleWillExpireStockWarningTypeInput:_.throttle(function(t){var n=this;return this.loading=!0,(0,i.setBatchConfig)({willExpireStockWarningType:t}).then(function(){n.willExpireStockWarningType=t,n.$emit("updateWillExpireStockWarningType",t)}).finally(function(){n.loading=!1})},1e3),handleOpenMultiSnSwitch:_.throttle(function(n){var e=this;this.loading=!0,(0,i.asyncSetConfigValue)().circle({key:"open_multi_sn",value:n,token:""},function(t,n){1==t.resultCode&&n(t)}).res(function(t){e.loading=!1,e.openMultiSn=n,FS.crmUtil.remind(1,$t("操作成功"))}).catch(function(){e.loading=!1})},1e3),openConfirmMessageBox:function(){var n=this;return new Promise(function(t){n.$confirm($t("该功能开启后不可关闭，是否确认开启？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消")}).then(function(){t(1)}).catch(function(){t(0)})})},handleOpenWareHouseSwitch:function(){return r(this,void 0,void 0,_regeneratorRuntime().mark(function t(){var n=this;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.dht_serial_number_param_switch)return t.abrupt("return");t.next=2;break;case 2:return t.next=4,this.openConfirmMessageBox();case 4:if(t.sent){t.next=7;break}return t.abrupt("return");case 7:this.loading=!0,(0,i.enableSnParamConfig)().then(function(t){n.loading=!1,t.isSuccess?(n.dht_serial_number_param_switch=!0,FS.crmUtil.remind(1,$t("操作成功"))):FS.crmUtil.remind(1,$t("stock.crm.setting.shiporder.abnormal_operation"))});case 9:case"end":return t.stop()}},t,this)}))},handleEnableBatchAndSnAtTheSameTime:_.throttle(function(e){return r(this,void 0,void 0,_regeneratorRuntime().mark(function t(){var n=this;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.openConfirmMessageBox();case 2:if(t.sent){t.next=5;break}return t.abrupt("return");case 5:this.loading=!0,(0,i.asyncSetConfigValue)().circle({key:"enable_batch_and_sn_at_the_same_time",value:"1",token:""},function(t,n){1==t.resultCode&&n(t)}).res(function(t){n.loading=!1,n.enableBatchAndSnAtTheSameTime=e,FS.crmUtil.remind(1,$t("操作成功"))}).catch(function(){n.loading=!1});case 7:case"end":return t.stop()}},t,this)}))},1e3),handleClosed:function(){this.$destroy()}}});n.BatchSnDialog=t});
define("crm-setting/shiporder/shiporder/page/manage/components/CostManagement",["crm-modules/common/stock/api"],function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.CostManagementDialog=void 0;var a=t("crm-modules/common/stock/api"),t=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync='visible'\n\t\t\t  ref='dialog1'\n\t\t\t  :append-to-body='true'\n\t\t\t  :title=\"$t('stock.cost_management.setting.tip')\"\n\t\t\t  width='600px'\n\t\t\t  @closed='handleClosed'\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t<div v-loading ='loading'>\n    \t\t\t\t\t<div style='margin-bottom: 10px'>{{ $t('stock.cost_management.setting.title') }}</div>\n  \t\t\t\t\t  <fx-radio-group\n  \t\t\t\t\t    :value='cost_management_calculate'\n  \t\t\t\t\t    @input='handleCostManagementCalculate'\n  \t\t\t\t\t  >\n\t\t\t\t\t\t\t  <fx-radio style='margin-bottom: 10px' label='1'>{{ $t('stock.cost_management.setting.normal.title') }} <span style='color: gray'>{{$t('stock.cost_management.setting.normal')}}</span></fx-radio>\n  \t\t\t\t\t\t  <fx-radio style='margin-bottom: 10px' label='2'>{{ $t('stock.cost_management.setting.kx.title') }} <span style='color: gray'>{{$t('stock.cost_management.setting.kx')}}</span></fx-radio>\n  \t\t\t\t\t  </fx-radio-group>\n\t\t\t\t</div>\n\t\t\t\t</template>\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n\x3c!--    \t\t\t\t<fx-button --\x3e\n\x3c!--    \t\t\t\t\ttype=\"primary\" --\x3e\n\x3c!--    \t\t\t\t\t@click=\"handleConfirm\" --\x3e\n\x3c!--    \t\t\t\t\tsize=\"small\"--\x3e\n\x3c!--    \t\t\t\t>确 定</fx-button>--\x3e\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click='handleClosed'\n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{ $t('取 消') }}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",props:{prop_cost_management_calculate:String},data:function(){return{loading:!1,visible:!0,cost_management_calculate:"1"}},created:function(){this.cost_management_calculate=this.prop_cost_management_calculate},methods:{handleCostManagementCalculate:_.throttle(function(n){var e=this;this.loading=!0,(0,a.asyncSetConfigValue)().circle({key:"cost_management_calculate",value:n,token:""},function(t,n){1==t.resultCode&&n(t)}).res(function(t){e.loading=!1,e.cost_management_calculate=n,e.$emit("updatecost_management_calculate",n)}).catch(function(){e.loading=!1})},1e3),handleClosed:function(){this.$destroy()}}});n.CostManagementDialog=t});
define("crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuide",[],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.CustomFunctionGuide=void 0,e.CustomFunctionGuide=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t\tclass="app-custom-function-guide"\n  \t\t\t\t:visible="true"\n  \t\t\t\tfullscreen\n  \t\t\t\tappendToBody\n  \t\t\t\t:title="$t(\'发货单函数配置指引\')"\n  \t\t\t\t@close="$emit(\'close\')"\n  \t\t\t>\n\t\t\t \t<template >\n\t\t\t\t \t<h6>{{ $t(\'一、进入自定义函数管理页面\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[0]"\n\t\t\t\t\t    :img-index="0"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t \t<h6>{{ $t(\'二、点击右上角的“新建函数”，在弹出框中输入配置信息：\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[1]"\n\t\t\t\t\t    :img-index="1"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<p>{{ $t(\'函数名称、ApiName可自定义，命名空间选择“校验函数”，返回值类型选择“ValidateResult”，绑定对象选择“发货单”。\') }}</p>\n\t\t\t\t\t<p>{{ $t(\'进入下一步后，将以下文本复制后粘贴到函数的输入框内，点击保存，即可完成函数的创建。\') }}</p>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[2]"\n\t\t\t\t\t    :img-index="2"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<textarea\n\t\t\t\t\tref="copyfunction"\n\t\t\t\t\tstyle="position: absolute;opacity: 0;"\n\t\t\t\t\t/></textarea>\n\t\t\t\t \t<fx-button \n\t\t\t\t \t\tstyle="margin-left: calc(50% - 80px);margin-top: 40px"\n\t\t\t\t \t\t@click="handleCopy" \n\t\t\t\t \t\ttype="success" \n\t\t\t\t \t\tround>{{ $t(\'点我复制函数信息\') }}\n\t\t\t\t \t</fx-button>\n\t\t\t\t \t<h6>{{ $t(\'三、在流程的审批节点配置自定义函数作为前置条件\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[3]"\n\t\t\t\t\t    :img-index="3"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[4]"\n\t\t\t\t\t    :img-index="4"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t \tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[5]"\n\t\t\t\t\t    :img-index="5"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<p>{{ $t(\'前置条件选择“基于自定义函数”，然后选择刚刚配置的函数即可。\') }}</p>\n  \t\t\t\t</template>\n\t\t\t</fx-dialog>\n\t\t',data:function(){return{language:"zh-CN"}},computed:{srcList:function(){return["https://a9.fspage.com/FSR/uipaas/stock/guide1_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide2_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide3_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide4_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide5_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide6_".concat(this.language,".png")]}},methods:{handleCopy:function(){var t=this.$refs.copyfunction;t.value='Map requestParam = [ \n\t//"deliveryNoteDataId":"600a934cf08093000131f31c"\n\t// "deliveryNoteDataId":"600a962ff080930001321b74"\n\t"deliveryNoteDataId":context.data._id\n]; \n\nFx.log.info("requsetParam=" + requestParam) \n\ndef ret = Fx.proxy.callAPI("stock.checkDeliveryRealStock", null, requestParam) \nFx.log.info("ret=" + ret); \n\nHttpResult data = ret.data as HttpResult \n\nif(null == data) { \n  return ValidateResult.build{\n    success = false\n    errorMessage = "网络超时"\n  }\n} \n\nString json = data.content\nFx.log.info("data.content:" + json)\n\nMap resultData = Fx.json.parse(json)\nString realRetStr = resultData.result\nFx.log.info("realRetStr:" + realRetStr)\n\nMap realRetMap = Fx.json.parse(realRetStr)\nboolean passCheck = realRetMap.passCheck\n\nString retMessage = "库存校验通过";\nif (passCheck == false) {\n\tretMessage = "库存校验不通过:"\n\tString shortageProductStr = realRetMap.shortageProducts\n\tFx.log.info("shortageProducts:" + shortageProductStr)\n\tMap shortageProductMap = Fx.json.parse(shortageProductStr)\n\tshortageProductMap.each { key, value -> \n\t  String name = (String)key\n\t  BigDecimal substact = (BigDecimal)value\n\t  retMessage = retMessage + name + "还差" + substact + ";"\n\t}\n}\n\ndef result = ValidateResult.build{\n  success = passCheck\n  errorMessage = retMessage\n}\n\nreturn result',t.select(),document.execCommand("copy")?this.$message({showClose:!0,message:$t("拷贝成功。"),type:"success"}):this.$message({showClose:!0,message:$t("拷贝失败，请重试。"),type:"error"})}},created:function(){var t=FS.contacts.getCurrentEmployee().language;"zh-CN"!==t&&"en"!==t||(this.language=t)}})});
define("crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuideStock",[],function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.CustomFunctionGuideStock=void 0,n.CustomFunctionGuideStock=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t\tclass="app-custom-function-guide"\n  \t\t\t\t:visible="true"\n  \t\t\t\tfullscreen\n  \t\t\t\tappendToBody\n  \t\t\t\t:title="$t(\'校验库存函数配置指引\')"\n  \t\t\t\t@close="$emit(\'close\')"\n  \t\t\t>\n\t\t\t \t<template >\n\t\t\t\t \t<h6>{{ $t(\'一、进入自定义函数管理页面\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[0]"\n\t\t\t\t\t    :img-index="0"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t \t<h6>{{ $t(\'二、点击右上角的“新建函数”，在弹出框中输入配置信息：\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[1]"\n\t\t\t\t\t    :img-index="1"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<p>{{ $t(\'函数名称、ApiName可自定义，命名空间选择“校验函数”，返回值类型选择“ValidateResult”，绑定对象选择“发货单”。\') }}</p>\n\t\t\t\t\t<p>{{ $t(\'进入下一步后，将以下文本复制后粘贴到函数的输入框内，点击保存，即可完成函数的创建。\') }}</p>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[2]"\n\t\t\t\t\t    :img-index="2"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<textarea\n\t\t\t\t\tref="copyfunction"\n\t\t\t\t\tstyle="position: absolute;opacity: 0;"\n\t\t\t\t\t/></textarea>\n\t\t\t\t \t<fx-button \n\t\t\t\t \t\tstyle="margin-left: calc(50% - 80px);margin-top: 40px"\n\t\t\t\t \t\t@click="handleCopy" \n\t\t\t\t \t\ttype="success" \n\t\t\t\t \t\tround>{{ $t(\'点我复制函数信息\') }}\n\t\t\t\t \t</fx-button>\n\t\t\t\t \t<h6>{{ $t(\'三、在流程的审批节点配置自定义函数作为前置条件\') }}</h6>\n\t\t\t\t \t<fx-image\n\t\t\t\t \t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[3]"\n\t\t\t\t\t    :img-index="3"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t\tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[4]"\n\t\t\t\t\t    :img-index="4"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<fx-image\n\t\t\t\t\t \tclass="app-custom-function-guide__pic"\n    \t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t    :src="srcList[5]"\n\t\t\t\t\t    :img-index="5"\n\t\t\t\t\t    :preview-src-list="srcList"\n\t\t\t\t\t    show-download\n\t\t\t\t\t>\n\t\t\t\t\t</fx-image>\n\t\t\t\t\t<p>{{ $t(\'前置条件选择“基于自定义函数”，然后选择刚刚配置的函数即可。\') }}</p>\n  \t\t\t\t</template>\n\t\t\t</fx-dialog>\n\t\t',data:function(){return{language:"zh-CN"}},computed:{srcList:function(){return["https://a9.fspage.com/FSR/uipaas/stock/guide1_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/check_stock1_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/check_stock2_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/check_stock3_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/guide5_".concat(this.language,".png"),"https://a9.fspage.com/FSR/uipaas/stock/check_stock4_".concat(this.language,".png")]}},methods:{handleCopy:function(){var t=this.$refs.copyfunction;t.value='\n\t\t\tMap requestParam = [\n                "dataId":context.data._id,\n                "apiName":context.data.object_describe_api_name\n            ];\n            \n            Fx.log.info("requsetParam=" + requestParam)\n            def ret = Fx.proxy.callAPI("stock.flowCompletedCheckStock", null, requestParam)\n            Fx.log.info("ret=" + ret)\n            \n            HttpResult data = ret.data as HttpResult\n            if(null == data) {\n                return "网络超时，请稍后重试"\n            }\n            def Map result = data.content as Map\n            Fx.log.info("result=" + result)\n            String msg = "校验成功";\n            Boolean isSuccess = true;\n            \n            if(result == null || !result[\'result\'][\'isSuccess\']) {\n                msg = result[\'result\'][\'message\']\n                isSuccess = false\n            }\n            def validateResult = ValidateResult.build{\n                success = isSuccess\n                errorMessage = msg\n            }\n            \n            return validateResult',t.select(),document.execCommand("copy")?this.$message({showClose:!0,message:$t("拷贝成功。"),type:"success"}):this.$message({showClose:!0,message:$t("拷贝失败，请重试。"),type:"error"})}},created:function(){var t=FS.contacts.getCurrentEmployee().language;"zh-CN"!==t&&"en"!==t||(this.language=t)}})});
define("crm-setting/shiporder/shiporder/page/manage/components/DeliveryNoteReturnSetting",["crm-modules/common/stock/api"],function(i,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.DeliveryNoteReturnSetting=void 0;var n=i("crm-modules/common/stock/api");e.DeliveryNoteReturnSetting=Vue.extend({template:'\n        <fx-dialog class="exchange-setting-dialog" :title="$t(\'stock.shiporder.delivery_plugin_setting.title\')"\n            size="small"\n            :visible.sync="visible"\n            :append-to-body="true"\n        >\n            <div class="main-content" v-loading="isLoading">\n                <div class="text">{{ $t(\'stock.shiporder.delivery_plugin_setting.text\') }}</div>\n                <fx-radio-group v-model="radio" is-vertical>\n                    <fx-radio :label="1">{{ $t(\'stock.shiporder.delivery_plugin_setting.radio1\') }}</fx-radio>\n                    <fx-radio :label="2" :disabled="isATOPTOOpen">\n                        <span>{{ $t(\'stock.shiporder.delivery_plugin_setting.radio2\') }}</span>\n                        <div v-if="isATOPTOOpen" style="color: #C1C5CE; font-size: 12px; margin: 4px 0 0 23px;">{{ $t(\'stock.shiporder.delivery_plugin_setting.exclusive_with_ato\') }}</div>\n                    </fx-radio>\n                </fx-radio-group>\n            </div>\n            <div slot="footer" class="dialog-footer">\n                <fx-button type="primary" @click="onConfirm" size="small">{{ $t(\'确定\') }}</fx-button>\n                <fx-button @click="visible = false" size="small">{{ $t(\'取消\') }}</fx-button>\n            </div>\n        </fx-dialog>\n    ',data:function(){return{visible:!0,isLoading:!0,radio:1,oldRadio:1}},props:{isATOPTOOpen:Boolean},created:function(){var e=this;(0,n.getSparePartConfig)({key:"delivery_return_product_add_mode"}).then(function(i){e.radio=i&&Number(i.value),e.oldRadio=e.radio}).catch(function(i){}).finally(function(){e.isLoading=!1})},methods:{onConfirm:function(){var i=this;this.isLoading=!0,this.radio===this.oldRadio?this.visible=!1:(0,n.setDeliveryReturnSettingParam)(this.radio).then(function(i){CRM.util.remind(1,$t("操作成功"))}).catch(function(i){CRM.util.remind(3,$t("操作失败"))}).finally(function(){i.isLoading=!1,i.visible=!1})}}})});
define("crm-setting/shiporder/shiporder/page/manage/components/ExchangeSettingDialog",["crm-modules/common/stock/api"],function(i,n,t){Object.defineProperty(n,"__esModule",{value:!0}),n.ExchangeSettingDialog=void 0;var e=i("crm-modules/common/stock/api");n.ExchangeSettingDialog=Vue.extend({template:'\n        <fx-dialog class="exchange-setting-dialog" :title="$t(\'stock.shiporder.exchange_plugin_setting.title\')"\n            size="small"\n            :visible.sync="visible"\n            :append-to-body="true"\n        >\n            <div class="main-content" v-loading="isLoading">\n                <div class="text">{{ $t(\'stock.shiporder.exchange_plugin_setting.text\') }}</div>\n                <fx-radio-group v-model="radio" is-vertical>\n                    <fx-radio :label="1">{{ $t(\'stock.shiporder.exchange_plugin_setting.radio1\') }}</fx-radio>\n                    <fx-radio :label="2">{{ $t(\'stock.shiporder.exchange_plugin_setting.radio2\') }}</fx-radio>\n                </fx-radio-group>\n            </div>\n            <div slot="footer" class="dialog-footer">\n                <fx-button type="primary" @click="onConfirm" size="small">{{ $t(\'确定\') }}</fx-button>\n                <fx-button @click="visible = false" size="small">{{ $t(\'取消\') }}</fx-button>\n            </div>\n        </fx-dialog>\n    ',data:function(){return{visible:!0,isLoading:!0,radio:1}},created:function(){var n=this;(0,e.getSparePartConfig)({key:"returned_goods_auto_update_stock"}).then(function(i){n.radio=i&&Number(i.value)}).catch(function(i){}).finally(function(){n.isLoading=!1})},methods:{onConfirm:function(){var i=this;this.isLoading=!0,(0,e.setExchangeSettingParam)(this.radio).then(function(i){CRM.util.remind(1,$t("操作成功"))}).catch(function(i){CRM.util.remind(3,$t("操作失败"))}).finally(function(){i.isLoading=!1,i.visible=!1})}}})});
define("crm-setting/shiporder/shiporder/page/manage/components/FreezeInventoryAdjustment",["crm-modules/common/stock/api"],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.FreezeInventoryAdjustment=void 0;var i=t("crm-modules/common/stock/api");e.FreezeInventoryAdjustment=Vue.extend({template:'\n        <fx-dialog class="freeze-inventory-adjustment-dialog" :title="$t(\'stock.shiporder.freeze_inventory_adjustment.plugin_title\')"\n            :visible.sync="visible"\n            :append-to-body="true"\n            @close="handleDialogClose"\n        >\n            <div class="main-content" v-loading="isLoading">\n                <div class="notice-text">{{ $t(\'stock.shiporder.freeze_inventory_adjustment.notice_text\') }}</div>\n                <div class="text-container">\n                    <div class="text-item">{{ \'1.\' + $t(\'stock.shiporder.freeze_inventory_adjustment.plugin_text1\') }}</div>\n                    <div class="text-item" v-if="isStockBAndDistributionOpen">{{ \'2.\' + $t(\'stock.shiporder.freeze_inventory_adjustment.plugin_text2\') }}</div>\n                    <div class="text-item">{{ (isStockBAndDistributionOpen ? \'3.\' : \'2.\') + $t(\'stock.shiporder.freeze_inventory_adjustment.plugin_text3\') }}</div>\n                    <div class="text-confirm">{{ $t(\'stock.shiporder.freeze_inventory_adjustment.confirm_text\') }}</div>\n                </div>\n            </div>\n            <div slot="footer" class="dialog-footer">\n                <fx-button type="primary" @click="onConfirm" size="small">{{ $t(\'确定\') }}</fx-button>\n                <fx-button @click="visible = false" size="small">{{ $t(\'取消\') }}</fx-button>\n            </div>\n        </fx-dialog>\n    ',data:function(){return{visible:!0,isLoading:!1,isConfirmSuccessful:!1}},props:{isStockBAndDistributionOpen:Boolean},methods:{handleDialogClose:function(){this.$emit("close",this.isConfirmSuccessful)},onConfirm:function(){var e=this,t=(this.isLoading=!0,{status:"1",update_time:Date.now().toString()});(0,i.openModifyFreezeInventorySwitch)(JSON.stringify(t)).then(function(t){CRM.util.remind(1,$t("操作成功")),e.isConfirmSuccessful=!0}).catch(function(t){CRM.util.remind(3,$t("操作失败"))}).finally(function(){e.isLoading=!1,e.visible=!1})}}})});
define("crm-setting/shiporder/shiporder/page/manage/components/Interconnect/change-enterprise-dialog",[],function(t,e,n){function a(){return{currentIdentity:"up",correspondingUpstream:"",correspondingUpstreamList:[]}}Object.defineProperty(e,"__esModule",{value:!0}),e.ChangeEnterpriseDialog=void 0;var i=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync='visible'\n\t\t\t  ref='dialog1'\n\t\t\t  :append-to-body='true'\n\t\t\t  title='采购业务联动-参数设置'\n\t\t\t  width='500px'\n\t\t\t  @closed='handleClosed'\n\t\t\t  class='change-enterprise-dialog-dialog'\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<fx-radio-group v-model='value.currentIdentity' \n\t\t\t\t\t\t\t\t\t\t\t\t\tclass='change-enterprise-dialog-dialog__major'\n\t\t\t\t\t\t\t\t\t\t\t\t\t@change='handleCurrentIdentityChange'\n\t\t\t\t\t>\n    \t\t\t\t<fx-radio label='up'>\n    \t\t\t\t\t<span>我是上游</span>\n\t\t\t\t\t\t</fx-radio>\n    \t\t\t\t<fx-radio label='down'>\n    \t\t\t\t\t\t<span>我是下游，请选择对应的上游</span>\n\t\t\t\t\t\t</fx-radio>\n  \t\t\t\t</fx-radio-group>\n\t\t\t\t\t<fx-radio-group v-model='value.correspondingUpstream'\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass='change-enterprise-dialog-dialog__sub'\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-show='value.currentIdentity===\"down\"'\n\t\t\t\t\t>\n    \t\t\t\t<fx-radio  v-for='item in value.correspondingUpstreamList' :label='item.upstreamTenantId'>\n    \t\t\t\t\t<span>{{item.upstreamName}}</span>\n\t\t\t\t\t\t</fx-radio>\n  \t\t\t\t</fx-radio-group>\n\t\t\t\t</template> \n\t\t\t\t\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n    \t\t\t\t<fx-button \n    \t\t\t\t\ttype='primary' \n    \t\t\t\t\t@click='handleConfirm' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('确 定')}}</fx-button>\n    \t\t\t\t<fx-button \n    \t\t\t\t\t@click='handleClosed' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('取 消')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",props:{defaultValue:{type:Object,default:a}},data:function(){return{visible:!0,value:a()}},created:function(){this.value=this.defaultValue},methods:{validate:function(){return"none"!==this.value.currentIdentity&&!("down"===this.value.currentIdentity&&!this.value.correspondingUpstream)},handleCurrentIdentityChange:function(t){"down"!==t||this.value.correspondingUpstream||(this.value.correspondingUpstream=this.value.correspondingUpstreamList[0].upstreamTenantId)},getValue:function(){return this.value},handleConfirm:function(){if(!this.validate())return this.$message({message:"请选择对应的上游",type:"warning"});this.$emit("confirm",this.getValue()),this.handleClosed()},handleClosed:function(){this.$emit("cancel",this.getValue()),this.$destroy()}}});e.ChangeEnterpriseDialog=i});
define("crm-setting/shiporder/shiporder/page/manage/components/Interconnect/purchase-received-warehouse-dialog",[],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var o=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync="visible"\n\t\t\t  ref="dialog1"\n\t\t\t  :append-to-body="true"\n\t\t\t  title="采购业务联动-参数设置"\n\t\t\t  width="500px"\n\t\t\t  @closed="handleClosed"\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<p style=\'margin-bottom: 10px\'>{{$t(\'stock.stock_manage.received_generate_warehouse\')}}</p>\n\t\t\t\t\t<fx-select\n    \t\t\t\tref="el1"\n    \t\t\t\tv-model="value"\n    \t\t\t\t:options="options"\n  \t\t\t\t></fx-select>\n\t\t\t\t</template>\n\n\t\t\t\t<span slot="footer" class="dialog-footer">\n    \t\t\t\t<fx-button\n    \t\t\t\t\ttype="primary"\n    \t\t\t\t\t@click="handleConfirm"\n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{$t(\'确 定\')}}</fx-button>\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click="handleClosed"\n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{$t(\'取 消\')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t',props:{defaultValue:String,options:{type:Array,default:function(){return[{value:"",label:""}]}}},data:function(){return{visible:!0,value:""}},created:function(){this.value=this.defaultValue},methods:{getValue:function(){return this.value},handleConfirm:function(){var e=this,t=this.options.find(function(t){return t.value===e.value});this.$emit("confirm",t),this.handleClosed()},handleClosed:function(){this.$destroy()}}});e.default=o});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return i};var c,i={},t=Object.prototype,u=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function h(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{h({},"")}catch(c){h=function(t,e,n){return t[e]=n}}function s(t,e,n,r){var o,a,i,s,e=e&&e.prototype instanceof l?e:l,e=Object.create(e.prototype);return h(e,"_invoke",(o=t,a=n,i=new x(r||[]),s=1,function(t,e){if(3===s)throw Error("Generator is already running");if(4===s){if("throw"===t)throw e;return{value:c,done:!0}}for(i.method=t,i.arg=e;;){var n=i.delegate;if(n){n=function t(e,n){var r=n.method,o=e.i[r];if(o===c)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=c,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;r=f(o,e.i,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,p;o=r.arg;return o?o.done?(n[e.r]=o.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=c),n.delegate=null,p):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,p)}(n,i);if(n){if(n===p)continue;return n}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if(1===s)throw s=4,i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);s=3;n=f(o,a,i);if("normal"===n.type){if(s=i.done?4:2,n.arg===p)continue;return{value:n.arg,done:i.done}}"throw"===n.type&&(s=4,i.method="throw",i.arg=n.arg)}}),!0),e}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}i.wrap=s;var p={};function l(){}function a(){}function d(){}var e={},m=(h(e,r,function(){return this}),Object.getPrototypeOf),m=m&&m(m(D([]))),g=(m&&m!==t&&u.call(m,r)&&(e=m),d.prototype=l.prototype=Object.create(e));function v(t){["next","throw","return"].forEach(function(e){h(t,e,function(t){return this._invoke(e,t)})})}function y(i,s){var e;h(this,"_invoke",function(n,r){function t(){return new s(function(t,e){!function e(t,n,r,o){var a,t=f(i[t],i,n);if("throw"!==t.type)return(n=(a=t.arg).value)&&"object"==_typeof(n)&&u.call(n,"__await")?s.resolve(n.__await).then(function(t){e("next",t,r,o)},function(t){e("throw",t,r,o)}):s.resolve(n).then(function(t){a.value=t,r(a)},function(t){return e("throw",t,r,o)});o(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()},!0)}function w(t){this.tryEntries.push(t)}function b(t){var e=t[4]||{};e.type="normal",e.arg=c,t[4]=e}function x(t){this.tryEntries=[[-1]],t.forEach(w,this),this.reset(!0)}function D(e){if(null!=e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(u.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=c,t.done=!0,t}).next=t}throw new TypeError(_typeof(e)+" is not iterable")}return h(g,"constructor",a.prototype=d),h(d,"constructor",a),a.displayName=h(d,o,"GeneratorFunction"),i.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===a||"GeneratorFunction"===(t.displayName||t.name))},i.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,h(t,o,"GeneratorFunction")),t.prototype=Object.create(g),t},i.awrap=function(t){return{__await:t}},v(y.prototype),h(y.prototype,n,function(){return this}),i.AsyncIterator=y,i.async=function(t,e,n,r,o){void 0===o&&(o=Promise);var a=new y(s(t,e,n,r),o);return i.isGeneratorFunction(e)?a:a.next().then(function(t){return t.done?t.value:a.next()})},v(g),h(g,o,"Generator"),h(g,r,function(){return this}),h(g,"toString",function(){return"[object Generator]"}),i.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.unshift(e);return function t(){for(;r.length;)if((e=r.pop())in n)return t.value=e,t.done=!1,t;return t.done=!0,t}},i.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function t(t){a.type="throw",a.arg=e,n.next=t}for(var r=n.tryEntries.length-1;0<=r;--r){var o=this.tryEntries[r],a=o[4],i=this.prev,s=o[1],u=o[2];if(-1===o[0])return t("end"),!1;if(!s&&!u)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=i){if(i<s)return this.method="next",this.arg=c,t(s),!0;if(i<u)return t(u),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(-1<r[0]&&r[0]<=this.prev&&this.prev<r[2]){var o=r;break}}var a=(o=o&&("break"===t||"continue"===t)&&o[0]<=e&&e<=o[2]?null:o)?o[4]:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o[2],p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),b(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,o=this.tryEntries[e];if(o[0]===t)return"throw"===(n=o[4]).type&&(r=n.arg,b(o)),r}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={i:D(t),r:e,n:n},"next"===this.method&&(this.arg=c),p}},i}define("crm-setting/shiporder/shiporder/page/manage/components/InterconnectionSettingDialog",["crm-modules/common/stock/api","crm-modules/common/stock/mixins/messageMixins"],function(t,e,n){var o=this&&this.__awaiter||function(t,i,s,u){return new(s=s||Promise)(function(n,e){function r(t){try{a(u.next(t))}catch(t){e(t)}}function o(t){try{a(u.throw(t))}catch(t){e(t)}}function a(t){var e;t.done?n(t.value):((e=t.value)instanceof s?e:new s(function(t){t(e)})).then(r,o)}a((u=u.apply(t,i||[])).next())})},a=(Object.defineProperty(e,"__esModule",{value:!0}),e.InterconnectionSettingDialog=void 0,t("crm-modules/common/stock/api")),t=t("crm-modules/common/stock/mixins/messageMixins"),t=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t\tclass='interconnection-setting-dialog'\n\t\t\t  :visible.sync='visible'\n\t\t\t  ref='dialog1'\n\t\t\t  :append-to-body='true'\n\t\t\t  :title='$t(\"采购业务联动-参数设置\")'\n\t\t\t  width='500px'\n\t\t\t  @closed='handleClosed'\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<fx-tabs v-model='activeName'>\n    \t\t\t\t<fx-tab-pane \n    \t\t\t\t\tv-if='identityInfos.isUp'\n    \t\t\t\t\t:label='$t(\"我是上游\")' \n    \t\t\t\t\tname='up'\n    \t\t\t\t>\n    \t\t\t\t\t<div class='interconnection-setting-dialog-up'>\n    \t\t\t\t\t\t<div>{{ $t(\"当销售订单确认后\") }}</div>\n    \t\t\t\t\t\t<fx-radio-group v-model='upData.autoGenerateDeliveryConfig'>\n    \t\t\t\t\t\t\t<fx-radio label='1'>\n    \t\t\t\t\t\t\t\t<span>{{ $t(\"在满足相关条件的前提下，系统自动创建发货单\") }}</span>\n\t\t\t\t\t\t\t\t\t</fx-radio>\n    \t\t\t\t\t\t\t<fx-radio label='0'>\n    \t\t\t\t\t\t\t\t<span>{{ $t(\"系统不自动创建发货单，需要手动创建\") }}</span>\n\t\t\t\t\t\t\t\t\t</fx-radio>\n  \t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</fx-tab-pane>\n    \t\t\t\t<fx-tab-pane \n    \t\t\t\t\tv-if='identityInfos.isDown'\n    \t\t\t\t\t:label=\"$t('我是下游')\"\n    \t\t\t\t\tname='down'\n    \t\t\t\t>\n    \t\t\t\t\t<div class='interconnection-setting-dialog-down'>\n    \t\t\t\t\t\t<div class='interconnection-setting-dialog-down--item'>\n    \t\t\t\t\t\t\t<span>{{ $t(\"对应上游\") }}</span>\n\t\t\t\t\t\t\t\t\t<fx-select\n    \t\t\t\t\t\t\t\tv-model=\"downData.selectedCorrespondingUpstream\"\n   \t\t\t\t\t\t\t\t\t:options=\"downData.correspondingUpstreamList\"\n    \t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t\t</div>\n    \t\t\t\t\t\t<div class='interconnection-setting-dialog-down--item'>\n    \t\t\t\t\t\t\t<span>{{ $t(\"采购入库默认仓库\") }}</span>\n    \t\t\t\t\t\t\t<fx-select\n    \t\t\t\t\t\t\t\tv-model=\"downData.selectedWarehouse\"\n   \t\t\t\t\t\t\t\t\t:options=\"downData.warehouseList\"\n    \t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</fx-tab-pane>\n  \t\t\t\t</fx-tabs>\n\t\t\t\t</template> \n\t\t\t\t\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n    \t\t\t\t<fx-button \n    \t\t\t\t\ttype='primary' \n    \t\t\t\t\t@click='handleSave' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('保存')}}</fx-button>\n    \t\t\t\t<fx-button \n    \t\t\t\t\t@click='handleClosed' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('取 消')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",mixins:[t.messageMixins],props:{identityInfos:{type:Object,validator:function(t){return t&&t.hasOwnProperty("isUp")&&"boolean"==typeof t.isUp&&t.hasOwnProperty("isDown")&&"boolean"==typeof t.isDown}},upStreamParams:{type:Object,default:function(){return{autoGenerateDeliveryConfig:1}}},downStreamParams:{type:Object,default:function(){return{correspondingUpstreamList:[],selectedCorrespondingUpstream:""}}}},watch:{"downData.selectedCorrespondingUpstream":function(r){return o(this,void 0,void 0,_regeneratorRuntime().mark(function t(){var e,n;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,this.getPurchaseReceivedWarehouse();case 5:n=t.sent,e=n.value,n=n.wareHouse,this.downData.warehouseList=n,this.downData.selectedWarehouse=e,t.next=17;break;case 12:t.prev=12,t.t0=t.catch(2),this.downData.warehouseList=[],this.downData.selectedWarehouse="";case 17:case"end":return t.stop()}},t,this,[[2,12]])}))}},data:function(){return{visible:!0,activeName:"up",upData:{autoGenerateDeliveryConfig:"1"},downData:{correspondingUpstreamList:[],selectedCorrespondingUpstream:"",warehouseList:[],selectedWarehouse:""}}},created:function(){this.propsToLocalData(),this.activeName=this.identityInfos.isUp?"up":"down"},methods:{propsToLocalData:function(){this.downParamsPropToLocalData(),this.upParamsToLocalData()},downParamsPropToLocalData:function(){var t=JSON.parse(JSON.stringify(this.downStreamParams));this.downData.correspondingUpstreamList=t.correspondingUpstreamList,this.downData.selectedCorrespondingUpstream=t.selectedCorrespondingUpstream},upParamsToLocalData:function(){var t=JSON.parse(JSON.stringify(this.upStreamParams));this.upData.autoGenerateDeliveryConfig=t.autoGenerateDeliveryConfig},getPurchaseReceivedWarehouse:function(){return o(this,void 0,void 0,_regeneratorRuntime().mark(function t(){var e,n,r;return _regeneratorRuntime().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,r=this.downData.selectedCorrespondingUpstream,t.next=5,(0,a.getPurchaseReceivedWarehouseForDownstream)({upstreamTenantId:r});case 5:return r=t.sent,e=r.warehouseInfoList.find(function(t){return t.predefine}).purchaseReceivedWarehouseId,n=r.warehouseInfoList.map(function(t){return{value:t.purchaseReceivedWarehouseId,label:t.purchaseReceivedWarehouseName}}),t.abrupt("return",Promise.resolve({value:e,wareHouse:n}));case 11:return t.prev=11,t.t0=t.catch(0),t.abrupt("return",Promise.reject(t.t0));case 14:case"end":return t.stop()}},t,this,[[0,11]])}))},setPurchaseUpstreamParam:function(t){return(0,a.setAutoGenerateUpstreamDeliveryNoteConfig)({autoGenerateConfig:t})},setPurchaseReceivedWarehouse:function(t,e){return(0,a.setPurchaseReceivedWarehouse)({upstreamTenantId:e,purchaseReceivedWarehouseId:t})},handleSave:_.throttle(function(){var e=this,t=[];this.identityInfos.isUp&&t.push(this.setPurchaseUpstreamParam(this.upData.autoGenerateDeliveryConfig).then(function(){var t={autoGenerateDeliveryConfig:e.upData.autoGenerateDeliveryConfig};e.$emit("updateUpStreamParams",t)})),this.identityInfos.isDown&&t.push(this.setPurchaseReceivedWarehouse(this.downData.selectedWarehouse,this.downData.selectedCorrespondingUpstream)),Promise.all(t).then(this.success).catch(this.error)},1e3),handleClosed:function(){this.$emit("cancel"),this.$destroy()}}});e.InterconnectionSettingDialog=t});
define("crm-setting/shiporder/shiporder/page/manage/components/K3CDialog",["crm-modules/common/stock/api"],function(t,n,i){Object.defineProperty(n,"__esModule",{value:!0}),n.K3CDialog=void 0;var a=t("crm-modules/common/stock/api"),t=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync="visible"\n\t\t\t  ref="dialog1"\n\t\t\t  :append-to-body="true"\n\t\t\t  :title="$t(\'金蝶K3C对接插件\')"\n\t\t\t  width="475px"\n\t\t\t  @closed="handleClosed"\n\t\t\t  class="k3c-plugin-dialog"\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<div v-loading="loading">\n\t\t\t\t\t\t<div class="sub-title">{{ $t("同步机制") }}</div>\n\t\t\t\t\t\t<div>\n  \t\t\t\t\t\t<fx-radio-group :value="syncType" is-vertical @input=\'changeSynchronizationConfirm\'>\n\t\t\t\t\t\t\t<fx-radio label="1">{{ $t("同步库存") }}</fx-radio>\n\t\t\t\t\t\t\t<div class="radio-desc">{{ $t("直接同步库存数据到库存、批次库存等对象") }}</div>\n  \t\t\t\t\t\t\t<fx-radio label="2">{{ $t("同步库存明细") }}</fx-radio>\n\t\t\t\t\t\t\t<div class="radio-desc">{{ $t("同步数据到库存明细对象后，系统将自动进行计算并更新库存、批次库存等对象的数据") }}</div>\n  \t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="sub-title available-quantit-title">{{ $t("实时可用量") }}</div>\n\t\t\t\t\t\t<div class="available-quantit-wrap">\n\t\t\t\t\t\t\t<span class="available-quantit-text">{{ $t("通过接口，获取金蝶K3C库存及批次库存的实时可用量") }}</span>\n\t\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\t\t:value=\'instantAvailableQuantityOpen\'\n\t\t\t\t\t\t\t\t@change=\'handleSetK3CInstantAvailableQuantity\'\n\t\t\t\t\t\t\t\tinactive-value=\'1\'\n\t\t\t\t\t\t\t\tactive-value=\'2\'\n\t\t\t\t\t\t\tsize="micro"\n\t\t\t\t\t\t\t></fx-switch>\n\t\t\t\t\t\t</div>\n\n\t\t\t\t\t</div>\n\t\t\t\t</template>\n\t\t\t\t<span slot="footer" class="dialog-footer">\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click="handleClosed"\n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{ $t("完成") }}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t',props:{propSyncType:String},data:function(){return{loading:!1,visible:!0,syncType:"1",instantAvailableQuantityOpen:"1"}},created:function(){this.syncType=this.propSyncType,this.init()},methods:{init:function(){this.getInstantAvailableQuantityOpen()},getInstantAvailableQuantityOpen:function(){var n=this;return this.loading=!0,(0,a.commonGetConfigValueByKey)({key:"stock_instant_available_quantity_switch"}).then(function(t){n.instantAvailableQuantityOpen=t.value}).finally(function(){n.loading=!1})},handleSetK3CInstantAvailableQuantity:function(n){var i=this;this.loading=!0,(0,a.asyncSetConfigValue)().circle({key:"stock_instant_available_quantity_switch",value:n,token:""},function(t,n){1==t.resultCode&&n(t)}).res(function(t){i.loading=!1,i.instantAvailableQuantityOpen=n}).catch(function(t){i.loading=!1,FS.crmUtil.remind(3,t)})},changeSynchronizationConfirm:function(t){var n=this,i=$t("切换同步机制，将会将现有库存和批次库存的数量全部置为0，确定要继续吗？"),a=FS.crmUtil.confirm(i,$t("切换提示"),function(){a.hide(),n.handleSynchronizationMechanismInput(t)})},handleSynchronizationMechanismInput:_.throttle(function(t){var n=this;return this.loading=!0,(0,a.settingK3C)({kingDeeK3CSyncType:t}).then(function(){n.syncType=t,n.$emit("updateKingDeeK3CSyncType",t)}).finally(function(){n.loading=!1})},1e3),handleClosed:function(){this.$destroy()}}});n.K3CDialog=t});
define("crm-setting/shiporder/shiporder/page/manage/components/multiOrderDeliveryDialog",[],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.cpqDeliveryClose=e.multiOrderDeliveryClose=e.multiOrderDeliveryOpen=void 0;var i=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t\t:visible.sync='visible'\n\t\t\t  :append-to-body='true'\n\t\t\t  :title=\"$t('开启多订单发货')\"\n\t\t\t  width='640px'\n\t\t\t  @closed='handleClosed'\n\t\t\t\tclass='multiOrderDeliveryDialog'\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<p>{{ $t('开启多订单发货，会对现有的部分逻辑做出一些调整，请确保您已知晓以下可能产生的影响：') }}</p>\n\t\t\t\t\t<p>{{ $t('1、开启多订单发货后，新建/编辑时，销售订单字段会在发货单主对象上隐藏;') }}</p>\n\t\t\t\t\t<p>{{ $t('2、如果一张发货单对多个订单进行了发货，发货单主对象上的销售订单字段将不会被赋值，可能会影响原有的报表分析;') }}</p>\n\t\t\t\t\t<p>{{ $t('3、开启后，如果想要关闭多订单发货，则需要先将对应了多张销售订单的发货单删除之后，才能够关闭。') }}</p>\n\t\t\t\t\t<p>\n\t\t\t\t\t<input type=\"checkbox\" name=\"checkbox\" v-model:value=\"isAgree\" :disabled=\"isDisabled\"/>\n\t\t\t\t\t<span v-if='time'>{{time}} s </span>{{ $t('我已知晓开启多订单发货后的调整，并确定这些调整不会影响订单业务的正常流转。') }}\n\t\t\t\t\t</p>\n\t\t\t\t</template>\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n    \t\t\t\t<fx-button\n    \t\t\t\t\ttype='primary'\n\t\t\t\t\t\t\t:disabled=\"isDisabled ||!isAgree\"\n    \t\t\t\t\t@click='handleConfirm'\n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('确 定')}}</fx-button>\n    \t\t\t\t<fx-button\n    \t\t\t\t\t@click='handleClosed'\n    \t\t\t\t\tsize='small'\n    \t\t\t\t>{{$t('取 消')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",data:function(){return{isAgree:!1,visible:!0,time:29,isDisabled:!0}},created:function(){var t=this,e=setInterval(function(){t.time--,t.time<=0&&(clearInterval(e),t.isDisabled=!1)},1e3)},methods:{handleConfirm:function(){this.isDisabled||(this.$emit("confirm"),this.$destroy())},handleClosed:function(){this.$destroy()}}}),i=(e.multiOrderDeliveryOpen=i,Vue.extend({template:"\n\t<fx-dialog\n\t:visible.sync='visible'\n\t:append-to-body='true'\n\t:title=\"$t('关闭多订单发货')\"\n\twidth='630px'\n\t@closed='handleClosed'\n\tclass='multiOrderDeliveryDialog'\n>\n\t<template >\n    <p>{{ $t('关闭多订单发货之前，请确保您已知晓以下前提条件：') }}</p>\n    <p>{{ $t('如果系统中存在关联多个销售订单的发货单，则必须先将这些发货单作废删除后，才能够关闭多订单发货功能。') }}</p>\n    <p class=\"inquire-list-btn\"><a :href=href target=\"_blank\">{{ $t('点击查询关联多个销售订单的发货单列表') }}</a></p>\n    <p>\n    <input type=\"checkbox\" name=\"checkbox\" v-model:value=\"isAgree\" :disabled=\"isDisabled\"/>\n    <span>{{time}} s </span>{{ $t('我已知晓开启多订单发货后的调整，并确定这些调整不会影响订单业务的正常流转。') }}\n    </p>\n\t</template>\n\t<span slot='footer' class='dialog-footer'>\n\t\t\t<fx-button\n\t\t\t\ttype='primary'\n\t\t\t\t:disabled=\"isDisabled ||!isAgree\"\n\t\t\t\t@click='handleConfirm'\n\t\t\t\tsize='small'\n\t\t\t>{{$t('确 定')}}</fx-button>\n\t\t\t<fx-button\n\t\t\t\t@click='handleClosed'\n\t\t\t\tsize='small'\n\t\t\t>{{$t('取 消')}}</fx-button>\n\t\t</span>\n</fx-dialog>\n ",computed:{href:function(){return window.location.pathname+"#crm/list/=/DeliveryNoteObj/filteremptysalesorderid"}},data:function(){return{isAgree:!1,visible:!0,time:29,isDisabled:!0,dialogVisible2:!1}},created:function(){var t=this,e=setInterval(function(){t.time--,t.time<=0&&(clearInterval(e),t.isDisabled=!1)},1e3)},methods:{handleConfirm:function(){this.isDisabled||(this.$emit("confirm"),this.$destroy())},handleClosed:function(){this.$destroy()}}})),i=(e.multiOrderDeliveryClose=i,Vue.extend({template:"\n\t<fx-dialog\n\t:visible.sync='visible'\n\t:append-to-body='true'\n\t:title=\"$t('stock.stock_manage.close_cpq_delivery')\"\n\twidth='630px'\n\t@closed='handleClosed'\n\tclass='multiOrderDeliveryDialog'\n>\n\t<template >\n\t\t<p>{{ $t('CPQ组合发货插件属于已下架、且不再维护的插件，推荐关闭。') }}</p>\n\t\t<p>{{ $t('该插件关闭后，不可再次开启，后续插件管理页面也无法再次查询到该插件的信息。') }}</p>\n\t\t<p>{{ $t('确定要关闭CPQ组合发货插件吗？') }}</p>\n\t\t<p>\n\t\t<input type=\"checkbox\" name=\"checkbox\" v-model:value=\"isAgree\" :disabled=\"isDisabled\"/>\n\t\t<span v-if='time'>{{time}} s </span>{{ $t('我已知晓CPQ组合发货插件关闭后无法再次开启，并确定关闭该插件不会影响现有的业务。') }}\n\t\t</p>\n\t</template>\n\t<span slot='footer' class='dialog-footer'>\n\t\t\t<fx-button\n\t\t\t\ttype='primary'\n\t\t\t\t:disabled=\"isDisabled ||!isAgree\"\n\t\t\t\t@click='handleConfirm'\n\t\t\t\tsize='small'\n\t\t\t>{{$t('确 定')}}</fx-button>\n\t\t\t<fx-button\n\t\t\t\t@click='handleClosed'\n\t\t\t\tsize='small'\n\t\t\t>{{$t('取 消')}}</fx-button>\n\t\t</span>\n</fx-dialog>\n ",data:function(){return{isAgree:!1,visible:!0,time:29,isDisabled:!0,dialogVisible2:!1}},created:function(){var t=this,e=setInterval(function(){t.time--,t.time<=0&&(clearInterval(e),t.isDisabled=!1)},1e3)},methods:{handleConfirm:function(){this.isDisabled||(this.$emit("confirm"),this.$destroy())},handleClosed:function(){this.$destroy()}}}));e.cpqDeliveryClose=i});
define("crm-setting/shiporder/shiporder/page/manage/components/purchase-received-upstream-params-dialog",[],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.PurchaseReceivedUpstreamParamsDialog=void 0;var a=Vue.extend({template:'\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync="visible"\n\t\t\t  ref="dialog1"\n\t\t\t  :append-to-body="true"\n\t\t\t  title="采购业务联动-参数设置"\n\t\t\t  width="500px"\n\t\t\t  @closed="handleClosed"\n\t\t\t  class=\'purchase-received-upstream-params-dialog\'\n\t\t\t>\n\t\t\t\t<template >\n\t\t\t\t\t<div>当销售订单确认后</div>\n\t\t\t\t\t<fx-radio-group v-model=\'value\'>\n    \t\t\t\t<fx-radio label=\'1\'>\n    \t\t\t\t\t<span>在满足相关条件的前提下，系统自动创建发货单</span>\n\t\t\t\t\t\t</fx-radio>\n    \t\t\t\t<fx-radio label=\'0\'>\n    \t\t\t\t\t\t<span>系统不自动创建发货单，需要手动创建</span>\n\t\t\t\t\t\t</fx-radio>\n  \t\t\t\t</fx-radio-group>\n\t\t\t\t</template> \n\t\t\t\t\n\t\t\t\t<span slot="footer" class="dialog-footer">\n    \t\t\t\t<fx-button \n    \t\t\t\t\ttype="primary" \n    \t\t\t\t\t@click="handleConfirm" \n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{$t(\'确 定\')}}</fx-button>\n    \t\t\t\t<fx-button \n    \t\t\t\t\t@click="handleClosed" \n    \t\t\t\t\tsize="small"\n    \t\t\t\t>{{$t(\'取 消\')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t',props:{defaultValue:String},data:function(){return{visible:!0,value:""}},created:function(){this.value=this.defaultValue},methods:{getValue:function(){return this.value},handleConfirm:function(){this.$emit("confirm",this.getValue()),this.handleClosed()},handleClosed:function(){this.$emit("cancel",this.getValue()),this.$destroy()}}});e.PurchaseReceivedUpstreamParamsDialog=a});
define("crm-setting/shiporder/shiporder/page/manage/components/RealTimeStockDialog",[],function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.RealTimeStockDialog=void 0;var i=Vue.extend({template:"\n\t\t\t<fx-dialog\n\t\t\t  :visible.sync='visible'\n\t\t\t  ref='dialog1'\n\t\t\t  size='small'\n\t\t\t  :append-to-body='true'\n\t\t\t  :title=\"$t('stock.stock_manage.erp.text_1')\"\n\t\t\t  class='rts-dialog k3c-plugin-dialog'\n\t\t\t>\n\t\t\t\t<div v-loading=\"loading\">\n\t\t\t\t  <div style='color: #181C25'>\n\t\t\t\t  \t\t{{$t('stock.shiporder.erp_real_time_inventory.title')}}\n\t\t\t\t  \t\t<span class=\"fx-icon-question\" @click=\"handleClickERPHelp\"></span>\n\t\t\t\t  </div>\n\t\t\t\t  <fx-radio-group v-model='erp_real_time_inventory.type'>\n\t\t\t\t\t\t\t<fx-radio label=\"1\" class='rts-dialog__radio'>{{ $t('stock.shiporder.erp_real_time_inventory.by_integrated_platform') }}</fx-radio>\n\t\t\t\t\t\t\t<div class=\"radio-desc\" style='margin-bottom: 0'>{{ $t(\"stock.shiporder.erp_real_time_inventory.by_integrated_platform_describe_text\") }}</div>\n\t\t\t\t\t\t\t<fx-radio label=\"2\" class='rts-dialog__radio'>\n\t\t\t\t\t\t\t\t<span>{{ $t('stock.stock_manage.erp.text_2') }}</span>\n\t\t\t\t\t\t\t\t<a \n\t\t\t\t\t\t\t\t\tv-show='erp_real_time_inventory.type==2&&!erp_real_time_inventory.functionApiName' \n\t\t\t\t\t\t\t\t\tclass='icon-tips' \n\t\t\t\t\t\t\t\t\t@click='addFun'\n\t\t\t\t\t\t\t\t>{{$t('stock.stock_manage.erp.text_3')}}</a>\n\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t<div class='rts-dialog__apl radio-desc'  \n\t\t\t\t\t     style='margin-left: 46px'\n\t\t\t\t\t     v-show='erp_real_time_inventory.type==2&&erp_real_time_inventory.functionApiName'>\n\t\t\t\t\t        <div  class='rts-dialog__apl__row'>\n\t\t\t\t\t          <div>{{$t('stock.stock_manage.erp.text_4')}}</div>\n\t\t\t\t\t          <div>\n\t\t\t\t\t            <span class='rts-dialog__apl__btn' @click='handleDelete'>X</span>\n\t\t\t\t\t          </div>\n\t\t\t\t\t        </div>\n\t\t\t\t\t        <div  class='rts-dialog__apl__row'>\n\t\t\t\t\t          <div>{{$t('stock.stock_manage.erp.text_5')}}</div>\n\t\t\t\t\t          <div>{{erp_real_time_inventory.functionApiName}}</div>\n\t\t\t\t\t        </div>\n\t\t\t\t\t        <div  class='rts-dialog__apl__row'>\n\t\t\t\t\t          <div>{{$t('stock.stock_manage.erp.text_6')}}</div>\n\t\t\t\t\t          <div>{{erp_real_time_inventory.functionDes}}</div>\n\t\t\t\t\t        </div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div style='margin-top: 24px;'>\n\t\t\t\t\t\t<div style='color: #181C25'>{{$t('stock.stock_manage.erp.text_7')}}</div>\n\t\t\t\t\t\t<fx-radio-group v-model='erp_real_time_inventory.dataProcessingType'>\n\t\t\t\t\t\t\t<fx-radio label=\"0\" class='rts-dialog__radio'>{{ $t('stock.stock_manage.erp.text_8') }}</fx-radio>\n\t\t\t\t\t\t\t<fx-radio label=\"1\" class='rts-dialog__radio'>{{ $t('stock.stock_manage.erp.text_9') }}</fx-radio>\n\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<span slot='footer' class='dialog-footer'>\n    \t\t\t\t<fx-button \n    \t\t\t\t\ttype='primary' \n    \t\t\t\t\t@click='handleConfirm' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t\t:disabled=\"loading\"\n    \t\t\t\t>{{$t('确 定')}}</fx-button>\n    \t\t\t\t<fx-button \n    \t\t\t\t\t@click='handleClosed' \n    \t\t\t\t\tsize='small'\n    \t\t\t\t\t:disabled=\"loading\"\n    \t\t\t\t>{{$t('取 消')}}</fx-button>\n    \t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t",props:{default_erp_real_time_inventory:Object},data:function(){return{loading:!1,visible:!0,erp_real_time_inventory:{type:"1",functionApiName:"",functionDes:"",dataProcessingType:"0"}}},created:function(){this.erp_real_time_inventory=this.default_erp_real_time_inventory},methods:{handleClickERPHelp:function(){window.open("https://help.fxiaoke.com/1969/7d85/a93b","_blank")},getSdkHelper:function(){var n=this;return this.loading=!0,new Promise(function(e){seajs.use("paas-function/sdk",function(t){n.loading=!1,e(t)})})},addFun:function(){var e=this;this.getSdkHelper().then(function(t){t.getControllerFunction({},function(t){t.status?(e.erp_real_time_inventory.functionApiName=t.data.function.api_name,e.erp_real_time_inventory.functionDes=t.data.function.remark):FS.crmUtil.remind(3,$t("操作失败"))})})},handleDelete:function(){this.erp_real_time_inventory.functionApiName="",this.erp_real_time_inventory.functionDes=" "},handleEdit:function(){return this.addFun()},handleConfirm:function(){this.$emit("confirm",this.erp_real_time_inventory),this.handleClosed()},handleClosed:function(){this.$emit("close"),this.$destroy()}}});e.RealTimeStockDialog=i});
define("crm-setting/shiporder/shiporder/page/manage/components/SmartReplenishmentDialog",["crm-modules/common/stock/api"],function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.SmartReplenishmentDialog=void 0;var i=t("crm-modules/common/stock/api"),t=Vue.extend({template:'\n<fx-dialog\nsize="big"\n:visible.sync="dialogVisible"\n:showFullscreenSwitch="true"\n:append-to-body="true"\n:close-on-click-outside="false"\n:close-on-click-modal="false"\n:title="$t(\'设置\')"\nv-loading="loading"\n>\n\t<div class="smartReplenishment_div">\n\t\t<div class="smartReplenishment_illustrate">\n\t\t\t<span>{{$t("说明")}}</span><br />\n\t\t\t1.{{$t("打开“智能预估”开关，在新增/编辑采购订单时预置“智能预估”按钮")}}<br />\n\t\t\t2.{{$t("本次仅支持“采购订单”进行预估")}}<br />\n\t\t\t3.{{$t("采购预估默认设置为“按照固定公式预估”")}}<br />\n\t\t\t4.{{$t("取值时间默认范围：取最近“30天”相关数据")}}<br />\n\t\t\t5.{{$t("建议采购量默认公式：销售总数量-库存余量-已锁定的库存量")}}<br />\n\t\t</div>\n\n\t\t<div class="smartReplenishment_type">\n\t\t\t<span class="title">{{$t("智能预估方式")}}:</span>\n\t\t\t<fx-radio v-model="radio" label="1">{{$t("按照预置公式")}}</fx-radio>\n\t\t\t<fx-radio v-model="radio" label="2">{{$t("按照函数")}}</fx-radio>\n\t\t\t<div class="APLDiv" v-if="radio==\'2\'">\n\t\t\t\t<span v-if="radio==\'2\' && !functionName" class="spl_span" @click="addAPL">{{$t("选择APL代码")}}</span>\n\t\t\t\t<div v-if="radio==\'2\' && functionName" class="APL-detail">\n\t\t\t\t\t<div class="APL-name">\n\t\t\t\t\t\t<div>{{ $t("采购订单产品智能预估函数") + functionName }}</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<span @click="closeAPL">{{ $t("删除") }}</span>\n\t\t\t\t\t\t\t<span @click="addAPL2()">{{$t("选择APL代码")}}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="APL-dec">{{ $t("函数描述：") + functionDesc }}</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\t\x3c!-- 预置公式 --\x3e\n\t\t<div class="smartReplenishment_default" v-if="radio==\'1\'">\n\t\t\t<span class="left_name">{{$t("预置公式")}}</span>\n\t\t\t<span>{{$t("建议采购量")}}<fx-tooltip class="item" effect="dark" :content="$t(\'通过预置公式计算后得出的结果，将填入采购数量字段\')" placement="top-start">\n\t\t\t<i class="shiporder-tip_icon">?</i></fx-tooltip>&nbsp;&nbsp;=&nbsp;&nbsp;{{$t("销售总数量")}}<fx-tooltip class="item" effect="dark" :content="$t(\'所选时间范围内的销售订单产品数量之和\')" placement="top-start">\n\t\t\t<i class="shiporder-tip_icon">?</i></fx-tooltip>&nbsp;&nbsp;-&nbsp;&nbsp;{{$t("可用库存量")}}<fx-tooltip class="item" effect="dark" :content="$t(\'当前产品的可用库存数量之和\')" placement="top-start">\n\t\t\t<i class="shiporder-tip_icon">?</i></fx-tooltip></span>\n\t\t\t<span>&nbsp;&nbsp;-&nbsp;&nbsp;<fx-checkbox v-model="onRoadchecked">{{$t("在途采购量")}}<fx-tooltip class="item" effect="dark" :content="$t(\'所选时间范围内的采购订单产品的未入库数量之和\')" placement="top-start">\n\t\t\t<i class="shiporder-tip_icon">?</i></fx-tooltip></fx-checkbox></span>\n\t\t\t<span>&nbsp;&nbsp;-&nbsp;&nbsp;<fx-checkbox v-model="safeStockChecked">{{$t("安全库存量")}}<fx-tooltip class="item" effect="dark" :content="$t(\'产品在产品档案中设置的”安全库存“数量\')" placement="top-start">\n\t\t\t<i class="shiporder-tip_icon">?</i></fx-tooltip></fx-checkbox></span>\n\t\t\t<div class="line"></div>\n\t\t\t<span class="left_name">{{$t("取值时间范围")}}:</span><span>{{$t("取最近")}}<fx-input class="smartReplenishment_day" :decimal-places="0" :integer-places="2" size="mini" ref="number" v-model="defaultDay"></fx-input>{{$t("天(不含今天)的销售量和在途数采购量")}}</span>\n\t\t</div>\n\t</div>\n\t<span slot="footer" class="dialog-footer">\n\t\t<fx-button v-if="radio == 2 && !functionName" disabled type="primary" @click="handleConfirm" size="small">{{$t("确定")}}</fx-button>\n\t\t<fx-button v-else type="primary" @click="handleConfirm" size="small">{{$t("确定")}}</fx-button>\n\t\t<fx-button @click="handleClosed" size="small">{{$t("取消")}}</fx-button>\n\t</span>\n</fx-dialog>',props:{intelligentReplenishmentSwitch:String},data:function(){return{loading:!0,dialogVisible:!0,radio:"1",onRoadchecked:!1,safeStockChecked:!1,defaultDay:"",functionName:"",functionDesc:""}},created:function(){this.getSmartSetting()},methods:{handleConfirm:function(){this.setSmartSetting()},handleClosed:function(){this.$destroy()},getSmartSetting:function(){var n=this;(0,i.getSmartSettings)().then(function(t){t.isSuccess&&(t=t.data.intelligentPrediction,n.radio=t.type+"",1==t.type?(n.onRoadchecked=t.inPurchase,n.safeStockChecked=t.safetyStock,n.defaultDay=t.timeFrame):(n.functionName=t.functionName,n.functionDesc=t.functionDesc)),n.loading=!1})},setSmartSetting:function(){var t,n=this;2!=this.radio||this.functionName?1!=this.radio||this.defaultDay?(t={},t=2==this.radio?{type:this.radio,functionName:this.functionName,functionDesc:this.functionDesc}:{type:this.radio,safetyStock:this.safeStockChecked,inPurchase:this.onRoadchecked,timeFrame:this.defaultDay},t={intelligentReplenishmentSwitch:this.intelligentReplenishmentSwitch,intelligentPrediction:t},this.loading=!0,i.setSmartSettings.circle(t,function(t,n){1==t.resultCode&&n(t)}).res(function(t){n.loading=!1,FS.crmUtil.remind(1,$t("操作成功")),n.$destroy()}).catch(function(t){FS.crmUtil.remind(3,t),n.loading=!1})):FS.crmUtil.remind(3,$t("stock.crm.setting.shiporder.please_set_a_time_range")):FS.crmUtil.remind(3,$t("stock.crm.setting.shiporder.please_set_the_function_first"))},closeAPL:function(){this.functionName="",this.functionDesc=""},addAPL:function(t){var n=this,e=this.functionName?2:1,i={object_api_name:"PurchaseOrderObj"};2==e&&(i.api_name=this.functionName);this.getSdkHelper().then(function(t){1==e?t.getStockReplenishmentFunction(i,function(t){t.status?(n.functionName=t.data.function.api_name,n.functionDesc=t.data.function.remark):FS.crmUtil.remind(3,$t("操作失败"))}):t.update(i,function(t){})})},addAPL2:function(){var n=this,e={object_api_name:"PurchaseOrderObj"};this.getSdkHelper().then(function(t){t.getStockReplenishmentFunction(e,function(t){t.status?(n.functionName=t.data.function.api_name,n.functionDesc=t.data.function.remark):FS.crmUtil.remind(3,$t("操作失败"))})})},getSdkHelper:function(){return new Promise(function(n){seajs.use("paas-function/sdk",function(t){n(t)})})}}});n.SmartReplenishmentDialog=t});
define("crm-setting/shiporder/shiporder/page/manage/dialogs/batch-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="receive-dialog-content"> <div class="shiporder-base-info_float-setting-title">' + ((__t = $t("临到期库存预警")) == null ? "" : __t) + '</div> <div class="shiporder-base-info_float-setting-options"> <div class="shiporder-base-info_float-setting-item"> <label for="radio"> <input type="radio" name="radio" value="2" ' + ((__t = obj.willExpireStockWarningType == 2 ? "checked" : "") == null ? "" : __t) + ' action-type="radio"> ' + ((__t = $t("开启预警")) == null ? "" : __t) + ' </label> </div> <div class="shiporder-base-info_float-setting-item"> <label for="radio"> <input type="radio" name="radio" value="1" ' + ((__t = obj.willExpireStockWarningType == 1 ? "checked" : "") == null ? "" : __t) + ' action-type="radio"> ' + ((__t = $t("不开启预警")) == null ? "" : __t) + " </label> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/manage/dialogs/receive-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="receive-dialog-content"> <div class="shiporder-base-info_float-setting-item"> <div class="shiporder-base-info_float-setting-content"> <label for="" > ' + ((__t = $t("发货单确认后")) == null ? "" : __t) + ' <input type="number" class="j-auto-receive-days" value="' + ((__t = obj.daysAfterShipment) == null ? "" : __t) + '" onkeyup="value=value.replace(/^(0+)|[^\\d]+/g,\'\')" action-type="inputChange" /> ' + ((__t = $t("天，系统自动确认收货")) == null ? "" : __t) + '<i class="icon-tips"></i ></label> </div> <div class="shiporder-base-info_float-setting-content"> <label for=""> ' + ((__t = $t("发货单物流签收后，系统自动确认收货")) == null ? "" : __t) + "</label> <p> " + ((__t = $t("需要在发货单填写正确的物流信息，并订阅物流签收提醒，每次订阅均会扣除一次物流查询次数")) == null ? "" : __t) + " </p> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/manage/index",["crm-setting/shiporder/shiporder/page/manage/components/K3CDialog","crm-setting/shiporder/shiporder/page/manage/components/CostManagement","crm-setting/shiporder/shiporder/page/manage/components/BatchSnDialog","crm-setting/shiporder/shiporder/page/manage/components/RealTimeStockDialog","crm-setting/shiporder/shiporder/page/manage/components/multiOrderDeliveryDialog","crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuide","crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuideStock","crm-setting/shiporder/shiporder/page/manage/components/AutoConfirmReceiptDialog","crm-setting/shiporder/shiporder/page/manage/components/InterconnectionSettingDialog","crm-setting/shiporder/shiporder/page/manage/components/SmartReplenishmentDialog","crm-setting/shiporder/shiporder/page/manage/components/ExchangeSettingDialog","crm-setting/shiporder/shiporder/page/manage/components/DeliveryNoteReturnSetting","crm-setting/shiporder/shiporder/page/manage/components/FreezeInventoryAdjustment","crm-modules/common/stock/api","./tpl-html","./dialogs/receive-html","./cardTpl-html"],function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Manage=void 0;var i=e("crm-setting/shiporder/shiporder/page/manage/components/K3CDialog"),o=e("crm-setting/shiporder/shiporder/page/manage/components/CostManagement"),r=e("crm-setting/shiporder/shiporder/page/manage/components/BatchSnDialog"),s=e("crm-setting/shiporder/shiporder/page/manage/components/RealTimeStockDialog"),a=e("crm-setting/shiporder/shiporder/page/manage/components/multiOrderDeliveryDialog"),c=e("crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuide"),l=e("crm-setting/shiporder/shiporder/page/manage/components/CustomFunctionGuideStock"),d=e("crm-setting/shiporder/shiporder/page/manage/components/AutoConfirmReceiptDialog"),m=e("crm-setting/shiporder/shiporder/page/manage/components/InterconnectionSettingDialog"),h=e("crm-setting/shiporder/shiporder/page/manage/components/SmartReplenishmentDialog"),u=e("crm-setting/shiporder/shiporder/page/manage/components/ExchangeSettingDialog"),p=e("crm-setting/shiporder/shiporder/page/manage/components/DeliveryNoteReturnSetting"),_=e("crm-setting/shiporder/shiporder/page/manage/components/FreezeInventoryAdjustment"),g=e("crm-modules/common/stock/api"),e=Backbone.View.extend({template:e("./tpl-html"),dialogReceive:e("./dialogs/receive-html"),cardTpl:e("./cardTpl-html"),events:{"click .j-switch-autoreceive":"_handleSwitchAutoReceive","click .j-switch-express":"_handleSwitchExpress","click .j-setting-receive":"_handleSettingReceive","click .j-setting-batch":"_handleSettingBatch","click .j-switch-batch":"_handleEnableBatch","click .j-multi-switch":"_handleMultiSwitch","click .j-switch-warehouse-position":"_handleEnableWarehousePosition","click .j-switch-cpq-delivery-way":"_cpqDeliveryWayChangeHandle","click .j-switch-cost-adjustment":"_handleCostAdjustmentSwitch","click .j-switch-delivery-note-return":"_handleSwitchDeliveryNoteReturn","click .j-switch-return":"_handleSwitchReturn","click .j-switch-exchange":"_handleSwitchExchange","click .j-switch-action":"handleActionSwitch","click .j-switch-k3c":"_handleK3CSwtich","click .j-setting-k3c":"_handleK3CSetting","click .j-setting-cost-management":"_handleKCostManagementSetting","click .j-cf-config":"_handleCustomFunctionConfigClick","click .j-switch-negative-inventory":"_handleNegativeInventory","click .j-setting-interconnection":"_handleSettingInterconnection","click .j-switch-smart":"_handleSmartSwtich","click .j-setting-smart":"_handleSmartSetting","click .j-switch-distribution-stock":"_handleSwitchDistributionStock","click .j-unique-product-code-management":"_handleUniqueProductCodeManagement","click .j-sales-order-ato-pto-switch":"_handleSalesOrderAtoPtoSwitch","click .j-setting-exchange":"_handleExchangeSetting","click .j-switch-realtimestock":"_handleRealTimeStockSwitch","click .j-setting-realtimestock":"_handleSettingRealTimeStock","click .j-setting-delivery_return_product_add_mode":"_handleDeliveryNoteReturnSetting","click .j-freeze-inventory-adjustment-switch":"handleFreezeInventoryAdjustment"},handleFreezeInventoryAdjustment:function(){var e,t,n=this;"1"!=this.model.get("order_freeze_inventory_adjustment")&&("1"===this.model.get("sales_order_ato_pto_switch")?FS.crmUtil.remind(3,$t("stock.stock_manage.ato_pto.text_1")):(e=$('<div id="app"></div>'),$("body").append(e),(t=new _.FreezeInventoryAdjustment({el:e[0],propsData:{isStockBAndDistributionOpen:1==this.model.get("isForErp")&&"2"==this.model.get("distribution_stock_switch")}})).$on("close",function(e){e&&(n.model.set({order_freeze_inventory_adjustment:"1"}),$(".j-freeze-inventory-adjustment-switch").addClass("oprate-btn_switch--on oprate-btn_switch--disabled")),t.$destroy(),t=null})))},_handleDeliveryNoteReturnSetting:function(){var e=$('<div id="app"></div>');$("body").append(e),new p.DeliveryNoteReturnSetting({el:e[0],propsData:{isATOPTOOpen:"1"===this.model.get("sales_order_ato_pto_switch")}})},initialize:function(){this.model.isShowStockLoading()},render:function(){var e=this;this.initPluginConfigs().then(function(){e.renderTemplate()})},renderTemplate:function(){var e=this.model.toJSON();this.$el&&this.$el.html(this.template(e))},initPluginConfigs:function(){var e=[this.getPluginConfig(),this.getCPQDeliveryWay(),this.getStockConfigData(),this.getPluginsGray(),this._getSmartSettings(),this.getGrayForCurtAvailableStock()];return Promise.all(e)},getGrayForCurtAvailableStock:function(){var t=this;return(0,g.getGrayForCurtAvailableStock)().then(function(e){t.model.set({isShowRealTimeAvailableStock:e.isShowRealTimeAvailableStock})})},getPluginsGray:function(){var t=this;return(0,g.isAllowSetStockConf)({configTypes:["delivery_note_multi_order_deliver_switch","negative_inventory_allowed_plugin_switch","open_multi_sn","intelligent_replenishment_switch","dht_serial_number_param_switch","erp_real_time_inventory","distribution_stock_switch","unique_product_code_management","sales_order_ato_pto_switch"]}).then(function(e){t.model.set({hasMultiOrderDeliveryGray:e.delivery_note_multi_order_deliver_switch,negative_inventory_allowed_plugin_switch:e.negative_inventory_allowed_plugin_switch,has_open_multi_sn:e.open_multi_sn,intelligent_replenishment_switch:e.intelligent_replenishment_switch,dht_serial_number_param_switch:e.dht_serial_number_param_switch,erp_real_time_inventory_gray:e.erp_real_time_inventory,distribution_stock_switch_gray:e.distribution_stock_switch,unique_product_code_management_gray:e.unique_product_code_management,sales_order_ato_pto_switch_gray:e.sales_order_ato_pto_switch})}).catch(function(e){})},getStockConfigData:function(){var i=this;return(0,g.commonGetConfigValueByKeys)({keys:[],isAllConfig:!0}).then(function(e){var t={type:"0",functionApiName:"",functionDes:"",dataProcessingType:"0"},n=(e.erp_real_time_inventory&&(t=JSON.parse(e.erp_real_time_inventory)),{status:"0",update_time:""});return e.order_freeze_inventory_adjustment&&(n=JSON.parse(e.order_freeze_inventory_adjustment)),i.model.set({order_freeze_inventory_adjustment:n.status,delivery_return_product_add_mode:e.delivery_return_product_add_mode,multiOrderDeliverySwitch:+e.delivery_note_multi_order_deliver_switch,delivery_note_cpq_deliver_switch:e.delivery_note_cpq_deliver_switch,delivery_note_returned_goods_switch:e.delivery_note_returned_goods_switch,non_note_returned_goods_switch:e.non_note_returned_goods_switch,returned_goods_exchange_switch:e.returned_goods_exchange_switch,distribution_stock_switch:e.distribution_stock_switch,erp_real_time_inventory:t,unique_product_code_management:e.unique_product_code_management,sales_order_ato_pto_switch:e.sales_order_ato_pto_switch,cost_management_calculate:e.cost_management_calculate}),e}).then(function(e){var t=e.delivery_note_automatic_receiving_config&&JSON.parse(e.delivery_note_automatic_receiving_config);return i.model.set({daysAfterShipment:t.daysAfterShipment,autoReceiveStatus:t.status,autoConfirmReceiptType:t.type}),e}).catch(function(e){})},getCPQDeliveryWay:function(){var n=this;return new Promise(function(t,e){n.model.getCPQDeliveryWay().then(function(e){n.model.set(e),t(void 0)},function(e){t(void 0)})})},getPluginConfig:function(){var t=this;return(0,g.getPluginConfig)().then(function(e){return t.model.set({isOpenWareHousePosition:1==+e.isOpenWarePosition,expressFuncCode:e.deliveryNoteExpressFuncType,batchSNSwitch:e.batchSnConfigInfo&&e.batchSnConfigInfo.batchSNSwitch,willExpireStockWarningType:e.batchSnConfigInfo&&e.batchSnConfigInfo.willExpireStockWarningType,multiSwitch:e.stockSalesModuleMultipleUnitOptimizeSwitch,displayAdvancedLogisticsQuery:e.displayAdvancedLogisticsQuery,costAdjustmentEnableSwitch:e.costAdjustmentEnableSwitch,kingDeeK3CSyncPluginSwitch:e.kingDeeK3CSyncPluginSwitch,kingDeeK3CSyncType:e.kingDeeK3CSyncType,isUpstreamEnterprise:e.isUpstreamEnterprise,isDownstreamEnterprise:e.isDownstreamEnterprise,upstreamDisplayStockInterconnection:e.upstreamDisplayStockInterconnection,downstreamDisplayStockInterconnection:e.downstreamDisplayStockInterconnection,purchaseReceivedWarehouseName:e.purchaseReceivedWarehouseName,autoGenerateDeliveryConfig:e.autoGenerateDeliveryConfig,negativeInventorySwitch:e.negativeInventorySwitch}),e.isUpstreamEnterprise?t.model.set({currentIdentity:"up"}):e.isDownstreamEnterprise&&t.model.set({currentIdentity:"down"}),e}).then(function(e){if(e.isDownstreamEnterprise)return(0,g.getDownstreamQueryRelatedUpstream)().then(function(e){t.model.set({correspondingUpstreamList:e.upstreamEnterpriseInfos,correspondingUpstream:e.upstreamEnterpriseInfos[0].upstreamTenantId})})})},_renderBatch:function(){var t=this;this.model.fetchBatch(function(e){$(".j-card-batch").html(t.cardTpl({title:"批次与序列号",content:"开启批次与序列号管理",isOn:2==e.batchSNSwitch,disabled:2==e.batchSNSwitch,settingTriger:"j-setting-batch",switchTriger:"j-switch-batch"}))})},_renderReceive:function(){var n=this;this.model.getReceivingInfo(function(e){var t=e.daysAfterShipment||7;$(".j-card-receive").html(n.cardTpl({title:"自动确认收货",content:"当发货单确认后，超过 "+t+" 天时，发货单会自动确认收货",isOn:1==e.autoReceiveStatus,settingTriger:"j-setting-receive",switchTriger:"j-switch-autoreceive"}))})},_handleSwitch:function(e){e=$(e.currentTarget);e.hasClass("oprate-btn_switch--on")?e.removeClass("oprate-btn_switch--on"):e.addClass("oprate-btn_switch--on")},_handleSwitchAutoReceive:function(e){var t=this;(0,g.setAutoConfirmReceipt)({type:this.model.get("autoConfirmReceiptType"),daysAfterShipment:this.model.get("daysAfterShipment"),status:1===this.model.get("autoReceiveStatus")?2:1}).then(function(){FS.crmUtil.remind(1,$t("操作成功")),t.model.set("autoReceiveStatus",1===t.model.get("autoReceiveStatus")?2:1),t.renderTemplate()})},_handleSwitchExpress:function(e){var t=$(e.currentTarget).hasClass("oprate-btn_switch--on")?1:2;this.model.set("expressFuncCode",t),this.model.setExpressFun(t),this._handleSwitch(e)},_handleSettingReceive:function(e){var n=this,t=$("<div></div>"),t=($("body").append(t),new d.AutoConfirmReceiptDialog({el:t[0],propsData:{defaultValue:this.model.get("autoConfirmReceiptType"),defaultDays:this.model.get("daysAfterShipment")}}));t.$on("confirm",function(e){n.model.set({daysAfterShipment:e.days,autoConfirmReceiptType:e.value});var t=[];t.push((0,g.setAutoConfirmReceipt)({type:e.value,daysAfterShipment:e.days,status:1})),e.hasOpenSign&&t.push((0,g.setDeliveryNoteSupportSignatures)({status:e.SSstatus,upStreamSigner:e.SSupStreamSigner,downStreamSigner:e.SSdownStreamSigner,printTemplateId:e.SSprintTemplateId,signAppType:e.SSsignAppType})),Promise.all(t).then(function(){FS.crmUtil.remind(1,$t("操作成功")),n.renderTemplate()}).catch(function(e){FS.crmUtil.remind(3,$t(e))})}),t.$on("close",function(e){n.renderTemplate()})},_handleSettingBatch:function(){var t=this,e=$('<div id="app"></div>'),e=($("body").append(e),new r.BatchSnDialog({el:e[0],propsData:{propWillExpireStockWarningType:this.model.get("willExpireStockWarningType"),has_open_multi_sn:this.model.get("has_open_multi_sn"),propOpenMultiSn:this.model.get("openMultiSn"),dht_serial_number_param_switch:this.model.get("dht_serial_number_param_switch")}}));e.$on("updateWillExpireStockWarningType",function(e){t.model.set("willExpireStockWarningType",e),FS.crmUtil.remind(1,$t("操作成功"))}),e.$on("updateOpenMultiSn",function(e){t.model.set("openMultiSn",e),FS.crmUtil.remind(1,$t("操作成功"))})},_handleEnableBatch:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("批次与序列号开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启批次与序列号？")+"</p>",t=FS.crmUtil.confirm(e,$t("批次与序列号"),function(){t.hide(),i.addClass("oprate-btn_switch--disabled oprate-btn_switch--on"),g.enableBatch.circle({},function(e,t){1==e.resultCode&&t(e)}).res(function(){n.model.set("batchSNSwitch",2),n.renderTemplate(),FS.crmUtil.remind(1,$t("批次与序列号开启成功"))}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleMultiSwitch:function(e){var t,n=$(e.currentTarget),i=this;n.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("快消行业多单位开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启快消行业多单位？")+"</p>",t=FS.crmUtil.confirm(e,$t("快消行业多单位"),function(){t.hide(),i.model.set("showMask",!0),g.enableStockSaleMulti.circle({},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),n.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),i.model.set("showMask",!1)}).catch(function(e){i.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleCostAdjustmentSwitch:function(e){var t,n=$(e.currentTarget);n.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("成本管理开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启成本管理？")+"</p>",t=FS.crmUtil.confirm(e,$t("成本管理"),function(){t.hide(),n.addClass("oprate-btn_switch--disabled oprate-btn_switch--on"),FS.crmUtil.remind(1,$t("正在开启成本管理..."),null,2e3),g.enableCostAdjustment.circle({},function(e,t){1==e.resultCode&&t(e)}).res(function(){FS.crmUtil.remind(1,$t("成本管理开启成功"))}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleSwitchDeliveryNoteReturn:function(e){return this._handleSwitchAsyncSetConfig(e,"delivery_note_returned_goods_switch")},_handleSwitchReturn:function(e){return this._handleSwitchAsyncSetConfig(e,"non_note_returned_goods_switch")},_handleSwitchExchange:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||($t("stock.shiporder.exchange_plugin_open.text"),t=FS.crmUtil.confirm($t("stock.shiporder.exchange_plugin_open.text"),$t("stock.shiporder.exchange_plugin_open.title"),function(){t.hide(),i.addClass("oprate-btn_switch--disabled oprate-btn_switch--on"),g.enableExchangePlugin.circle({key:"returned_goods_exchange_switch",value:"2"},function(e,t){1==e.resultCode&&t(e)}).res(function(){n.model.set("returned_goods_exchange_switch",2),n.renderTemplate(),FS.crmUtil.remind(1,$t("stock.shiporder.exchange_plugin_open.success"))}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleSwitchAsyncSetConfig:function(e,t){var n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(this.model.set("showMask",!0),e=this.model.get(t),(0,g.asyncSetConfigValue)().circle({key:t,value:"1"===e?"2":"1",token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):(FS.crmUtil.remind(1,$t("操作成功")),n.model.set(t,"2"),i.addClass("oprate-btn_switch--on oprate-btn_switch--disabled")),n.model.set("showMask",!1)}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服")),n.model.set("showMask",!1)}))},_handleK3CSetting:function(e){var t=this,n=$('<div id="app"></div>');$("body").append(n),new i.K3CDialog({el:n[0],propsData:{propSyncType:this.model.get("kingDeeK3CSyncType")}}).$on("updateKingDeeK3CSyncType",function(e){t.model.set("kingDeeK3CSyncType",e),FS.crmUtil.remind(1,$t("操作成功"))})},_handleRealTimeStockSwitch:function(e){var t=this,n=$(e.currentTarget),e="1"==this.model.get("erp_real_time_inventory").type?"0":"1",i="1"==e,o={type:e,functionApiName:"",functionDes:"",dataProcessingType:"0"};(0,g.set_erp_real_time_inventory)(JSON.stringify(o)).then(function(){t.model.set("erp_real_time_inventory",o),FS.crmUtil.remind(1,$t("操作成功")),i?(n.addClass("oprate-btn_switch--on"),$(".shiporder-manage_category-card-option__j-switch-realtimestock").addClass("active j-setting-realtimestock")):(n.removeClass("oprate-btn_switch--on"),$(".shiporder-manage_category-card-option__j-switch-realtimestock").removeClass("active j-setting-realtimestock"))})},_handleSettingRealTimeStock:function(e){var t=this,n=$('<div id="app"></div>');$("body").append(n),new s.RealTimeStockDialog({el:n[0],propsData:{default_erp_real_time_inventory:this.model.get("erp_real_time_inventory")}}).$on("confirm",function(e){t.model.set("erp_real_time_inventory",e),(0,g.set_erp_real_time_inventory)(JSON.stringify(e)).then(function(){FS.crmUtil.remind(1,$t("操作成功"))})})},_handleKCostManagementSetting:function(e){var t=this,n=$('<div id="app"></div>');$("body").append(n),new o.CostManagementDialog({el:n[0],propsData:{prop_cost_management_calculate:this.model.get("cost_management_calculate")}}).$on("updatecost_management_calculate",function(e){t.model.set("cost_management_calculate",e),FS.crmUtil.remind(1,$t("操作成功"))})},getDownStreamParams:function(){var e=this.model.get("correspondingUpstreamList").map(function(e){return Object.assign(Object.assign({},e),{value:e.upstreamTenantId,label:e.upstreamName})});return{correspondingUpstreamList:e,selectedCorrespondingUpstream:e.length?e[0].upstreamTenantId:""}},getPurchaseUpstreamParam:function(){return{autoGenerateDeliveryConfig:this.model.get("autoGenerateDeliveryConfig")}},_handleK3CSwtich:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("金蝶K3C对接插件开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启该插件？")+"</p>",t=FS.crmUtil.confirm(e,$t("stock.crm.setting.shiporder.open_K3C_plugin"),function(){t.hide(),n.model.set("showMask",!0),g.asyncEnableK3C.circle({},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),i.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),$(".shiporder-manage_category-card-option__k3c-param").addClass("active j-setting-k3c"),n.model.set("showMask",!1)}).catch(function(e){n.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_handleEnableWarehousePosition:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("仓位管理开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启仓位管理？")+"</p>",t=FS.crmUtil.confirm(e,$t("开启仓位管理"),function(){t.hide(),n.model.set("showMask",!0),g.enableParentWareHouse.circle({},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),i.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),n.model.set("showMask",!1)}).catch(function(e){n.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}))},_cpqDeliveryWayChangeHandle:function(e){this.model.get("sales_order_ato_pto_switch_gray");this.cpqDeliveryWayChangeHandle_of_ato_pto(e)},cpqDeliveryWayChangeHandle:function(e){var t=this,n=$(e.currentTarget),e=this.model.get("cpqDeliveryWay");this.model.set("showMask",!0),(0,g.asyncSetConfigValue)().circle({key:"delivery_note_cpq_deliver_switch",value:1===e?2:1,token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),n.toggleClass("oprate-btn_switch--on"),t.model.set("showMask",!1)}).catch(function(e){t.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})},cpqDeliveryWayChangeHandle_of_ato_pto:function(e){var t=this,n=$(e.currentTarget);1==this.model.get("cpqDeliveryWay")?(e=$("<div></div>"),$("body").append(e),new a.cpqDeliveryClose({el:e[0]}).$on("confirm",function(){t.model.set("showMask",!0),(0,g.asyncSetConfigValue)().circle({key:"delivery_note_cpq_deliver_switch",value:2,token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),n.removeClass("oprate-btn_switch--on"),t.model.set("showMask",!1)}).catch(function(e){t.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})})):(this.model.set("showMask",!0),(0,g.asyncSetConfigValue)().circle({key:"delivery_note_cpq_deliver_switch",value:1,token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),n.toggleClass("oprate-btn_switch--on"),t.model.set("showMask",!1)}).catch(function(e){t.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))}))},handleActionSwitch:function(e){var t=this,n=$(e.currentTarget),e=$("<div></div>"),i=($("body").append(e),this.model.get("multiOrderDeliverySwitch"));i?new a.multiOrderDeliveryClose({el:e[0]}).$on("confirm",function(){t.model.set("showMask",!0),(0,g.asyncSetConfigValue)().circle({key:"delivery_note_multi_order_deliver_switch",value:0,token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),t.model.set({multiOrderDeliverySwitch:!i}),n.removeClass("oprate-btn_switch--on"),t.model.set("showMask",!1)}).catch(function(e){t.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})}):new a.multiOrderDeliveryOpen({el:e[0]}).$on("confirm",function(){t.model.set("showMask",!0),(0,g.asyncSetConfigValue)().circle({key:"delivery_note_multi_order_deliver_switch",value:1,token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(e){e=e.Value&&e.Value.Result.FailureMessage||null;e?FS.crmUtil.remind(3,e):FS.crmUtil.remind(1,$t("操作成功")),t.model.set({multiOrderDeliverySwitch:!i}),n.toggleClass("oprate-btn_switch--on"),t.model.set("showMask",!1)}).catch(function(e){t.model.set("showMask",!1),FS.crmUtil.remind(3,e||$t("启用失败请稍后刷新重试或联系纷享客服"))})})},_handleCustomFunctionConfigClick:function(e){e.stopPropagation();e=e.target,e=$(e).data("type"),e={config:this._handleCustomFunctionConfig,flow:this._handleCustomFunctionFlow,guide:this._handleCustomFunctionGuide,guideStock:this._handleCustomFunctionGuideStock}[e];e&&e.call(this)},_openNewTab:function(e){var t=$("<a></a>");t.attr("href",e),t.attr("target","_blank"),$("body").append(t),t[0].click(),t.remove()},_handleCustomFunctionConfig:function(){var e=location.origin+location.pathname+"#crmmanage/=/module-myfunction";return this._openNewTab(e)},_handleCustomFunctionFlow:function(){var e=location.origin+location.pathname+"#crmmanage/=/module-approval";return this._openNewTab(e)},_handleCustomFunctionGuide:function(){var e=$("<div></div>"),t=($("body").append(e),new c.CustomFunctionGuide({el:e[0],name:"CustomFunctionGuide"}));t.$on("close",function(){t.$destroy()})},_handleCustomFunctionGuideStock:function(){var e=$("<div></div>"),t=($("body").append(e),new l.CustomFunctionGuideStock({el:e[0],name:"customFunctionGuideStock"}));t.$on("close",function(){t.$destroy()})},_handleSettingInterconnection:function(){var t=this,e={isUp:this.model.get("isUpstreamEnterprise"),isDown:this.model.get("isDownstreamEnterprise")},n=this.getPurchaseUpstreamParam(),i=this.getDownStreamParams(),o=$("<div></div>");$("body").append(o),new m.InterconnectionSettingDialog({el:o[0],name:"InterconnectionSettingDialog",propsData:{identityInfos:e,upStreamParams:n,downStreamParams:i}}).$on("updateUpStreamParams",function(e){t.model.set("autoGenerateDeliveryConfig",e.autoGenerateDeliveryConfig)})},_handleNegativeInventory:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(t="1"===this.model.get("negativeInventorySwitch")?"2":"1",this.model.set("showMask",!0),(0,g.setNegativeInventorySwitch)({negativeInventorySwitch:t}).then(function(e){e.isSuccess?(FS.crmUtil.remind(1,$t("操作成功")),n.model.set({negativeInventorySwitch:t}),"1"==t?i.addClass("oprate-btn_switch--on"):i.removeClass("oprate-btn_switch--on")):FS.crmUtil.remind(1,$t("操作失败"))}).finally(function(){n.model.set("showMask",!1)}))},_handleSmartSwtich:function(){var t=this,n=(t.model.set("showMask",!0),"2"==t.model.get("intelligentReplenishmentSwitch")?3:2);g.setSmartSettings.circle({intelligentReplenishmentSwitch:n},function(e,t){1==e.resultCode&&t(e)}).res(function(e){t.model.set("showMask",!1),1==e.resultCode&&(t.model.set({intelligentReplenishmentSwitch:n}),FS.crmUtil.remind(1,$t("操作成功"))),t.renderTemplate()}).catch(function(e){t.model.set("showMask",!1)})},_handleSmartSetting:function(){var e=$('<div id="app"></div>');$("body").append(e),new h.SmartReplenishmentDialog({el:e[0],propsData:{intelligentReplenishmentSwitch:this.model.get("intelligentReplenishmentSwitch")}})},_handleSwitchDistributionStock:function(e){var t,n=$(e.currentTarget);n.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("渠道库存管理开启后，不可关闭。")+"</p><br><p>"+$t("是否确认要开启渠道库存管理？")+"</p>",t=FS.crmUtil.confirm(e,$t("开启渠道库存管理"),function(){t.hide();FS.crmUtil.remind(1,$t("正在开启渠道库存管理..."),null,5e3),g.setSparePart.circle({key:"distribution_stock_switch",value:"2",token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(){n.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),FS.crmUtil.remind(1,$t("开启渠道库存管理成功"))}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后重试或联系纷享客服"),null,3e3)})}))},_handleUniqueProductCodeManagement:function(e){var t,n=$(e.currentTarget);n.hasClass("oprate-btn_switch--disabled")||(e="<p>"+$t("stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code_opened")+"</p><br><p>"+$t("stock.crm.setting.shiporder.turn_on_the_bar_code_of_FMCG_industry_tip")+"</p>",t=FS.crmUtil.confirm(e,$t("stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code"),function(){t.hide();FS.crmUtil.remind(1,$t("stock.crm.setting.shiporder.opening_FMCG_industry_commodity_bar_code"),null,5e3),g.setSparePart.circle({key:"unique_product_code_management",value:"2",token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(){n.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),FS.crmUtil.remind(1,$t("stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code_success"))}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后重试或联系纷享客服"),null,3e3)})}))},_handleSalesOrderAtoPtoSwitch:function(e){var t,n=this,i=$(e.currentTarget);i.hasClass("oprate-btn_switch--disabled")||(1==this.model.get("delivery_note_cpq_deliver_switch")?FS.crmUtil.remind(3,$t("该插件与CPQ组合发货插件互斥，请先关闭CPQ组合发货插件后重试。")):"2"===this.model.get("delivery_return_product_add_mode")?FS.crmUtil.remind(3,$t("stock.shiporder.ato_pto.notice")):"1"===this.model.get("order_freeze_inventory_adjustment")?FS.crmUtil.remind(3,$t("stock.crm.setting.shiporder.frozen_inventory_adjustment_function_has_been_enabled_not_ato")):(e="<p>"+$t("销售订单ATO&PTO组合发货，不可关闭。")+"</p><br><p>"+$t("是否确认要开启销售订单ATO&PTO组合发货？")+"</p>",t=FS.crmUtil.confirm(e,$t("销售订单ATO&PTO组合发货"),function(){t.hide(),n.model.set("showMask",!0);FS.crmUtil.remind(1,$t("正在开启销售订单ATO&PTO组合发货..."),null,5e3),g.setSparePart.circle({key:"sales_order_ato_pto_switch",value:"1",token:""},function(e,t){1==e.resultCode&&t(e)}).res(function(){FS.crmUtil.remind(1,$t("开启销售订单ATO&PTO组合发货成功")),i.addClass("oprate-btn_switch--on oprate-btn_switch--disabled"),n.model.set("showMask",!1)}).catch(function(e){FS.crmUtil.remind(3,e||$t("启用失败请稍后重试或联系纷享客服"),null,3e3),n.model.set("showMask",!1)})})))},_handleExchangeSetting:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=$('<div id="app"></div>');$("body").append(i),new u.ExchangeSettingDialog({el:i[0]})},_getSmartSettings:function(){var t=this;return(0,g.getSmartSettings)().then(function(e){e.isSuccess&&e.data&&e.data.intelligentReplenishmentSwitch&&t.model.set({intelligentReplenishmentSwitch:e.data.intelligentReplenishmentSwitch})})},destroy:function(){this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null}});t.Manage=e});
define("crm-setting/shiporder/shiporder/page/manage/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<div class='shiporder-manage_wrapper'> <!-- 开启 A类库存，或者开启 B类库存且开启渠道库存才显示销售订单的这个插件 --> <!-- 由于目前销售订单插件只有一个，所以该条件就应该加在销售订单这里，而不是具体的插件那 --> ";
            if (isForErp == false && stockSwitch == 2 || isForErp == true && distribution_stock_switch == 2) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("销售订单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("stock.shiporder.freeze_inventory_adjustment.title")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("stock.shiporder.freeze_inventory_adjustment.describe1")) == null ? "" : __t) + "</div> ";
                if (isForErp == true && distribution_stock_switch == 2) {
                    __p += " <div class='shiporder-manage_category-card-content'>" + ((__t = $t("stock.shiporder.freeze_inventory_adjustment.describe2")) == null ? "" : __t) + "</div> ";
                }
                __p += " <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-freeze-inventory-adjustment-switch " + ((__t = obj.order_freeze_inventory_adjustment == 1 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> </div> </div> ';
            }
            __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("发货单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("确认收货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("可设置系统自动确认收货，或手动收货时必须电子签名")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <!-- <div class=\"shiporder-manage_category-card-option j-detail\">" + ((__t = $t("详情")) == null ? "" : __t) + '</div> --> <div class="shiporder-manage_category-card-option ' + ((__t = obj.autoReceiveStatus == 1 ? "active j-setting-receive" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-autoreceive " + ((__t = obj.autoReceiveStatus == 1 ? "oprate-btn_switch--on" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
            if (obj.stockStatus === 2) {
                __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("函数校验实际库存-发货单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'> " + ((__t = $t("配置自定义函数，作为审批前置条件校验，避免实际库存不足导致发货单被作废")) == null ? "" : __t) + " </div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options j-cf-config kc-left'> <div class='shiporder-manage_category-card-option active' data-type='config'>" + ((__t = $t("配置函数")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-option active' data-type='flow'>" + ((__t = $t("配置流程")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-option active' data-type='guide'>" + ((__t = $t("查看指引")) == null ? "" : __t) + "</div> </div> </div> </div> ";
            }
            __p += " <!-- 890迭代需求：对于新租户，以及未开启CPQ组合发货插件的现有租户，均下架“CPQ组合发货”插件，前端不可见，不允许再开启。 --> ";
            if (!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise && obj.cpqDeliveryWay == 1) {
                __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>CPQ " + ((__t = $t("组合发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启CPQ组合发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-cpq-delivery-way " + ((__t = obj.cpqDeliveryWay == 1 ? "oprate-btn_switch--on" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
            }
            __p += " ";
            if (obj.hasMultiOrderDeliveryGray && !obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise) {
                __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("多订单发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("启用后，支持对同一个客户的多张订单同时发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-action " + ((__t = obj.multiOrderDeliverySwitch === 1 ? "oprate-btn_switch--on" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
            }
            __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("销售订单ATO&PTO组合发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，将根据订单产品上的BOM标识，来决定订单产品以ATO或者PTO的模式发货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-sales-order-ato-pto-switch " + ((__t = obj.sales_order_ato_pto_switch == 1 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + "\"></i> </div> </div> </div> </div> </div> <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("退货单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card j-card-return'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("基于发货单退货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，可基于发货单进行退货，退货单将同时与发货单、销售订单进行联动")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option " + ((__t = obj.delivery_note_returned_goods_switch == 2 ? "active j-setting-delivery_return_product_add_mode" : "") == null ? "" : __t) + '"> ' + ((__t = $t("参数设置")) == null ? "" : __t) + " </div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-delivery-note-return " + ((__t = obj.delivery_note_returned_goods_switch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + "\"></i> </div> </div> </div> <div class='shiporder-manage_category-card j-card-return'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("无源单退货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，可不基于任何单据进行退货，也可基于其他预设对象或者自定义对象进行退货（需自行添加相关对象的查找关联字段）")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-return " + ((__t = obj.non_note_returned_goods_switch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + "\"></i> </div> </div> </div> <div class='shiporder-manage_category-card j-card-return'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("换货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("退货单换货插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> ";
            if (!obj.isForErp && obj.stockStatus === 2) {
                __p += ' <div class="shiporder-manage_category-card-option ' + ((__t = obj.returned_goods_exchange_switch == 2 ? "active j-setting-exchange" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> ";
            }
            __p += " </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-exchange " + ((__t = obj.returned_goods_exchange_switch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> </div> </div> ';
            if (obj.isOpenWareHousePosition == 1) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("仓库")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("仓位")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启仓位管理")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-warehouse-position " + ((__t = obj.isOpenWareHousePosition == 1 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> </div> </div> ';
            }
            __p += " ";
            if (obj.isUpstreamEnterprise && obj.upstreamDisplayStockInterconnection || obj.isDownstreamEnterprise && obj.downstreamDisplayStockInterconnection) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title shiporder-manage_category__current-identity'> <span>" + ((__t = $t("互联插件")) == null ? "" : __t) + "</span> </div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("采购业务联动")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，上游的销售订单同步生成采购订单，上游的发货单确认收货后生成采购入库单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class='shiporder-manage_category-card-option active j-setting-interconnection'>" + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> ";
                if (obj.isDownstreamEnterprise && obj.downstreamDisplayStockInterconnection) {
                    __p += " <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i> ";
                } else if (obj.isUpstreamEnterprise && obj.upstreamDisplayStockInterconnection) {
                    __p += " <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i> ";
                }
                __p += " </div> </div> </div> <div class='shiporder-manage_category-card '> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("退货业务联动")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，可跨企业提交退货申请，上游审核通过后自动生成相应的采购退货单")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class='oprate-btn_switch oprate-btn_switch--on oprate-btn_switch--disabled'></i> </div> </div> </div> <!-- <div class=\"shiporder-manage_category-card \">--> <!-- <div class=\"shiporder-manage_category-card-title\">" + ((__t = $t("在途库存")) == null ? "" : __t) + '</div>--> <!-- <div class="shiporder-manage_category-card-title">' + ((__t = $t("stock.stock_manage.info.text6")) == null ? "" : __t) + '</div>--> <!-- <div class="shiporder-manage_category-card-content">' + ((__t = $t("开启后，将新增库存明细对象，可接收库存明细数据，并关联对应的库存记录")) == null ? "" : __t) + '</div>--> <!-- <div class="shiporder-manage_category-card-footer">--> <!-- <div class="shiporder-manage_category-card-options">--> <!-- </div>--> <!-- <div class="shiporder-manage_category-card-switcher">--> <!-- <i class="oprate-btn_switch oprate-btn_switch&#45;&#45;on oprate-btn_switch&#45;&#45;disabled"></i>--> <!-- </div>--> <!-- </div>--> <!-- </div>--> </div> </div> ';
            }
            __p += " ";
            if (+obj.stockSwitch !== 1 || !obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("行业插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> ";
                if (+obj.stockSwitch !== 1) {
                    __p += " <div class='shiporder-manage_category-card j-card-batch'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("批次与序列号")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启批次与序列号管理")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option " + ((__t = obj.stockSwitch == 2 && obj.batchSNSwitch == 2 ? "active j-setting-batch" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-batch " + ((__t = obj.batchSNSwitch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " ";
                if (!obj.isUpstreamEnterprise && !obj.isDownstreamEnterprise) {
                    __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("快消行业多单位")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("针对快消行业的特性做优化，同时支持多种单位输入数量，新增数量合计等")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-multi-switch " + ((__t = obj.multiSwitch == 1 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " <!-- 一物一码：由后端控制该插件是否展示 --> ";
                if (obj.unique_product_code_management_gray) {
                    __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("stock.crm.setting.shiporder.FMCG_industry_commodity_bar_code")) == null ? "" : __t) + "</div> <!-- <div class='shiporder-manage_category-card-content'>" + ((__t = $t("支持快消行业的商品条码管理")) == null ? "" : __t) + "</div>--> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("stock.stock_manage.info.text7")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-unique-product-code-management " + ((__t = obj.unique_product_code_management == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " <!-- 智能补货 active j-setting-batch 参数设置是否可点击 oprate-btn_switch--on 是否开启--> ";
                if (obj.intelligent_replenishment_switch) {
                    __p += " <div class='shiporder-manage_category-card j-card-batch'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("智能补货")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("根据销售、采购、库存、退货、换货等数据进行采购数量的预估")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option " + ((__t = obj.intelligentReplenishmentSwitch == 2 ? " active j-setting-smart" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-smart " + ((__t = obj.intelligentReplenishmentSwitch == 2 ? "oprate-btn_switch--on" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " </div> </div> ";
            }
            __p += " ";
            if (!obj.isForErp && obj.stockStatus === 2) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("库存插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("成本管理")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("stock.manage.cost.content")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option " + ((__t = obj.costAdjustmentEnableSwitch == 2 ? "active j-setting-cost-management" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-cost-adjustment " + ((__t = obj.costAdjustmentEnableSwitch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                if (obj.negative_inventory_allowed_plugin_switch) {
                    __p += " <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("负库存出库")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("启用负库存出库插件，并在仓库上设置允许负库存出库后，创建出库相关单据时，即使该仓库的实际库存为负，也允许出库")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-negative-inventory " + ((__t = obj.negativeInventorySwitch == "1" ? "oprate-btn_switch--on " : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("函数校验实际库存-调拨单等")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'> " + ((__t = $t("配置自定义函数，作为审批前置条件校验，避免实际库存不足导致库存相关业务单据被作废")) == null ? "" : __t) + "<br /> " + ((__t = $t("可配置单据包括：调拨单、出库单、采购退货单")) == null ? "" : __t) + " </div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options j-cf-config kc-left'> <div class='shiporder-manage_category-card-option active' data-type='config'>" + ((__t = $t("配置函数")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-option active' data-type='flow'>" + ((__t = $t("配置流程")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-option active' data-type='guideStock'>" + ((__t = $t("查看指引")) == null ? "" : __t) + "</div> </div> </div> </div> </div> </div> ";
            }
            __p += " ";
            if (obj.isForErp) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("对接插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> ";
                if (!obj.isShowRealTimeAvailableStock && obj.erp_real_time_inventory_gray) {
                    __p += " <div class='shiporder-manage_category-card j-card-receive'> <!-- <div class='shiporder-manage_category-card-title'>" + ((__t = $t("ERP实时库存")) == null ? "" : __t) + "</div>--> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("stock.stock_manage.info.text8")) == null ? "" : __t) + "</div> <!-- <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，可在库存对象、批次库存对象的列表页和详情页以及销售订单的新建页，获取ERP实时库存，并基于实时库存进行销售订单的相关校验。")) == null ? "" : __t) + "</div>--> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("stock.stock_manage.info.text9")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option shiporder-manage_category-card-option__j-switch-realtimestock " + ((__t = obj.erp_real_time_inventory.type != 0 ? "active j-setting-realtimestock" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-realtimestock " + ((__t = obj.erp_real_time_inventory.type != 0 ? "oprate-btn_switch--on" : "") == null ? "" : __t) + '"></i> </div> </div> </div> ';
                }
                __p += " <div class='shiporder-manage_category-card j-card-receive'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("金蝶")) == null ? "" : __t) + " K3C " + ((__t = $t("对接插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("开启后，将新增库存明细对象，可接收库存明细数据，并关联对应的库存记录")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> <div class=\"shiporder-manage_category-card-option shiporder-manage_category-card-option__k3c-param " + ((__t = obj.kingDeeK3CSyncPluginSwitch == 1 ? "active j-setting-k3c" : "") == null ? "" : __t) + '">' + ((__t = $t("参数设置")) == null ? "" : __t) + "</div> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-k3c " + ((__t = obj.kingDeeK3CSyncPluginSwitch == 1 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> </div> </div> ';
            }
            __p += " <!-- A类库存和B类库存均可见渠道插件 --> ";
            if (obj.isForErp == false && obj.stockSwitch == 2 || obj.isForErp == true) {
                __p += " <div class='shiporder-manage_category'> <div class='shiporder-manage_category-title'>" + ((__t = $t("渠道插件")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-cards'> <div class='shiporder-manage_category-card'> <div class='shiporder-manage_category-card-title'>" + ((__t = $t("渠道库存管理")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-content'>" + ((__t = $t("管理经销商的库存，对经销商的业务进行赋能")) == null ? "" : __t) + "</div> <div class='shiporder-manage_category-card-footer'> <div class='shiporder-manage_category-card-options'> </div> <div class='shiporder-manage_category-card-switcher'> <i class=\"oprate-btn_switch j-switch-distribution-stock " + ((__t = obj.distribution_stock_switch == 2 ? "oprate-btn_switch--on oprate-btn_switch--disabled" : "") == null ? "" : __t) + '"></i> </div> </div> </div> </div> </div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/mask/index",["./tpl-html"],function(e,i,t){t.exports=Backbone.View.extend({template:e("./tpl-html"),initialize:function(){this.name=this.options.name},render:function(){var e=this.model.get("showMask"),i=this.model.get("maskTips"),t=$(".b-g-con");e?t.find(".j-shiporder-mask").length?t.find(".j-shiporder-mask").show():t.append(this.template({tips:i})):t.find(".j-shiporder-mask").hide()},destroy:function(){this.model=null,this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null}})});
define("crm-setting/shiporder/shiporder/page/mask/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-shiporder-mask j-shiporder-mask"> <div class="crm-shiporder-mask_wrapper"> <div class="crm-shiporder-mask_content"> <div class="crm-shiporder-mask_loading"></div> <p class="crm-shiporder-mask_tips">' + ((__t = obj.tips) == null ? "" : __t) + "</p> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/none/none",["../../view/view","./page"],function(e,n,r){var i=e("../../view/view"),t=e("./page");return i.extend({getCurPage:function(){return t}})});
define("crm-setting/shiporder/shiporder/page/none/options/index",["./tpl-html"],function(e,t,n){n.exports=Backbone.View.extend({template:e("./tpl-html"),events:{"click .j-back":"_handleGoback","click .j-shiporder-checkbox":"_handleCheckBox","click .j-confirm":"_handleConfirm"},initialize:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.doStockType=e.value,this.onBack=e.onBack||$.noop,this.onConfirm=e.onConfirm||$.noop},data:{delivery:!0,stock:!1,purchase:!1,returned:!1},render:function(){var e=_.extend({},this.model.toJSON(),this.data,{doStockType:this.doStockType});this.$el.html(this.template(e))},_handleGoback:function(){this.onBack()},_handleCheckBox:function(e){var t,e=$(e.currentTarget),n=e.data("name"),e=e.is(":checked");this.data[n]=e,this.data.stock||-1!==["purchase","returned"].indexOf(n)&&e&&(this.data.stock=!0,$(".j-shiporder-checkbox[data-name='stock']").attr("checked",!0)),"stock"!==n||e||(t=this,["purchase","returned"].forEach(function(e){t.data[e]=!1,$(".j-shiporder-checkbox[data-name='"+e+"']").attr("checked",!1)}))},_handleConfirm:function(e){var e=$(e.currentTarget),t={enableDeliveryNote:this.data.delivery,enableStock:this.data.stock,enablePurchase:this.data.purchase,enableReturnExchange:this.data.returned};this.onConfirm(t,e)},destroy:function(){this.model=null,this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null}})});
define("crm-setting/shiporder/shiporder/page/none/options/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-setting_wrapper"> <!-- <div class="shiporder-setting_intr"> <p class="shiporder-setting_intr-title">说明:</p> <ul> <li class="shiporder-setting_intr-tips">1. 订单审核后，可新建发货单，可维护发货产品和数量，支持分批发货。</li> <li class="shiporder-setting_intr-tips">2. 单独开启发货单，未开启库存的情况下，创建发货单不会校验及扣减库存。</li> <li class="shiporder-setting_intr-tips">3. 同时开启了发货单和库存的情况下，创建发货单时会校验发货仓库的实际库存，发货单确认后会自动创建出库单，并扣减发货仓库的实际库存。</li> <li class="shiporder-setting_intr-tips">4. 发货单一旦开启，不可关闭。</li> <li class="shiporder-setting_intr-tips">5. 为保证发货单功能的正常使用，请将移动客户端的版本升级至6.3及以上。</li> </ul> </div> --> <div class="shiporder-setting_options"> <p class="shiporder-setting_options-title">' + ((__t = (Number(obj.doStockType) === 0 ? $t("完整版纷享库存") : $t("对接版纷享库存")) + $t("，请勾选您需要开启的功能")) == null ? "" : __t) + '</p> <div class="shiporder-setting_options-container"> <div class="shiporder-setting_options-item"> <div class="shiporder-checkbox"> <input class="j-shiporder-checkbox checkbox-disabled" type="checkbox" data-name="delivery" checked disabled> <span>' + ((__t = $t("发货单")) == null ? "" : __t) + '</span> </div> <p class="shiporder-setting_options-tips">' + ((__t = $t("开启后，会初始化发货单对象")) == null ? "" : __t) + '</p> </div> </div> <div class="shiporder-setting_options-container"> <div class="shiporder-setting_options-item"> <div class="shiporder-checkbox"> <input class="j-shiporder-checkbox" ' + ((__t = obj.stock ? "checked" : null) == null ? "" : __t) + ' type="checkbox" data-name="stock"> <span>' + ((__t = $t("库存")) == null ? "" : __t) + "</span> </div> ";
            if (Number(obj.doStockType) === 0) {
                __p += ' <p class="shiporder-setting_options-tips">' + ((__t = $t("开启后，会初始化仓库、库存、入库单、出库单、调拨单、盘点单、出入库明细7个对象")) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <p class="shiporder-setting_options-tips">' + ((__t = $t("开启后，会初始化仓库、库存两个对象")) == null ? "" : __t) + "</p> ";
            }
            __p += " </div> ";
            if (Number(obj.doStockType) === 0) {
                __p += ' <div class="shiporder-setting_options-item child-option"> <div class="shiporder-checkbox"> <input class="j-shiporder-checkbox" ' + ((__t = obj.purchase ? "checked" : null) == null ? "" : __t) + ' type="checkbox" data-name="purchase"> <span>' + ((__t = $t("采购")) == null ? "" : __t) + '</span> </div> <p class="shiporder-setting_options-tips">' + ((__t = $t("开启后，会初始化供应商、采购订单两个对象")) == null ? "" : __t) + "</p> </div> ";
            }
            __p += ' <div class="shiporder-setting_options-item child-option" style="display: none;"> <div class="shiporder-checkbox"> <input class="j-shiporder-checkbox" ' + ((__t = obj.returned ? "checked" : null) == null ? "" : __t) + ' type="checkbox" data-name="returned"> <span>' + ((__t = $t("退换货")) == null ? "" : __t) + '</span> </div> <p class="shiporder-setting_options-tips">$t("开启后，会初始化退换货单、退款单两个对象")</p> </div> </div> </div> <div class="shiporder-setting_btns"> <div class="shiporder-setting_btn-back j-back">' + ((__t = $t("返回上一步")) == null ? "" : __t) + '</div> <div class="shiporder-setting_btn-confirm crm-btn-primary j-confirm">' + ((__t = $t("确认开通")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/page/none/page",[],function(e,t,n){n.exports=Backbone.View.extend({initialize:function(){this.selectType=0},render:function(){this.renderSelect()},renderPage:function(){1===this.stockStep?this.renderSelect():this.renderOptions()},renderSelect:function(){var t=this,n=this,i=this.model.get("hasStockPackage")?{el:$(".j-stock-con"),value:this.selectType,title:$t("请根据贵企业的业务需求，选择开启相应版本的库存"),options:[{title:$t("完整版"),icon:CRMSETTING.ASSETS_PATH+"/images/stock/icon-service-90853d64d6.png",tips:[$t("未使用第三方（金蝶、用友、SAP）ERP系统；"),$t("只需要管理常规的库存业务，例如库存的出入库、调拨、盘点、批次/序列号、查询明细等；"),$t("不需要对库存进行复杂的财务管理；")],value:0},{title:$t("对接版"),icon:CRMSETTING.ASSETS_PATH+"/images/stock/icon-erp-666c624ac3.png",tips:[$t("已有自研ERP或第三方（金蝶、用友、SAP）ERP系统；"),$t("库存的精细化管理程度高，例如会有单据红字冲正、实际库存为负数等；"),$t("需要对库存进行成本核算及财务管理；")],value:1}],onConfirm:function(e){n.selectType=+e,n.renderOptions()}}:{el:$(".j-stock-con"),value:this.selectType,options:[{title:$t("发货单"),icon:CRMSETTING.ASSETS_PATH+"/images/stock/icon-erp-666c624ac3.png",tips:[$t("发货后可进行物流进度查询，支持市面上所有主流物流公司；"),$t("支持一张发货单对多张订单合并发货；"),$t("支持对一张订单进行分批发货，支持进行收货确认，并更新相应订单的发货信息；")],value:0}],btnLabel:$t("开启"),onConfirm:function(e,t){n.selectType=+e,n.initShipOrder({enableDeliveryNote:!0,enableStock:!1,enablePurchase:!1,enableReturnExchange:!1,isForErp:1===n.selectType},t)}};e.async("./select/index",function(e){t.page&&t.page.destroy();e=new e(i);e.render(),t.page=e})},renderOptions:function(){var n=this;e.async("./options/index",function(e){n.page&&n.page.destroy(),n.page=new e({el:$(".j-stock-con"),model:n.model,value:n.selectType,onBack:function(){n.renderSelect()},onConfirm:function(e,t){n.initShipOrder(_.extend({},e,{isForErp:1===n.selectType}),t)}}),n.page.render()})},initShipOrder:function(e,t){var n=this;this.model.set("showMask",!0),this.model.initShipOrder(e,t).then(function(){n.model.set("showMask",!1),n.model.reload()},function(e){n.model.set("showMask",!1);e=e&&e.errMsg||$t("启用失败请稍后重试或联系纷享客服");FS.crmUtil.alert(e)})},destroy:function(){this.page&&this.page.destroy(),this.page=null,this.remove()}})});
define("crm-setting/shiporder/shiporder/page/none/select/index",["./tpl-html"],function(t,e,i){i.exports=Backbone.View.extend({template:t("./tpl-html"),events:{"click .shiporder-intr_selection-item":"_handleSelect","click .shiporder-intr_btn-next":"_handleNext"},initialize:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.attrData=_.extend({title:"",options:[],btnLabel:$t("下一步")},_.pick(t,"title","options","btnLabel")),this.value=t.value,this.onConfirm=t.onConfirm||$.noop},render:function(){this.$el.html(this.template(_.extend({value:this.value},this.attrData)))},_handleSelect:function(t){var t=$(t.currentTarget),e=String(t.attr("data-value"));e!==String(this.value)&&(this.value=e,this.$(".shiporder-intr_selection-item").removeClass("active"),t.addClass("active"))},_handleNext:function(){this.onConfirm(this.value,this.$el.find(".shiporder-intr_btn-next"))},destroy:function(){this.model=null,this.$el.off(),this.$el.empty(),this.$el=this.el=this.options=null}})});
define("crm-setting/shiporder/shiporder/page/none/select/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="shiporder-intr_wrapper"> <p class="shiporder-intr_tips">' + ((__t = obj.title) == null ? "" : __t) + '</p> <div class="shiporder-intr_content"> <div class="shiporder-intr_selection"> ';
            _.each(obj.options, function(item) {
                __p += ' <div class="shiporder-intr_selection-item ' + ((__t = obj.value == item.value ? "active" : "") == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '"> <div class="shiporder-intr_selection-item-icon"> <img src="' + ((__t = item.icon) == null ? "" : __t) + '" alt=""> </div> <div class="shiporder-intr_selection-item-content"> <p class="shiporder-intr_selection-item-title">' + ((__t = item.title) == null ? "" : __t) + '</p> <ul class="shiporder-intr_selection-item-tips"> ';
                _.each(item.tips, function(tip) {
                    __p += " <li>" + ((__t = tip) == null ? "" : __t) + "</li> ";
                });
                __p += " </ul> </div> </div> ";
            });
            __p += ' </div> </div> <button class="shiporder-intr_btn-next">' + __e(obj.btnLabel) + "</button> </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/view/tabs/tabs",["./tpl-html"],function(t,e,s){s.exports=Backbone.View.extend({template:t("./tpl-html"),options:{tabs:[],curTab:0},events:{"click .j-tab":"selectHandle"},render:function(){this.$el.html(this.template(this.options))},selectHandle:function(t){var t=$(t.currentTarget),e=+t.attr("data-tab");e!==this.options.curTab&&(this.setValue(e,t),this.trigger("change",e))},setValue:function(t,e){(e||this.$("[data-tab="+t+"]")).addClass("cur").siblings(".cur").removeClass("cur"),this.options.curTab=t},destroy:function(){this.$el.remove()}})});
define("crm-setting/shiporder/shiporder/view/tabs/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tab"> ';
            _.each(obj.tabs, function(tab, index) {
                __p += ' <span class="j-tab ' + ((__t = index === obj.curTab ? "cur" : "") == null ? "" : __t) + '" data-tab="' + ((__t = index) == null ? "" : __t) + '">' + __e(tab) + "</span> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/view/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (!_.isEmpty(obj.tabs)) {
                __p += ' <div class="j-crm-tabs"></div> ';
            }
            __p += ' <div class="shiporder-con crm-scroll j-container ' + ((__t = _.isEmpty(obj.tabs) ? "" : "shiporder-con_hastab") == null ? "" : __t) + '"> </div>';
        }
        return __p;
    };
});
define("crm-setting/shiporder/shiporder/view/view",["./tabs/tabs","./tpl-html"],function(t,e,s){var i=t("./tabs/tabs"),n=Backbone.View.extend({template:t("./tpl-html"),options:{title:$t("stock.stock_management.stock_management"),titleLabel:"",tabs:[],curTab:0},initialize:function(t){this.super=n.prototype,this.model=this.options.model,this.options=_.extend({},this.super.options,this.options,t),this.options.tabs=this.formatTabs(this.options.tabs),this.setElement(t.wrapper),this.widgets={}},render:function(){var t=this.options;this.model.set({title:t.title,titleLabel:t.titleLabel}),this.$el.html(this.template({tabs:t.tabs})),this.$container=this.$(".j-container"),this.initTabs(),this.renderTpl()},formatTabs:function(t){return _.isEmpty(t)||!_.isArray(t)||t.length<2?[]:t},initTabs:function(){var e=this,t=this.options,s=t.getCurTab?t.getCurTab():t.curTab||0;this.options.curTab=s,this.widgets.tabs&&this.widgets.tabs.destroy(),this.widgets.tabs=new i({el:e.$(".j-crm-tabs").get(0),curTab:s,tabs:t.tabs}),this.widgets.tabs.render(),this.widgets.tabs.on("change",function(t){e.options.curTab=t,e.renderTpl()})},renderTpl:function(){var t=this.getCurPage();this.widgets.page&&this.widgets.page.destroy(),this.widgets.page=new t({el:this.$container.get(0),model:this.model}),this.widgets.page.render()},getCurPage:function(){},destroy:function(){this.$el.off(),this.$el.empty(),_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),this.widgets=null,this.remove()}});s.exports=n});
define("crm-setting/shiporder/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2 class="crm-s-shiporder_title"> <span class="tit-txt j-setting-title">' + ((__t = obj.title) == null ? "" : __t) + '</span> <span class="crm-s-shiporder_titlelabel j-setting-titlelabel ' + ((__t = obj.titleLabel ? "" : "crm-s-shiporder_hide") == null ? "" : __t) + '">' + ((__t = obj.titleLabel) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con j-stock-con"> <div class="crm-loading tab-loading"></div> </div>';
        }
        return __p;
    };
});