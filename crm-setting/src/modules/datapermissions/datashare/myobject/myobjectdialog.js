/**
 * 支持基于条件的共享规则弹窗
 */
define(function (require, exports, module) {
    var util = require("crm-modules/common/util");
    var Dialog = require("crm-widget/dialog/dialog");
    var Selector = require("crm-widget/selector/selector");
    var Select = require("crm-widget/select/select");
    var Fieldfilter = require("crm-modules/common/fieldfilter/fieldfilter");
    var mainHtml = require("../template/add-dialog-html");
    var Config = require("../../common/config");
    var COMPANY_ID = 999999;

    var BaseDataShare = Dialog.extend({
        config: "myObjectByField",

        attrs: {
            title: $t("共享规则"),
            content: '<div class="crm-loading"></div>',
            className: "crm-s-datapermissions",
            showBtns: true,
            showScroll: true,
            isCopy: false,
            // size: "md",
            type: "add",
            data: {},
            width: "810px",
        },
        events: {
            // "click .update-tip .title": "onToggleWarn",
            "click .j-toggle": "onToggleCondition",
            "focus .rulename-ipt": "onRemoveErrmsg",
            "click .b-g-btn": "onSave",
            "click .add-group-btn": "addFilterGroup",
            "click .j-del-group": "delFilterGroup",
            "click .b-g-btn-cancel": "destroy",
            "click .j-permission": "onTogglePerMission",
        },
        render: function () {
            this.widgets = {};
            this.myObjectDataPermissions = JSON.parse(
                sessionStorage.getItem("crmDataPermissions")
            ).myObjectDataPermissions;
            this.myObjectDataPermissions = this.myObjectDataPermissions.filter(
                function (item) {
                    return item.objectType != 1;
                }
            );

            this.userGroups = JSON.parse(sessionStorage.getItem("userGroups"));
            return BaseDataShare.superclass.render.call(this);
        },

        show: function (opts) {
            this.widgets = {};
            this.set(opts);
            var result = BaseDataShare.superclass.show.call(this);
            var isEdit = this.get("type") === "edit";
            var isCopy = this.get("isCopy");
            var isDatasource =
                ["datasource", "datasourcedep", "datasourcedep_org"].indexOf(
                    this.get("scopeType")
                ) > -1;
            if (!isEdit && !this.get("scopeType")) {
                isDatasource = true;
            }
            var isDep = this.get("scopeType") == "datasourcedep";
            var isDepOrg = this.get("scopeType") == "datasourcedep_org";

            this.curType = "datasource";
            if (isDep) this.curType = "datasourcedep";
            if (isDepOrg) this.curType = "datasourcedep_org";
            if (!isDatasource) this.curType = "field";

            this.setContent(
                mainHtml({
                    isEdit: isEdit,
                    isDatasource: isDatasource,
                    isDep: isDep,
                    isDepOrg: isDepOrg,
                    data: this.get("data"),
                    showField:
                        this.get("showField") &&
                        opts.data.ObjectType != "BpmInstance" &&
                        opts.data.ObjectType != "ApprovalInstanceObj", // 去除bpm实例对象 流程实例
                    isSysObj: false,
                    disabled: this.get("disabled"),
                    isCopy: isCopy,
                })
            );
            this.$sharedata = this.$(".j-sharedata");
            this.$rulename = this.$(".rulename-ipt");
            this.$shareobj = this.$(".j-shareobj");
            this.$fieldfilter = this.$(".j-fieldfilter");
            this.$conditionGroups = this.$(".condition-groups");

            this.initSelectBar("datasource", isEdit || this.get("disabled"));
            this.initSelectBar(
                "target",
                (isEdit && isDatasource) || this.get("disabled")
            );
            this.initShareData(isEdit && isDatasource);
            if (this.get("scopeType") === "field" || (!isEdit && !isCopy)) {
                this.initShareObj(isEdit);
            }
            this.resizedialog();

            return result;
        },

        curType: "datasource",

        // 数据来源于、数据共享到
        initSelectBar: function (type, disabled, rangeType) {
            var me = this;
            var data = me.get("data");
            var isDataEmp =
                (me.get("scopeType") === "datasource" ||
                    me.curType == "datasource") &&
                type != "target";
            var isDatasource =
                me.get("scopeType") === "datasource" ||
                me.get("scopeType") === "datasourcedep" ||
                me.get("scopeType") === "datasourcedep_org" ||
                me.curType == "datasource" ||
                me.curType == "datasourcedep" ||
                me.curType == "datasourcedep_org";
            var isDep =
                (me.get("scopeType") === "datasourcedep" ||
                    me.curType == "datasourcedep") &&
                type != "target";
            var isDepOrg =
                (me.get("scopeType") === "datasourcedep_org" ||
                    me.curType == "datasourcedep_org") &&
                type != "target";
            var defaultSelectedData;
            var typeArr = me.get(type);
            var isCopy = this.get("isCopy");

            // 判断用户组是否在用户组列表中
            if (
                type === "target" &&
                typeArr &&
                typeArr[0].type === "usergroup"
            ) {
                // TODO: SelectBar中用户组的type为0，所以先这么写。。
                defaultSelectedData = _.findWhere(me.userGroups, {
                    id: typeArr[0].id,
                    type: 0,
                })
                    ? typeArr
                    : [];
            } else {
                defaultSelectedData = typeArr || [];
                if (data.DataSourceStatus == 1) {
                    //停用
                    defaultSelectedData.forEach(function (item) {
                        if (item.type == "member") item.type = "stop";
                    });
                }
            }

            // 设置是否显示包含子部门
            var groupIncludeChildrenStatus = 0;
            // “数据共享到”
            if (type === "target") {
                groupIncludeChildrenStatus = 1;
                if (this.get("type") == "edit" || isCopy) {
                    //编辑、查看
                    var receiveCascade;
                    if (isDatasource) {
                        receiveCascade = data.receiveDeptCascade;
                    } else {
                        if (data.receives[0]) {
                            receiveCascade = data.receives[0].receiveCascade;
                        }
                    }
                    groupIncludeChildrenStatus = receiveCascade == 1 ? 2 : 1;
                }
            } else if (type === "datasource") {
                // if(me.get("scopeType") === "datasource" ||me.curType == "datasource"){
                // 	groupIncludeChildrenStatus = 1;
                // 	//todo: 初始化时如何设置????
                // }
            }

            var isGroup = isDep || isDepOrg;

            let stopDepartmentData=[];
            if(!isDatasource){
                defaultSelectedData.forEach(function (item) {
                    let id = item.id;
                    if (item.type == "group") {
                        if(!Fx.contacts.getCircleById(id)){
                            Fx.contacts.getCircleByIdSync(id,function(dep){
                                if(Fx.contacts.isStop(dep)){
                                    stopDepartmentData.push(dep)
                                }
                            });
                            
                        }
                    } 
                })
            }

            var widget = new Selector({
                $wrap: me.$(".j-" + type),
                zIndex: me.get("zIndex"),
                group: {
                    company: type === "datasource" || isGroup,
                    chooseType: isDep
                        ? "department"
                        : isDepOrg
                        ? "organization"
                        : "",
                }, // 被共享方不含全公司
                member: isGroup ? false : true,
                usergroup: isGroup ? false : true,
                role: isGroup ? false : true,
                stop: isDataEmp? true : !isDatasource ? {hidden:true} : false,
                stopDepartment:!isDatasource ? {hidden:true,data:stopDepartmentData} : false,
                excludeItems: {
                    role: ["personnelrole"], //屏蔽所有员工,暂不支持
                },
                groupIncludeChildrenStatus: groupIncludeChildrenStatus,
                single: false,
                label: isGroup
                    ? isDep
                        ? $t("xuanzeshujuguishubumen")
                        : $t("xuanzeshujuguishuzuzhi")
                    : $t("选择员工部门用户组或角色"),
                defaultSelectedItems: (function () {
                    var data = {
                        member: [],
                        group: [],
                        usergroup: [],
                        role: [],
                        stop: [],
                        stopDepartment:[],
                    };
                    var id;
                    var type;
                    _.each(defaultSelectedData, function (item) {
                        id = item.id;
                        type = item.type;
                        if (type == "member") {
                            if(!isDatasource && !Fx.contacts.getEmployeeById(id)){
                                let emp = Fx.contacts.getEmployeeById(id,{includeStop:true,includeStopByRequest:true});
                                if(Fx.contacts.isStop(emp)){
                                    data.stop.push(id);
                                    return;
                                }
                            }
                            data.member.push(id);
                        } else if (type == "group") {
                            if(!isDatasource && stopDepartmentData.length){
                                if(stopDepartmentData.findIndex(function(dep){return dep.id==id})!=-1){
                                    data.stopDepartment.push(id);
                                    return; 
                                }
                                
                            }
                            data.group.push(id);
                        } else if (type === "usergroup") {
                            data.usergroup.push(id);
                        } else if (type === "role") {
                            data.role.push(id);
                        } else if (type === "stop") {
                            data.stop.push(id);
                        } else if (type === "stopDepartment") {
                            data.stopDepartment.push(id);
                        }
                    });
                    return data;
                })(),
                isFromManage: true,
                enableScope: true,
            });

            if (disabled) {
                widget.lock();
            }

            widget.on("addItem", function () {
                this.options.$wrap.next().hide();
                me.resizedialog();
            });

            me.widgets[type] = widget;
        },

        // 共享的数据
        initShareData: function (disabled) {
            var me = this;
            var data = me.get("data");
            var ObjectType = data.ObjectType || data.ObjectDescribeApiName;

            ObjectType = ObjectType === -1 ? "" : ObjectType;

            var options = me.myObjectDataPermissions.reduce(function (
                list,
                item
            ) {
                list.push({
                    name: item.ObjectDescribeDisplayName,
                    value: item.ObjectDescribeApiName,
                });
                return list;
            },
            []);

            // if (data.ObjectType && data.ObjectType == "-2") {
            // 	// 全部预置对象
            // 	options = _.filter(options, function (item) {
            // 		return item.value && item.value.indexOf("__c") == -1;
            // 	});
            // }

            // if (data.ObjectType && data.ObjectType == "-1") {
            // 	// 全部自定义对象
            // 	options = _.filter(options, function (item) {
            // 		return item.value && item.value.indexOf("__c") != -1;
            // 	});
            // }

            var widget = new Select({
                $wrap: me.$sharedata,
                zIndex: me.get("zIndex"),
                width: 500,
                multiple: "multiple",
                allCheck: true,
                options: options,
                disabled: disabled,
                stopPropagation: true,
                defaultValue: ObjectType,
            });
            widget.on("change", function () {
                this.options.$wrap.next().hide();
                me.resizedialog();
            });
            me.widgets["sharedata"] = widget;
        },

        // 共享的对象
        initShareObj: function (isEdit) {
            var me = this;
            var data = me.get("data");
            var entityId = me.get("data").entityId;
            var options = me.myObjectDataPermissions
                .map((item) => {
                    return {
                        name: item.ObjectDescribeDisplayName,
                        value: item.ObjectDescribeApiName,
                    };
                })
                .filter((item) => {
                    // 屏蔽 联系人、订单产品、退货单，退货单产品,目标值, 如果想支持某对象, 请和相关产品以及测试沟通后放开.
                    if (
                        [
                            "SalesOrderProductObj",
                            "ReturnedGoodsInvoiceProductObj",
                            "GoalValueObj",
                        ].indexOf(item.value) != -1
                    ) {
                        return false;
                    }
                    return true;
                });

            options = _.filter(options, function (item) {
                return (
                    item.value != "BpmInstance" &&
                    item.value != "ApprovalInstanceObj"
                );
            });

            // 公共对象屏蔽条件共享规则新增
            me.publicObjectBlocking(function (res) {
                if (res && res.publicObject.length > 0) {
                    // 循环过滤指定对象
                    res.publicObject.forEach(function (resItem) {
                        options = _.filter(options, function (item) {
                            return item.value != resItem;
                        });
                    });
				}
				
                var isCopy = me.get("isCopy");
                if (isCopy) {
                    isEdit = true;
                }

                // if (data.ObjectType && data.ObjectType == "-2") {
                // 	// 全部预置对象
                // 	options = _.filter(options, function (item) {
                // 		return item.value && item.value.indexOf("__c") == -1;
                // 	});
                // }

                // if (data.ObjectType && data.ObjectType == "-1") {
                // 	// 全部自定义对象
                // 	options = _.filter(options, function (item) {
                // 		return item.value && item.value.indexOf("__c") != -1;
                // 	});
                // }

                var widget = new Select({
                    $wrap: me.$shareobj,
                    zIndex: me.get("zIndex"),
                    multiple: "single",
                    options: [
                        {
                            name: $t("请选择"),
                            value: "",
                        },
                    ].concat(options),
                    defaultValue: entityId,
                    disabled: isEdit && !isCopy,
                });
                widget.on("change", function (value) {
                    this.options.$wrap.next().hide();

                    me.renderFieldfilter({
                        apiname: value,
                    });

                    me.resizedialog();

                    if (value == "AccountObj") {
                        //客户
                        this.options.$wrap.next().next().show();
                    } else {
                        this.options.$wrap.next().next().hide();
                    }
                });
                isEdit &&
                    me.renderFieldfilter({
                        apiname: entityId,
                        isEdit: isEdit,
                    });
                me.widgets["shareobj"] = widget;
            });
        },
        // 新增公共对象屏蔽
        publicObjectBlocking: function (cb) {
            util.FHHApi({
                url: "/EM1HNCRM/API/v1/object/data_privilege/service/getDescribeListByObjectType",
                data: {},
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        cb && cb(res.Value);
                    } else {
                        cb && cb();
                    }
                },
            });
        },
        renderFieldfilter: function (data) {
            var me = this;
            var isEdit = data.isEdit;
            delete data.isEdit;
            (me.widgets.filterGroup || []).forEach(function (item) {
                item.destroy();
                me.$conditionGroups.html("");
                me.$fieldfilter._hasAddBtn = 0;
                me.$fieldfilter.find(".add-group-btn").remove();
            });
            me.widgets.filterGroup = [];
            if (isEdit) {
                var ruleParse = me.get("data").ruleParse || ""; //"( (1 and 2) or (3 and 4) )"
                var rules = me.get("data").rules || [];
                var arr = ruleParse.replace(/[() ]/g, "").split("or");
                var rulesGroup = [];
                arr.forEach(function (v) {
                    var t = [];
                    v.split("and").forEach(function (v2) {
                        rules.forEach(function (v3) {
                            // 2022年8月11日 修补保存【合作伙伴】字段时filedName的fix，以支持反显 by:mulianju
                            if (v3.fieldName == "partner_id.name") {
                                v3.fieldName = "partner_id";
                                v3.fieldValue = [v3.fieldValue.join(";")];
                            }
                            if (v3.ruleOrder == v2) {
                                t.push(v3);
                            }
                        });
                    });
                    rulesGroup.push(t);
                });
                rulesGroup.forEach(function (r) {
                    me.widgets.filterGroup.push(me.createFieldfilter(data, r));
                });
            } else {
                me.widgets.filterGroup.push(me.createFieldfilter(data));
            }
        },
        createFieldfilter: function (data, rules) {
            var me = this;
            var uuid = Date.now() + "-" + Math.random();

            var $wrapper = $(
                '<div class="filter-item filter-item-' + uuid + '"></div>'
            );
            var employeeTypesArr = [
                "employee",
                "employee_many",
                "department",
                "department_many",
            ];
            // crm\crm2\modules\common\fieldfilter\fieldfilter.js
            // crm\crm2\modules\common\filter\filter.js
            var defV,
                disabledV = [];
            if (rules) {
                defV = _.map(rules, function (item) {
                    if (["IS", "ISN"].indexOf(item.operate) > -1) {
                        disabledV.push([undefined, undefined, true]);
                    } else {
                        disabledV.push([]);
                    }
                    return [item.fieldName, item.operate, item.fieldValue];
                });
            }
            var filter = new Fieldfilter(
                _.extend(data, {
                    uuid: uuid,
                    $wrapper: $wrapper,
                    width: 636,
                    // title: '<span class="del-span j-delete-filter"></span><span style="color:#919eab">' + $t("且（AND）") + '</span>',
                    title:
                        '<span class="el-icon-remove j-del-group" data-uuid=' +
                        uuid +
                        "></span>" +
                        $t("且（AND）"),
                    max: 10,
                    filterType: Config.filterType,
                    filterApiname: Config.filterApiname,
                    openQuoteField: true,
                    openPartnerIdField: true,
                    extra: { from: "datashare" },
                    disabledValue: disabledV,
                    defaultValue: defV,
                    parseCompare: function (compare, type, conf) {
                        if (
                            _.contains(employeeTypesArr, type) ||
                            type == "dimension"
                        ) {
                            return _.map([13, 14, 9, 10], function (item) {
                                return conf[item - 1];
                            });
                        }

                        if (
                            ["count", "number", "currency"].indexOf(type) > -1
                        ) {
                            compare.push(conf[8]);
                            compare.push(conf[9]);
                        } else if (type == "rich_text") {
                            compare.push(conf[6], conf[7]);
                        } else if (
                            ["country", "province", "city", "district"].indexOf(
                                type
                            ) > -1
                        ) {
                            compare.push(conf[12], conf[13]);
                        }
                        return compare;
                    },
                    _getCompareOptions: function (field) {
                        var me = this;
                        // crm/modules/common/fieldfilter/helper.js
                        var compares = this.opts.helper.getCompare(field.type);
                        compares = me.opts.parseCompare(
                            compares,
                            field && field.type,
                            me.opts.helper.getCompareConfig()
                        );
                        var type = this.opts.helper.getType(
                            field.type,
                            me.opts.isRelate
                        );

                        // 2022年8月2日 “合作伙伴”字段修复为text类型 by:mulianju
                        if (
                            _.contains(
                                ["object_reference", "object_reference_many"],
                                field.type
                            ) &&
                            field.api_name == "partner_id"
                        ) {
                            type = "text";
                        }

                        //创建人、最后修改人、跟进人特殊处理
                        if (me.isEmpField(field.api_name)) {
                            type = "employee";
                        }
                        //老对象不支持包含、不包含、属于、不属于
                        if (CRM.config.objDes[me.opts.apiname.toLowerCase()]) {
                            // if (_.contains(['employee', 'select_many'], type)) {
                            //条件共享 -- 人员字段支持指定人员
                            if (type == "select_many") {
                                compares = _.filter(
                                    compares,
                                    function (compare) {
                                        return !_.contains(
                                            [7, 8, 13, 14],
                                            compare.value
                                        );
                                    }
                                );
                            } else if (
                                _.contains(
                                    ["date", "time", "date_time"],
                                    field.type
                                )
                            ) {
                                compares = _.filter(
                                    compares,
                                    function (compare) {
                                        return !_.contains(
                                            [9, 10],
                                            compare.value
                                        );
                                    }
                                );
                            }
                        }

                        let stopDepartmentData=[];

                        // todo: 部门 开启 包含子部门
                        var groupIncludeChildrenStatus = 0,
                            groupIncludeChildrenStatusCascade = false;
                        var valSubfix = "";
                        //归属部门显示包含子部门勾选, type == 'department'
                        if (field.api_name == "data_own_department") {
                            groupIncludeChildrenStatus = 1;
                            groupIncludeChildrenStatusCascade = true;
                            valSubfix = "_y";
                            this.opts.defaultValue &&
                                this.opts.defaultValue.forEach((item) => {
                                    if (item[0] == "data_own_department") {
                                        if (
                                            Array.isArray(item[2]) &&
                                            /_y$/g.test(item[2][0])
                                        ) {
                                            //包含子部门
                                            item[2] = item[2].map((val) => {
                                                return val.replace("_y", "");
                                            });
                                            groupIncludeChildrenStatus = 2;
                                        } else {
                                            groupIncludeChildrenStatus = 1;
                                        }
                                    }
                                });
                        } else if (field.api_name == "data_own_organization") {
                            groupIncludeChildrenStatus = 1;
                            // groupIncludeChildrenStatusCascade = true;
                            valSubfix = "_y";
                            this.opts.defaultValue &&
                                this.opts.defaultValue.forEach((item) => {
                                    if (item[0] == "data_own_organization") {
                                        if (
                                            Array.isArray(item[2]) &&
                                            /_y$/g.test(item[2][0])
                                        ) {
                                            //包含子部门
                                            item[2] = item[2].map((val) => {
                                                return val.replace("_y", "");
                                            });
                                            groupIncludeChildrenStatus = 2;
                                        } else {
                                            groupIncludeChildrenStatus = 1;
                                        }
                                    }
                                });
                        } else if (field.type == "department") {
                            groupIncludeChildrenStatus = 1;
                            if (field.api_name.endsWith('__c')) {
                                valSubfix = "_z";
                            } else {
                                valSubfix = "_y";
                            }
                            this.opts.defaultValue &&
                                this.opts.defaultValue.forEach((item) => {
                                    if (item[0] == field.api_name) {
                                        if (
                                            Array.isArray(item[2]) &&
                                            (/_z$/g.test(item[2][0]) || /_y$/g.test(item[2][0]))
                                        ) {
                                            //包含子部门
                                            item[2] = item[2].map((val) => {
                                                return val.replace("_z", "");
                                            });
                                            //包含子部门
                                            item[2] = item[2].map((val) => {
                                                return val.replace("_y", "");
                                            });
                                            groupIncludeChildrenStatus = 2;
                                        } else {
                                            groupIncludeChildrenStatus = 1;
                                        }
                                    }
                                });
                        }

                        if (field.type == "department" ||  field.type == "department_many") {
                            let depIds=[];
                            this.opts.defaultValue &&
                                this.opts.defaultValue.forEach((item) => {
                                    if (item[0] == field.api_name && Array.isArray(item[2])) {
                                        depIds = item[2].slice(0);
                                    }
                                });
                            
                            depIds.forEach(function (id) {
                                if(!Fx.contacts.getCircleById(id)){
                                    Fx.contacts.getCircleByIdSync(id,function(dep){
                                        if(Fx.contacts.isStop(dep)){
                                            stopDepartmentData.push(dep)
                                        }
                                    });
                                }
                            })
                        }

                        return _.map(compares, function (compare) {
                            var child = {
                                type: me._getChildType(compare,type,field.type),
                                ftype: field.type,
                                fname: field.api_name,
                                options: me._getValueOptions(field),
                                isReInit: _.contains(employeeTypesArr, type),
                                relatedName:
                                    field.target_related_list_name || "",
                                targetApiName: field.target_api_name,
                                dimension_type: field.dimension_type || "",
                                stopDepartment: {hidden:true,data:stopDepartmentData},
                                groupIncludeChildrenStatus:
                                    groupIncludeChildrenStatus, // 如果有多个相同的规则名称，此值不准确，取的最后一个
                                groupIncludeChildrenStatusCascade:
                                    groupIncludeChildrenStatusCascade,
                                valSubfix: valSubfix,
                            };
                            if (
                                (_.contains(employeeTypesArr, type) ||
                                    type == "dimension") &&
                                _.contains([7, 8, 13, 14], compare.value)
                            ) {
                                child.isMultiple = true;
                            }
                            // 2022年8月5日 “合作伙伴”字段“属于”对比条件时，开放useMInput by:mulianju
                            if (
                                _.contains(
                                    [
                                        "object_reference",
                                        "object_reference_many",
                                    ],
                                    field.type
                                ) &&
                                field.api_name == "partner_id" &&
                                _.contains(["IN", "NIN"], compare.value1)
                            ) {
                                child.useMInput = true;
                            }
                            return {
                                label:
                                    (field.type == "date_time" &&
                                        compare.dname) ||
                                    compare.name,
                                value: compare.value,
                                child: child,
                            };
                        });
                    },

                    // crm\crm2\modules\common\fieldfilter\fieldfilter.js
                    // fields有接口'/EM1HNCRM/API/v1/object/describe/service/findDraftByApiName'返回
                    parseFields: function (fields) {
                        var cloneFields = CRM.util.deepClone(fields);
                        // xx.type == 'department_many' && xx.describe_api_name=='AccountObj'
                        for (var key in cloneFields) {
                            var field = cloneFields[key];
                            // server 暂不支持计算字段返回 日期、时间、日期时间的类型
                            // server 要走isIndex 计算公司中包含特定时间的计算
                            if (
                                (field.type == "formula" &&
                                    _.contains(
                                        ["date_time", "date", "time"],
                                        field.return_type
                                    )) ||
                                (field.type == "formula" && !field.is_index) ||
                                (field.type == "quote" &&
                                    field.is_index === false) // 把引用字段类型 && 不支持筛选的字段过滤掉
                            ) {
                                delete cloneFields[key];
                            }
                            
							if((field.type == "quote" && ['employee','employee_many','department','department_many'].indexOf(field.quote_field_type)>-1)){
									delete cloneFields[key];
							}

                            if (field.type === "formula") {
                                field.type = field.return_type || field.type;
                            }

                            if (
                                field.type === "select_one" &&
                                field.cascade_parent_api_name
                            ) {
                                delete field.cascade_parent_api_name; //共享规则不用考虑级联@邓华 modify wangj
                            }

                            if (
                                Config.filterType.indexOf(
                                    field.quote_field_type
                                ) != -1
                            ) {
                                delete cloneFields[key];
                            }
                            if (
                                Config.filterApiname.indexOf(field.api_name) !=
                                -1
                            ) {
                                delete cloneFields[key];
                            }
                            // 只有商机2.0 放出来销售流程字段
                            if (
                                data.apiname !== "NewOpportunityObj" &&
                                field.api_name === "sales_process_id"
                            ) {
                                delete cloneFields[key];
                            }
                        }
                        return cloneFields;
                    },
                    formatFields(fields) {
                        for (const api_name in fields) {
                            var field = fields[api_name];
                            field.type = field.quote_field_type || field.type;
                        }
                        return fields;
                    },
                    disabled: me.get("disabled"),
                })
            );

            filter.on("render", function () {
                me.resizedialog();
                me.$conditionGroups.find(".crm-loading").remove();
                if (
                    me.$conditionGroups.find(".filter-item-" + uuid).length == 0
                ) {
                    var $group = $(
                        '<div class="group-item"><span class="fm-error crm-ico-error" style="display:none">' +
                            $t("请完善筛选条件") +
                            '</span><div class="or-line"><span>' +
                        $t("或", {},"或") +
                            "</span></div></div>"
                    );
                    var p = $wrapper.parent();
                    $group.prepend($wrapper);
                    me.$conditionGroups.append($group);
                    if (p) p.remove();
                }

                if (!me.$fieldfilter._hasAddBtn) {
                    me.$fieldfilter._hasAddBtn = 1;
                    me.$fieldfilter.append(
                        '<span class="add-group-btn el-icon-circle-plus">' +
                            $t("添加条件") +
                            "</span>"
                    );
                }

                if (me.get("disabled")) {
                    me.$fieldfilter.append(
                        '<div class="fieldfilter-mask"></div>'
                    );
                }
            });
            filter.on("change", function () {
                this.opts.$wrapper.next().hide();
            });
            filter.on("add.item", function () {
                me.resizedialog();
            });
            filter.on("del.item", function () {
                me.resizedialog();
            });

            return filter;
        },
        addFilterGroup: function () {
            var val = this.widgets["shareobj"].getValue();
            this.widgets.filterGroup.push(
                this.createFieldfilter({
                    apiname: val,
                })
            );
        },
        delFilterGroup: function (e) {
            if (this.widgets.filterGroup.length == 1) {
                this.widgets.filterGroup[0].reset();
                return;
            }

            var uuid = $(e.target).data("uuid");
            for (var i = 0; i < this.widgets.filterGroup.length; i++) {
                var filter = this.widgets.filterGroup[i];
                if (filter.opts.uuid == uuid) {
                    filter.destroy();
                    filter.opts.$wrapper.closest(".group-item").remove();
                    this.widgets.filterGroup.splice(i, 1);
                    break;
                }
            }
        },
        onToggleWarn: function (e) {
            $(e.currentTarget)
                .closest(".update-tip")
                .toggleClass("update-tip-hover");
            this.resizedialog();
        },
        onToggleCondition: function (e) {
            var $target = $(e.currentTarget);
            if ($target.hasClass("disabled-selected")) {
                e.stopPropagation();
                return false;
            }

            var baseClassName = $(e.target).data("base");
            this.curType = baseClassName;
            baseClassName =
                baseClassName == "datasourcedep" ||
                baseClassName == "datasourcedep_org"
                    ? "datasource"
                    : baseClassName;
            this.$(".j-basefield").hide();
            this.$(".j-basedatasource").hide();
            this.$(".j-base" + baseClassName).show();
            if (baseClassName == "datasource" && this.get("type") !== "edit") {
                this.widgets.datasource && this.widgets.datasource.destroy();
                this.widgets.datasource = null;
                this.initSelectBar("datasource");
            }

            this.widgets.target && this.widgets.target.destroy();
            this.widgets.target = null;
            this.initSelectBar("target", this.get("disabled"), baseClassName);

            this.resizedialog();
        },
        onTogglePerMission: function (e) {
            var $target = $(e.currentTarget);
            if ($target.hasClass("disabled-selected")) {
                e.stopPropagation();
                return false;
            }
        },
        onRemoveErrmsg: function (e) {
            $(e.currentTarget).next().hide();
        },
        onSave: function (e) {
            var me = this;
            if (this.get("disabled")) {
                this.hide();
                return;
            }

            var $target = $(e.currentTarget);

            var targetData = me.widgets["target"].getValue();
            var includeGroupChildrenStatus = me.widgets[
                "target"
            ].getIncludeGroupChildrenStatus()
                ? 1
                : 0;

            let t = targetData.member.length + targetData.group.length +  targetData.usergroup.length +  targetData.role.length;
            if(Array.isArray(targetData.stop)) t+=targetData.stop.length;
            if(Array.isArray(targetData.stopDepartment)) t+=targetData.stopDepartment.length;
            if ( t == 0 ) {
                me.$(".j-target").next().show();
                return false;
            }


            var base_value = this.$(".j-toggle.mn-selected").data("base");
            var is_basedatasource =
                base_value === "datasource" ||
                base_value === "datasourcedep" ||
                base_value === "datasourcedep_org";
            var permissiontype = me
                .$(".j-permission.mn-selected")
                .data("permissiontype");

            if (is_basedatasource) {
                // 基于数据来源
                var sourceData = me.widgets["datasource"].getValue();
                var shareData = me.widgets["sharedata"].getValue();
                sourceData = _.extend(
                    {
                        member: [],
                        group: [],
                        usergroup: [],
                        role: [],
                    },
                    sourceData
                );

                if (base_value === "datasource") {
                    if (sourceData["stop"] && sourceData["stop"].length) {
                        sourceData["member"] = sourceData["member"].concat(
                            sourceData["stop"]
                        );
                        delete sourceData["stop"];
                    }
                }

                if (
                    sourceData.member.length +
                        sourceData.group.length +
                        sourceData.usergroup.length +
                        sourceData.role.length ==
                    0
                ) {
                    me.$(".j-datasource").next().show();
                    return false;
                } else if (shareData.length === 0) {
                    me.$sharedata.next().show();
                    return false;
                } else if (
                    _.contains(targetData.group, COMPANY_ID) &&
                    _.contains(sourceData.group, COMPANY_ID)
                ) {
                    // 被共享方和共享方不能同时为全公司
                    util.remind(
                        3,
                        $t("数据来源和数据共享不能同时为") +
                            util.getCircleById(COMPANY_ID).name
                    );
                    return false;
                }

                var sourceDeps = [],
                    sourceOrgs = [];
                sourceData.group.forEach((id) => {
                    var dep = FS.contacts.getCircleById(id);
                    if (dep) {
                        if (dep.recordType == "default__c") {
                            sourceDeps.push(id);
                        } else if (dep.recordType == "organization__c") {
                            sourceOrgs.push(id);
                        }
                    }
                });

                var targetDeps = [],
                    targetOrgs = [];
                targetData.group.forEach((id) => {
                    var dep = FS.contacts.getCircleById(id);
                    if (dep) {
                        if (dep.recordType == "default__c") {
                            targetDeps.push(id);
                        } else if (dep.recordType == "organization__c") {
                            targetOrgs.push(id);
                        }
                    }
                });

                util.FHHApi(
                    {
                        url: "/EM1HNCRM/API/v1/object/data_privilege/service/addOrUpdateShareRules",
                        data: {
                            ObjectDescribeApiNames: shareData,
                            SourceEmployeeIDs: sourceData.member,
                            SourceCircleIDs: sourceDeps,
                            SourceUserGroupIDs: sourceData.usergroup,
                            SourceRoleIDs: sourceData.role,
                            TargetEmployeeIDs: targetData.member,
                            TargetCircleIDs: targetDeps,
                            TargetUserGroupIDs: targetData.usergroup,
                            TargetRoleIDs: targetData.role,
                            PermissionType: permissiontype,
                            receiveDeptCascade: includeGroupChildrenStatus,
                            SourceOrganizationIDs: sourceOrgs,
                            TargetOrganizationIDs: targetOrgs,
                            basedType: {
                                datasource: 0,
                                datasourcedep: 1,
                                datasourcedep_org: 2,
                            }[base_value],
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                me.trigger("success", base_value);
                                util.remind(1, $t("操作成功！"));
                                me.hide();
                            }
                        },
                    },
                    {
                        submitSelector: $target,
                    }
                );
            } else {
                //基于条件
                var describeApiName = me.widgets["shareobj"].getValue();
                var ruleName = $.trim(me.$rulename.val());
                var receives = [];
                var rules = [];

                if (!ruleName) {
                    me.$rulename.next().text($t("请输入规则名称")).show();
                    return false;
                } else if (util.isContainEmojiCharacter(ruleName)) {
                    me.$rulename
                        .next()
                        .text($t("规则名称不能包含emoji表情符"))
                        .show();
                    return false;
                } else if (!describeApiName) {
                    me.$shareobj.next().show();
                    return false;
                }

                var isReturn = false;
                var filterGroup = me.widgets.filterGroup;
                filterGroup.forEach(function (item) {
                    if (item.isExitNull()) {
                        const isArea = CRM.util.getUserAttribute("crmAreaV3");
                        item.opts.$wrapper
                            .next()
                            .text(
                                isArea
                                    ? $t("请完善筛选条件,选中指定层级")
                                    : $t("请完善筛选条件")
                            )
                            .show();
                        isReturn = true;
                        return false;
                    }
                });
                if (isReturn) return;

                var rulesGroup = [],
                    index = 0;
                filterGroup.forEach(function (filter) {
                    var ruleParse = [];
                    _.each(filter.getData(), function (value) {
                        const [type, compare, fieldValue] = value;
                        index++;
                        rules.push({
                            fieldName:
                                type == "partner_id" ? type + ".name" : type,
                            fieldType: value[3],
                            // 2022年8月5日 “合作伙伴”字段“属于”对比条件时，提交时，按照";"拆分成数组 by:mulianju
                            fieldValue:
                                type == "partner_id" &&
                                _.contains(["IN", "NIN"], compare)
                                    ? fieldValue.split(";")
                                    : _.contains(["IS", "ISN"], compare)
                                    ? [""]
                                    : _.isArray(fieldValue)
                                    ? fieldValue
                                    : [fieldValue],
                            operate: value[1],
                            ruleOrder: index,
                        });
                        ruleParse.push(index);
                    });
                    rulesGroup.push("(" + ruleParse.join(" and ") + ")");
                });

                var typeMap = Config[this.config]().typeMap;
                _.each(targetData, function (value, key) {
                    receives = receives.concat(
                        _.map(value, function (item) {
                            let receiveType = _.invert(typeMap)[key];
                            let d = {
                                permission: permissiontype,
                                receiveId: item,
                                receiveType: receiveType,
                                receiveCascade: 0,
                            };
                            if (key == "group" || key=='stopDepartment') {
                                let t = util.getCircleById(item);
                                if(!t){
                                    Fx.contacts.getCircleByIdSync(item,function(dep){
                                        t = dep;
                                    });
                                }
                                if (t) {
                                    if (t.recordType == "default__c") {
                                        receiveType = 2;
                                    } else {
                                        receiveType = 8;
                                    }
                                }
                                d.receiveType = receiveType;
                                d.receiveCascade = includeGroupChildrenStatus;
                            }
                            return d;
                        })
                    );
                });

                util.FHHApi(
                    {
                        url:
                            me.get("type") === "add"
                                ? "/EM1HNCRM/API/v1/object/data_privilege/service/addFieldShare"
                                : "/EM1HNCRM/API/v1/object/data_privilege/service/updateFieldShare",
                        data: {
                            describeApiName: describeApiName,
                            receives: receives,
                            ruleCode: me.get("data").ruleCode || null,
                            ruleName: ruleName,
                            ruleParse: rulesGroup.join(" or "),
                            rules: rules,
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                me.trigger("success", base_value);
                                util.remind(1, $t("操作成功！"));
                                me.hide();
                            }
                        },
                    },
                    {
                        submitSelector: $target,
                    }
                );
            }
        },
        hide: function () {
            _.each(this.widgets, function (item) {
                item && item.destroy && item.destroy();
            });
            this.widgets = null;
            return BaseDataShare.superclass.hide.call(this);
        },
        destroy: function () {
            _.each(this.widgets, function (item) {
                item && item.destroy && item.destroy();
            });
            this.widgets = null;
            return BaseDataShare.superclass.destroy.call(this);
        },
    });
    module.exports = BaseDataShare;
});
