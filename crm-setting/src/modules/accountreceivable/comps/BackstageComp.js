
define(function (require, exports, module) {
    const DescList = {
        props: ['index', 'dataList', 'showAllIndex'],
        render(h) {
            let getTpl = (list, pIndex) => {
                let currentIndex = 0;
                let child = list.map((sen, i) => {
                    let _idx = (showIndex = true) => (showIndex && this.showAllIndex) ? [pIndex, ++currentIndex].filter((index) => index > 0).join('') + `${pIndex ? '' : '.'}` : '';
                    let _c = null;
                    let html = '';
                    let title = '';
                    if (typeof sen === 'object') {
                        const idx = _idx(sen.showIndex);
                        if (sen.list && sen.list.length) {
                            _c = getTpl(sen.list, idx);
                        }
                        if (Array.isArray(sen.title)) {
                            // 支持配置描述字体样式
                            title = sen.title.map((t, ti) => {
                                const {text, styles = ''} = t;
                                return h('span', {
                                    class: styles
                                }, text)
                            });
                            html = [`${idx}`,title, _c];
                        } else {
                            html = [`${idx}${sen.title}`, _c];
                        }

                    } else if (typeof sen === 'function') {
                        html = sen(h);
                    } else if (typeof sen === 'string') {
                        html = `${_idx()}         ${sen}`;
                    }

                    return h('li', {
                        key: i,
                        style: {
                            'padding': '0 10px',
                        }
                    }, html);
                })
                return h('ul', {
                }, child);
            };
            return getTpl(this.dataList, this.index + 1);
        }
    };

    const BackstageSettingItem = {
        template: `
            <div class="backstage_setting_item">
                <div class="backstage_setting_item__header">
                    <slot name="header">
                        <div class="backstage_setting_item__title">
                            <h2>{{title}}</h2>
                        </div>
                        <div class="backstage_setting_item__headerright">
                            <slot name="header-right" />
                        </div>
                    </slot>
                </div>
                <div class="backstage_setting_item__main">
                    <div class="backstage_setting_item__desc" v-if="describeList && describeList.length">
                        <desc-list :index="-1" :dataList="describeList" :showAllIndex="describeList.length > 1" />
                    </div>
                    <slot name="main" />
                </div>
            </div>
        `,
        components: {
            DescList,
        },
        props: {
            key: {
                type: String,
                required: true,
            },
            title: {
                type: String,
                default: ''
            },
            describeList: {
                type: Array,
                default: () => []
            }
        }
    }

    module.exports = {
        BackstageSettingItem,
        BackstageSettingDescList: DescList,
    };
});