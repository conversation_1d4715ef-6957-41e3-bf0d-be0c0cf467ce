/*
 * @Descripttion: 快捷自动化立应收
 * @Author: chaoxin
 * @Date: 2024-09-26 16:19:04
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-25 11:31:35
 */
define(function (require, exports, module) {
    const {BackstageSettingItem} = require('./BackstageComp');
    const FieldMapping = {
        template: `
            <p>
                <fx-button v-for="item in objectApiNames" :key="item" plain size="mini" @click="handleClick(item)">{{ item }}</fx-button>
                <rule-comp
                    v-if="dialogFormVisible"
                    :dialogFormVisible.sync="dialogFormVisible"
                    :data="ruleCompData"
                    :includeDetail="true"
                    targetApiName="AccountsReceivableNoteObj"
                />
            </p>
        `,
        components: {
            RuleComp: () => new Promise((resolve) => {
                require.async("vcrm/sdk", (sdk) => {
                    sdk.getComponent("backstage").then((backstage) => resolve(backstage.MultiSourceMappingFieldMappingForm))
                })
            })
        },
        data() {
            return {
                objectApiNames: ['SalesOrderObj', 'SalesContractObj'],
                ruleCompData: {
                    objectApiName: '',
                    fieldApiName: '',
                    ruleApiName: '',
                },
                dialogFormVisible: false,
            }
        },
        methods: {
            handleClick(item) {
                this.dialogFormVisible = true;
                this.ruleCompData.objectApiName = item;
            }
        }
    }
    const CreateRule = {
        template: `
            <div>
            create_rule
            </div>
        `
    }
    module.exports = (wrapper, key, values) => {
        return FxUI.create({
            wrapper,
            template: `
                <backstage-setting-item
                    key="${key}"
                    :title="title"
                    :describeList="describeList"
                >
                    <template slot="header-right">
                        <fx-switch
                            :value="value"
                            size="small"
                            true-label="0"
                            false-label="1"
                            @input="handleChange"
                        />
                    </template>
                    <template slot="main">
                        <div class="auto-create-ar-container">
                            <h2>设置字段映射</h2>
                            <field-mapping />
                        </div>
                        <div class="auto-create-ar-container">
                            <h2>创建规则</h2>
                            <create-rule />
                        </div>
                    </template>
                </backstage-setting-item>
            `,
            components: {
                BackstageSettingItem,
                FieldMapping,
                CreateRule
            },
            data() {
                return {
                    value: (values[key] ?? '0') === '1',
                    title: '快捷应收自动化规则',
                    describeList: [
                        '面向销售人员的极简应收创建，启用后，销售人员可在合同界面一键触发极简的应收创建流程。他们仅需在系统弹出的简易窗口中输入核心应收信息（如金额、分期、日期等）。系统将基于此处设置的规则完成自动化创建。'
                    ],
                }
            },
            methods: {
                handleChange(val) {
                    this.$emit('change', key, val ? '1' : '0');
                }
            }
        })
    }
})
