/*
 * @Descripttion: 
 * @Author: chaoxin
 * @Date: 2024-08-29 14:31:59
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-03-31 11:33:08
 */

define(function (require, exports, module) {
    const { Base } = require('../promotionrebate/promotionrebate');
    const config = require('./config');
    const {CONFIG_DATA, KEY_CONFIG} = config;
    const CLAIM_UPON_RECEIPT_OF_PAYMENT = 'claim_upon_receipt_of_payment';

    module.exports = Base.extend({
        getConfigData() {
            return CONFIG_DATA;
        },

        getConfigKeyData() {
            return KEY_CONFIG;
        },

        transValue(key, value, toServer) {
            if (['is_payment_enter_account_enable', 'is_payment_with_detail_enter_account_enable'].includes(key)) {
                if (toServer) {
                    return value ? '2' : '0';
                }
                return value === '2';
            } else if ([CLAIM_UPON_RECEIPT_OF_PAYMENT].includes(key)) {
                if (toServer) {
                    return value ? '1' : '0';
                }
                return value === '1';
            } else if (key === 'order_payment_mapping_rule') {
                if (toServer) {
                    return JSON.stringify(value);
                }
                return JSON.parse(value);
            } else if (key === 'order_payment_required') {
                if (toServer) {
                    return JSON.stringify({...value, status: value.status ? '1' : '0'})
                }
                const _value = JSON.parse(value);
                return {
                    ..._value,
                    status: _value.status === '1',
                };
            }

            return value;
        },

        async beforeSetConfig(key, value) {
            const param = await Base.prototype.beforeSetConfig.apply(this, arguments);
            if (key === CLAIM_UPON_RECEIPT_OF_PAYMENT) {
                param.confirmInfo = $t('crm.setting.claim_upon_receipt_of_payment.confirm');
            }
            return param;
        },

        // 自定义config请求
        setConfig(param) {
            const key = this.currentChangeKey;
            if (key === 'is_payment_pay_enable') {
                return this.setPaymentPayEnableConfig();
            } else if (key === 'is_payment_enter_account_enable') {
                return this.setPaymentEnterAccountConfig();
            } else if (key === 'is_payment_with_detail_enter_account_enable') {
                // 有回款明细的回款允许入账
                return this.setPaymentWithDetailEnterAccount();
            } else if (key === CLAIM_UPON_RECEIPT_OF_PAYMENT) {
                return this.setClaimUponReceiptOfPayment();
            }
            return Base.prototype.setConfig.apply(this, arguments);
        },

        afterSetConfig({update}) {
            const key = this.currentChangeKey;
            // 开启多来源开关，刷新多来源映射配置
            if (key === 'is_open_order_payment_multi_source') {
                update(['order_payment_mapping_rule']);
            }
        },

        // 开启线上支付能力
        async setPaymentPayEnableConfig() {
            const me = this;
            // 查询是否绑定了支付宝或微信账号
            const onlineAccountList = await me.fetch({
                url: '/EM1HNCRM/API/v1/object/payment_pay/service/query_valid_isv'
            }).then((res) => res.Value.valid_isv_list);
            if (onlineAccountList && onlineAccountList.length) {
                await me.confirm($t('确定要开启显示支付能力吗'));
                await me.commonSetConfigFetch({
                    url: '/EM1HNCRM/API/v1/object/payment_pay/service/enable_payment_pay',
                    logData: {
                        key: 'is_payment_pay_enable',
                        value: '1'
                    }
                });
            } else {
                await me.confirm(
                    $t('企业钱包暂未绑定支付宝或微信账号请先绑定后在启用'),
                    {
                        btnLabel: {
                            confirm: $t("现在绑定"),
                            cancel: $t("知道了")
                        }
                    }
                );
                // 跳转至企业钱包页面
                window.location.hash = '#app/entwallet/wallet';
                // throw new Error();
            }
        },

        // 开启入账到客户账户
        async setPaymentEnterAccountConfig() {
            const me = this;
            if (this.getKeyValues('is_customer_account_enable') !== '2') {
                throw new Error($t('该租户未启用客户账户模块请联系管理员开通'));
            }
            await me.confirm($t('启用回款入账将在对象上新增等字段并预设按钮确定要启用吗'));
            await me.commonSetConfigFetch({
                url: '/EM1HNCRM/API/v1/object/fund_account/service/payment_enter_account_init',
                logData: {
                    key: 'is_payment_enter_account_enable',
                    value: '1'
                }
            })
        },

        async setPaymentWithDetailEnterAccount() {
            const data = {
                key: 'is_payment_with_detail_enter_account_enable',
                value: '2'
            };

            await this.commonSetConfigFetch({
                url: '/EM1HNCRM/API/v1/object/crm_config/service/set_config_value',
                data,
            });
        },

        async setClaimUponReceiptOfPayment() {
            await this.fetch({
                url: '/EM1HNCRM/API/v1/object/payment_claim/service/claim_upon_receipt_of_payment',
            });
        }
    });
    module.exports.config = config;
});