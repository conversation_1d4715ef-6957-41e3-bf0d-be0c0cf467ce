<template>
    <backstage-setting-item
        key="${key}"
        :title="title"
        :describeList="describeList"
    >
        <template slot="header-right">
            <fx-switch
                :value="value"
                size="small"
                true-label="0"
                false-label="1"
                @input="handleChange"
            />
        </template>
        <template slot="main">
            <div class="auto-create-ar-container">
                <h2>设置字段映射</h2>
                <field-mapping />
            </div>
            <div class="auto-create-ar-container">
                <h2>创建规则</h2>
                <create-rule />
            </div>
        </template>
    </backstage-setting-item>
</template>

<script>
import FieldMapping from './FieldMapping'
import CreateRule from './CreateRule'
export default {
    name: 'AutoQuickCreateAr',
    components: {
        FieldMapping,
        CreateRule
    },
    data() {
        return {
            value: (values[key] ?? '0') === '1',
            title: '快捷应收自动化规则',
            describeList: [
                '面向销售人员的极简应收创建，启用后，销售人员可在合同界面一键触发极简的应收创建流程。他们仅需在系统弹出的简易窗口中输入核心应收信息（如金额、分期、日期等）。系统将基于此处设置的规则完成自动化创建。'
            ],
        }
    },
    methods: {
        handleChange(val) {
            this.$emit('change', key, val ? '1' : '0');
        }
    }
}
</script>
