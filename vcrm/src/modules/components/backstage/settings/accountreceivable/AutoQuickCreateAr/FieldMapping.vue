<template>
    <p>
        <fx-button v-for="item in objectApiNames" :key="item" plain size="mini" @click="handleClick(item)">{{ item }}</fx-button>
        <rule-comp
            v-if="dialogFormVisible"
            :dialogFormVisible.sync="dialogFormVisible"
            :data="ruleCompData"
            :includeDetail="true"
            targetApiName="AccountsReceivableNoteObj"
        />
    </p>
</template>


<script>
import {MultiSourceMappingFieldMappingForm} from '../../components'
export default {
    name: 'FieldMapping',
    components: {
        RuleComp: MultiSourceMappingFieldMappingForm
    },
    data() {
        return {
            objectApiNames: ['SalesOrderObj', 'SalesContractObj'],
            ruleCompData: {
                objectApiName: '',
                fieldApiName: '',
                ruleApiName: '',
            },
            dialogFormVisible: false,
        }
    },
    methods: {
        handleClick(item) {
            this.dialogFormVisible = true;
            this.ruleCompData.objectApiName = item;
        }
    }
}
</script>