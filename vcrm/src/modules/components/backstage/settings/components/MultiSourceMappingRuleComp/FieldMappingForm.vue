<template>
    <fx-dialog
        :title="title"
        ref="rDialog"
        :visible="dialogFormVisible"
        @close="() => $emit('update:dialogFormVisible', false)"
        custom-class="field-mapping-form order_payment_mapping_rule"
    >
        <div class="content crm-scroll" v-if="!initLoading">
            <!-- 主对象 -->
            <div class="field-mapping-item-title">
                <h2>{{ sourceDisplayName }}</h2>
                <span></span>
                <h2>{{ targetDisplayName }}</h2>
            </div>
            <field-mapping-item
                ref="rFieldMappingItem"
                v-for="(item, i) in fieldsMappings"
                :key="i"
                :selectedSourceFiled="selectedSourceFiled"
                :selectedTargetFiled="selectedTargetFiled"
                :sourceOptions="sourceOptions"
                :targetOptions="targetOptions"
                :data="item"
                :readOnly="isReadOnly(item)"
                @update:data="(data) => handleDataChangeItem(i, data)"
                @del="handleDelItem(i)"
            />
            <span class="add-line" @click="handleAddLine"><span class="fx-icon-add"></span><span>{{ $t('添加') }}</span></span>
            <!-- 从对象、类似主对象，需要抽象 -->
             
        </div>
        <div slot="footer" class="dialog-footer">
            <fx-button type="primary" @click="handleAddConfirm" size="small" :loading="confirmLoading">{{$t('确 定')}}</fx-button>
            <fx-button @click="handleCancel" size="small">{{$t('取 消')}}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
import FieldMappingItem from './FieldMappingItem'
import {apiRequestCreateUpdateRule, apiRequestFieldOptions, apiRequestGetMappingDetail, parseFieldOptions} from './utils/index'

export default {
    name: 'FieldMappingForm',
    components: {
        FieldMappingItem
    },
    props: {
        // 是否主从一起新建
        includeDetail: {
            type: Boolean,
            default: false
        },
        dialogFormVisible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            required: true
        },
        ruleApiName: {
            type: String,
        },
        sourceApiName: {
            type: String,
        },
        targetApiName: {
            type: String,
        },
        confirmLoading: {
            type: Boolean,
            default: false
        },
        readOnlyFields: {
            type: Array,
            default: () => []
        },
        customConfig: {
            type: Object,
            default: void 0
        },
        customFieldsProcess: {
            type: Function,
            default: null
        }
    },
    data() {
        return {
            initLoading: false,
            title: $t('crm.setting.order_payment_mapping_rule.field_mapping_title'),
            fieldsMappings: [],
            sourceOptions: [],
            targetOptions: [],
            sourceDisplayName: '',
            targetDisplayName: '',
            subFieldsData: [
                {
                    
                }
            ]
        }
    },
    created() {
        this.initFetch();
    },
    computed: {
        cSourceApiName() {
            return this.data?.objectApiName || this.sourceApiName;
        },
        selectedSourceFiled() {
            return this.fieldsMappings.map(item => item.source_field_api_name);
        },
        selectedTargetFiled() {
            return this.fieldsMappings.map(item => item.target_field_api_name);
        }
    },
    methods: {
        isReadOnly(item) {
            return this.readOnlyFields.includes(item.source_field_api_name);
        },
        async initFetch() {
            this.initLoading = true;
            
            await Promise.all([
                this.fetchOptions(),
                this.fetchMappingDetail(),
            ]);
            this.initLoading = false
        },
        async fetchOptions() {
            if (this.cSourceApiName !== this.targetApiName) {
                return Promise.all([
                    this.fetchSourceOptions(),
                    this.fetchTargetOptions(),
                ]);
            }
            // 如果sourceApiName和targetApiName相同，则只请求一次
            await this.fetchSourceOptions();
            await this.fetchTargetOptions();
        },
        async fetchSourceOptions() {
            const {fields, display_name} = await apiRequestFieldOptions(this.cSourceApiName, {
                include_detail_describe: this.includeDetail
            });
            this.sourceDisplayName = display_name;
            let options = parseFieldOptions(fields, this.cSourceApiName, 'source', this.customConfig);
            if (this.customFieldsProcess) {
                options = this.customFieldsProcess({
                    options,
                    fields,
                    type: 'source',
                    apiName: this.cSourceApiName,
                    customConfig: this.customConfig
                });
            }
            this.sourceOptions = options;
        },
        async fetchTargetOptions() {
            const {fields, display_name} = await apiRequestFieldOptions(this.targetApiName, {
                include_detail_describe: this.includeDetail
            });
            this.targetDisplayName = display_name;
            let options = parseFieldOptions(fields, this.targetApiName, 'target', this.customConfig);
            if (this.customFieldsProcess) {
                options = this.customFieldsProcess({
                    options,
                    fields,
                    type: 'target',
                    apiName: this.targetApiName,
                    customConfig: this.customConfig
                });
            }
            this.targetOptions = options;
        },
        async fetchMappingDetail() {
            if (!this.data.ruleApiName) {
                this.fieldsMappings.push({});
                return;
            }
            const {ruleApiName, masterRuleApiName} = this.data;
            // 从对象的映射存在主对象的映射里，合同层级结构配置
            const rst = await apiRequestGetMappingDetail(masterRuleApiName || ruleApiName);
            this.fieldsMappings = rst.ruleList.find(rItem => rItem.rule_api_name === ruleApiName)?.field_mapping || [];
            if (this.fieldsMappings.length === 0) {
                this.handleAddLine();
            }
        },
        validate() {
            const valid = (this.$refs.rFieldMappingItem || []).every(item => item.validate());
            let errorMsg = '';
            if (!valid) {
                errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_fill_source_and_target_field');
            } else if (this.fieldsMappings.length === 0) {
                errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_add_mapping_relation');
            }
            if (errorMsg) {
                this.$message({
                    message: errorMsg,
                    type: 'error'
                })
                return false;
            }
            return true;
        },
        handleAddConfirm() {
            if (!this.validate()) {
                return;
            }
            this.$emit('confirm', {
                describe_api_name: this.cSourceApiName,
                rule_list: [{
                    source_api_name: this.cSourceApiName,
                    target_api_name: this.targetApiName,
                    field_api_name: this.data.fieldApiName,
                    field_mapping: this.fieldsMappings
                }]
            }, {ruleData: this.data});
        },
        handleCancel() {
            this.$emit('update:dialogFormVisible', false);
        },
        handleAddLine() {
            this.fieldsMappings.push({});
        },
        handleDelItem(index) {
            this.fieldsMappings.splice(index, 1);
        },
        handleDataChangeItem(index, data) {
            this.$set(this.fieldsMappings, index, data);
        },
    }
}
</script>

<style lang="less">
.order_payment_mapping_rule.field-mapping-form {
    .content {
        display: flex;
        flex-direction: column;
        gap: 14px;
        height: 400px;

        .field-mapping-item-title {
            display: flex;
            gap: 12px;
            align-items: center;

            h2 {
                // font-size: 12px;
                line-height: 18px;
                flex-basis: 240px;
                color: var(--color-neutrals19);
            }

            span {
                flex-basis: 20px;
            }
        }
    }
    .add-line {
        align-self: flex-start;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: var(--color-primary06);
        cursor: pointer;

        .fx-icon-add {
            font-size: 16px;
        }
    }
}
</style>