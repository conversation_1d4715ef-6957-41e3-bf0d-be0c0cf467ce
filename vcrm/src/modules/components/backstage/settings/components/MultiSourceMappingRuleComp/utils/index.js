/*
 * @Descripttion: 
 * @Author: chaoxin
 * @Date: 2024-09-04 10:14:52
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-25 11:15:21
 */
export {parseFieldOptions, requireSyncFieldConfig} from './field'
const CACHE_OBJ_FIELD_MAP = new Map();
import crmRequire from '@common/require';

export const clearFieldCache = () => {
    CACHE_OBJ_FIELD_MAP.clear();
}

export const apiRequestFieldOptions = (apiname, opts = {}) => {
    if (!apiname) return Promise.resolve([]);
    if (CACHE_OBJ_FIELD_MAP.has(apiname)) {
        return Promise.resolve(CACHE_OBJ_FIELD_MAP.get(apiname));
    }
    return CRM.util.getDescribeLayout({
        apiname,
        include_layout: false,
        include_detail_describe: false,
        ...opts
    }).then((res) => {
        CACHE_OBJ_FIELD_MAP.set(apiname, res.objectDescribe);
        return res.objectDescribe;
    });
}

/** 获取映射详情 */
export const apiRequestGetMappingDetail = (ruleApiName) => {
    return CRM.util.ajax_base('/EM1HNCRM/API/v1/object/object_mapping/service/findByApiName', {rule_api_name: ruleApiName}, null)
}
