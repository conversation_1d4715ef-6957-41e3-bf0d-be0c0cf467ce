const path = require('path');
// 112
const accountObj = {
    // activity
    'zhenju0111': ['***********', 'zzj123456'], // 新ui ai
    'fsceshi082': ['***********', '1234qwer'],
    '93747': ['a01', '12345@qwert'],


    // 开票
    '81292': ['***********'], // 开票模式3 --> 开票模式2开票小计支持编辑
    '82681': [], // 开票3 多单位,价格政策,流程布局适配
    '81366': ['a01', 'aa123456'], // 开票3 多币种，合同层级
    '76200': ['***********'], // 开票2 多币种
    '79460': [], // 开票模式1
    //     测开票订单解耦的企业
    '76201': ['***********'], // 模式1 解耦
    '90893': ['***********'], //开票模式2 订单解耦，周期性立应收，固定搭配
    '90454': ['***********'], //开票模式3 订单解耦
    // 76201  ***********  1234qwer    模式一
    // 90893   ***********   1234qwer  模式二
    // 90454   ***********   1234qwer  模式三
    '84393': ['***********'], // 开票 多来源

    '93550': ['a01', 'A1234qwer'],

    // 合同
    '93009': ['18618326986'],

    // 返利
    '86263': [], // 返利使用适配合同
    '89308': ['15300306723', '1234qwer'], // 返利 返利维度
    '89206': ['15300306723'], // 885 返利
    // '90438': ['15300306723'], // 900 返利维度,
    '90438': ['***********', 'A1234qwer'], // 分类业务字段，商品，价格政策，周期性产品

    // 固定搭配
    '86026': [], // 商品+固定搭配+多单位
    '84946': ['***********'], // 固定搭配 ?? 无价格政策
    '84395': ['***********'], // 固定搭配，回款多来源
    '88506': ['***********'], // 固定搭配适配bomcore --- 旧
    '86133': ['***********'], // 固定搭配适配bomcore 多单位、商品 895，excel导入，规格样式
    '85768': [], // 固定搭配适配bomcore
    '82909': [], // bom 分类属性 新ui
    'zhenju0111': ['***********', 'zzj123456'], // 新ui ai

    '83180': ['***********'], // bom fsceshi082,促销活动
    '82769': [], // bom excel导入,老回款

    // 价格政策
    '84475': ['***********'], // 价格政策相关
    '82093': ['afs', 'a123456'], // 银鹭


    // 应收核销
    '90893': ['***********'], //开票模式2 订单解耦，周期性立应收，固定搭配
    '81147': ['13537676088', '123456qwe'], // 应收管理
    '89150': ['11188889999', 'aa123456'], // 核销单,回款负数
    // '89150': ['yxy', 'aa123456'], // 核销单,回款负数
    '83050': ['11255555555', '123456qwe'], // 二维码核销
    '84148': ['11266660216', '123456qwe'], // 老回款
    '93079': ['***********'],

    // 商品
    '81080': ['11111111122'],
    // 产品分类
    '90242': ['a01', '1234qwer!'], // 分类对象化 属性，甄零合同对接，合同层级，合同履约
    '90893': ['***********', 'A1234qwer'], // 产品分类ui事件, 应收，应收快捷，结算单
    // '90893': ['a02'], // 应收快捷创建权限


    '89183': ['***********'], //
    '83382': [],
    '84126': [],
    '83120': [],
    '85491': [],
    '85781': ['11188889999', 'aa123456'],
    '85777': ['11188889999', 'aa123456'],
    'fsceshi003': ['shiwj', '123qwe'],
    // displayname 84796 83382
    // 价目表未解耦 83382
    // 产品分类对象 71568
    '71568': ['a10'],
    '88436': ['15823055360', 'yxj123456'],
    // 82769 订单地址组件优化，合同约束
    // 86238 销售合同约束
    // 85380 价格键盘，新ui
    // 79533 从对象切换价目表
    // 82909 bom + 临时子件 + 常规模式
    '82312': ['13737754031', 'aa123456'], // 深研 发货单
    //85216 85276
    '76173': ['***********'],
    // '86240': ['a02'],
    '89199': ['a01', '1234QWER'],
    '89273': ['13520224582', 'aa123456'],
    '84844': ['a02', '1234qwer'],
    // '88985': [], 
    '90057': ['18800411514', '1234qwer'],
    '85345': [], // 价目表改造
    '78437': ['13537676089', '123456qwe'], // 商城分类
    '83050': ['11255555555', '123456qwe'], // 商城分类
    '90153': ['13612885686', '123456qwe'], // 多级订货 品牌商
    '90158': ['13612885686', '123456qwe'], //  一批商
    // 'fsceshi082': ['a01', '1234qwer'], // bom core 商品
    '90265': ['15330060301', 'zhy123456'],
    '81964': [], // 产品 字段ui事件
    '89707': ['15300306723'],
    '90899': [],

    '93087': ['11220250208', '93087qwe'], // 960商城类目

    // 90899  a01 1234qwer
    '90899': ['a01', '1234qwer'], // 激励政策
}

// 线上
const fxiaoke = {
    '757908_sandbox': ['18898548242', '878209800jyz'],
    'fktest2851': ['a01', 'A1234qwer'], // 返利,返利政策,销售合同
    'fktest2853': [], // 返利
    'pricepolicy2021': ['a01', '1234qwer'],
    'fktest085': ['a01', '1234qwer'], // 
    'fktest3488': ['a01', '1234qwer'], // 固定搭配多单位
    'fktest2992': ['a01', '1234qwer'], // 固定搭配商品
    'fktest2389': ['a01', '1234qwer'], // bom
    'fktest084': ['a01', '1234qwer'],
    'fktest1837': ['a01', 'A1234qwer'], // 价格政策卡顿
    'fktest1119': ['a01', 'A1234qwer'], // 开票, 模式3 回款多来源
    // 'fktest1118': ['a01', '12345qwert'], // 开票 模式3
    '683017': ['a01', '12345qwert'], // 开票,
    'fktest1540': ['a01', '12345qwert'], // 产品分类,鼎桥分类
    'fktest1335': ['a02', '12345qwert'], // bom 大数据
    'fktest1199': ['a01', '12345qwert'], // bom
    'fktest3469': ['18118720760', 'test123456'], // 深研退货单
    'fktest087': ['lihh', 'A1234@qwer'], // 深研退货单 推拉单
    'fktest1396': ['13537676089', 'qwe123321'], // 回款 核销
    'fktest1400': ['13537676089', '123456zxc'],
    'fktest2928': ['18612590452'],
    'fktest086': ['xt', '1234qwer'],
    '778346': ['13520224582', 'aa123456'],
    '565245': ['liaoxf', '1234qwer'], // 商品
    '67000074_sandbox': ['18301133670', '1234qwer'], // bom core
    'fktest2361': ['18301133670', '1234qwer'], // 订单地址
    'fktest3580': ['***********'], // 固定搭配
    '40163028_sandbox': ['13716368080', 'mn888888'], // 蒙牛应收沙盒
    '721788_sandbox': ['***********', 'a1234567x'],
    'fktest084': [],
    'fktest5331': ['13537676089', 'Fktest5331'], // 多级订货 品牌商
    'fktest6012': ['13537676089', 'Fktest6012'], // 多级订货 品牌商
    'fktest4591': ['a01', 'A1234qwer'], // 激励政策，商品，产品分类
    'fktest5516': ['a01', '12345@qwert'], // 客户画像

    // 线上排查问题
    'hiteeth': ['18510003500', 'abcd1234'],
    'ennpower': ['13671816393', 'xodl8888'],
    'fktest2388': [],
    'hksp8888': ['18911396026', 'Aa1234567890'], // 返利 惠康
    'jilang2023': ['18850417410', 'jl123456'], // 
    'ylspjt': ['***********', 'a1234567x'], // 银鹭
    'njrljc': ['13851625187', '6411314ai'],
    'jmk8888': ['18906570963', 'JMK88291158'], 
    'demo221': ['***********', 'aaa123456'],
    'sparkx': ['***********', 'yy123456'],
    '721788_sandbox': ['***********', 'a1234567x'], // 银鹭沙盒
    'deligao2023': ['***********', 'abc12345678'], // 得力
    '801774_sandbox': ['***********', 'hanhan123'],
    'hngxgg': ['***********', 'fxxk2021'],
    '798779_sandbox': ['***********', '@Cjs117898949'],
    'draeger': ['***********', 'deg123456'],
    'xgnsjt2025': ['***********', 'ns123qwe'],
}

/**
 * @typedef {keyof typeof accountObj | keyof typeof fxiaoke} MyVar
 */

/** @type {MyVar} */
const upstreamEa = '90893';
const user112 = accountObj[upstreamEa];
const userfxiaoke = fxiaoke[upstreamEa];
let [upstreamUsername = 'a01', upstreamPassword = '1234qwer', env = (userfxiaoke ? 1 : 0) ] =  user112 || userfxiaoke || [];

module.exports = {
    account: {
        upstreamEa,
        upstreamUsername,
        upstreamPassword,
    },
    env: env === 1 ? 'fxiaoke' : 'ceshi112',
    // env: 'fxiaoke',
    developDownstream: false,
    // appId: 'FSAID_11490c84',
    appId: 'FSAID_11490f80',
    macOS: true,
    watchFile: false,
    cache112: false,
    project: {
        'vcrm-dist': {
            enable: true,
            onlinePath: '/vcrm-dist/',
            localPath: path.join(__dirname, '../vcrm/dev')
        },
        'crm-dist': {
            enable: true,
            onlinePath: '/crm-dist/',
            localPath: path.join(__dirname, '../crm/crm2')
        },
        // 'base-biz': {
        //     enable: true,
        //     onlinePath: '/base-biz/',
        //     localPath: path.join(__dirname, '../../fs-others/base-biz/dev')
        // },
        'crm-setting': {
            enable: true,
            onlinePath: '/crm-setting/',
            localPath: path.join(__dirname, '../crm-setting/dist')
        },
        // 'workflow': {
        //     enable: true,
        //     onlinePath: '/paas/workflow/',
        //     localPath: path.join(__dirname, '../fs/workflow/dev')
        // },
        // 'fx-libs': {
        //     enable: true,
        //     onlinePath: '/fx-libs/',
        //     localPath: path.join(__dirname, '../fs/fx-libs/release')
        // }
        // 'npaas-dist': {
        //     enable: false,
        //     onlinePath: '/npaas-dist/',
        //     localPath: path.join(__dirname, '../fs-other/npaas/dist')
        // },
        // 'base-dist': {
        //     enable: false,
        //     onlinePath: '/base-dist/',
        //     localPath: path.join(__dirname, '../fs/base/base/base-dist')
        // },
        'paas/vui': {
            enable: true,
            onlinePath: '/paas/vui/',
            localPath: path.join(__dirname, '../../fs-others/vui/dev')
        },
        // 'fx': {
        //     enable: true,
        //     onlinePath: '/fx/',
        //     localPath: path.join(__dirname, '../fs/fx/dist')
        // }

    },
    // 是否是无租户，无租户企业用不了mock登录，只能登录企业后，把需要的cookie粘贴过来
    // 无需使用时，将其注释或者guid为空。下游使用：guid、ERInfo、CRInfo、ERUpstreamEa
    // 上游使用：guid、fs_token、FSAuthX、FSAuthXC
    staticCookie: {
        // guid: 'a3ecdc50-454f-44cd-e865-e1982bb7088a',
        // fs_token: 'DMKoE3TcE64jCpDZCYqqD3bcBJbcE3KjD3KrCJ4tOZaqDZTZ',
        // FSAuthX: '0G60KFrZ1mC0001Inx6y485jCac9439kOwB1ZniNkc9oFskSeXFG3Na9qKEvRy7eZhRKrXP3SVL3ytVCnKuzqECEbFeLeLJISVhsu82kJB6ltexhvMzAjWkSmG9OeT6xOTMIJ3oE7VR1RU4sbifmAub0IhyGKeXwcDz4qPJZr8WsdzMqDEE2goCy7TAqZryHu6GHRC4mTE45XBBeCAtcnXGlVoxj17Lpiyty4oGGDmPfnjkMKgKEIMZ3gdq3',
        // FSAuthXC: '0G60KFrZ1mC0001Inx6y485jCac9439kOwB1ZniNkc9oFskSeXFG3Na9qKEvRy7eZhRKrXP3SVL3ytVCnKuzqECEbFeLeLJISVhsu82kJB6ltexhvMzAjWkSmG9OeT6xOTMIJ3oE7VR1RU4sbifmAub0IhyGKeXwcDz4qPJZr8WsdzMqDEE2goCy7TAqZryHu6GHRC4mTE45XBBeCAtcnXGlVoxj17Lpiyty4oGGDmPfnjkMKgKEIMZ3gdq3',
    //     ERInfo: 'er_9f68345fa7c14dcda57f46fa51d6cb23_0317141750_475601',
    //     CRInfo: 'er_9f68345fa7c14dcda57f46fa51d6cb23_0317141750_475601',
    //     ERUpstreamEa: '475601',
    },
};
